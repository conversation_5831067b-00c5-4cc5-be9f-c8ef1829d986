"use client"
import { motion } from "framer-motion"
import { LaptopDashboard } from "./laptop-dashboard"
import { AnimatedErpShowcase } from "./animated-erp-showcase"

export function HeroAnimation() {
  return (
    <div className="relative">
      {/* Background Elements */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1.5, delay: 0.5 }}
        className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-purple-50/30 to-pink-50/50 rounded-3xl"
      />
      
      {/* Floating Orbs */}
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 0.6, scale: 1 }}
        transition={{ duration: 2, delay: 1 }}
        className="absolute top-10 right-20 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-2xl"
      />
      
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 0.4, scale: 1 }}
        transition={{ duration: 2, delay: 1.5 }}
        className="absolute bottom-20 left-20 w-24 h-24 bg-gradient-to-r from-pink-400/20 to-orange-400/20 rounded-full blur-xl"
      />

      {/* Main Laptop Dashboard */}
      <div className="relative z-10 py-12">
        <AnimatedErpShowcase />
      </div>
    </div>
  )
}

