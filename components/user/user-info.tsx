"use client";

import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { ChevronUp, ChevronDown, MoreHorizontal, Loader2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuGroup,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { UserAvatar } from "@/components/ui/user-avatar";
import { useUserInfo, UserInfoConfig } from "@/hooks/use-user-info";
import {
  SIDEBAR_USER_ACTIONS,
  HEADER_USER_ACTIONS,
  filterActionsByRole,
  addSeparators,
  getActionById
} from "@/lib/config/user-actions-config";

export interface UserInfoProps {
  variant?: 'sidebar' | 'header' | 'compact' | 'full';
  size?: 'sm' | 'md' | 'lg';
  layout?: 'horizontal' | 'vertical';
  showDropdown?: boolean;
  showStatus?: boolean;
  showRole?: boolean;
  showLastActive?: boolean;
  className?: string;
  dropdownAlign?: 'start' | 'end' | 'center';
  dropdownSide?: 'top' | 'bottom' | 'left' | 'right';
  config?: UserInfoConfig;
  onUserClick?: () => void;
  customActions?: any[];
}

export function UserInfo({
  variant = 'sidebar',
  size = 'md',
  layout = 'horizontal',
  showDropdown = true,
  showStatus = false,
  showRole = true,
  showLastActive = false,
  className,
  dropdownAlign = 'end',
  dropdownSide = 'bottom',
  config,
  onUserClick,
  customActions = [],
}: UserInfoProps) {
  const [isOpen, setIsOpen] = useState(false);

  const { 
    user, 
    isLoading, 
    error, 
    userActions, 
    userDisplayInfo, 
    handleAction 
  } = useUserInfo({
    config: {
      showStatus,
      showRole,
      showLastActive,
      avatarSize: size,
      layout,
      customActions,
      ...config
    }
  });

  // Get actions based on variant
  const getActionsForVariant = () => {
    let actions = variant === 'header' ? HEADER_USER_ACTIONS : SIDEBAR_USER_ACTIONS;
    
    if (user?.role) {
      actions = filterActionsByRole(actions, user.role);
    }
    
    return addSeparators(actions);
  };

  const actions = getActionsForVariant();

  // Loading state
  if (isLoading) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Skeleton className="h-8 w-8 rounded-full" />
        {variant !== 'compact' && (
          <div className="flex flex-col gap-1">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-3 w-16" />
          </div>
        )}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn("flex items-center gap-2 text-destructive", className)}>
        <div className="h-8 w-8 rounded-full bg-destructive/10 flex items-center justify-center">
          !
        </div>
        {variant !== 'compact' && (
          <span className="text-sm">Error loading user</span>
        )}
      </div>
    );
  }

  const avatarSize = size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'md';

  const userContent = (
    <div className={cn(
      "flex items-center gap-2 min-w-0",
      layout === 'vertical' && "flex-col items-start gap-1"
    )}>
      <UserAvatar
        src={userDisplayInfo.avatarUrl}
        name={userDisplayInfo.name}
        initials={userDisplayInfo.initials}
        size={avatarSize}
        status={showStatus ? userDisplayInfo.status as any : undefined}
        showStatus={showStatus}
        role={showRole ? userDisplayInfo.role : undefined}
        showRole={showRole && variant === 'full'}
        className="flex-shrink-0"
      />
      
      {variant !== 'compact' && (
        <div className={cn(
          "flex flex-col min-w-0",
          layout === 'vertical' && "items-center text-center"
        )}>
          <span className="font-semibold text-sm truncate max-w-[120px]">
            {userDisplayInfo.name}
          </span>
          
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            {showRole && (
              <Badge 
                variant="secondary" 
                className={cn("text-xs px-1 py-0 h-auto", userDisplayInfo.roleColor)}
              >
                {userDisplayInfo.role}
              </Badge>
            )}
            
            {showStatus && (
              <Badge 
                variant="outline" 
                className={cn("text-xs px-1 py-0 h-auto", userDisplayInfo.statusColor)}
              >
                {userDisplayInfo.status}
              </Badge>
            )}
          </div>
          
          {showLastActive && userDisplayInfo.lastActive && (
            <span className="text-xs text-muted-foreground truncate">
              {userDisplayInfo.lastActive}
            </span>
          )}
        </div>
      )}
    </div>
  );

  if (!showDropdown) {
    return (
      <div 
        className={cn("cursor-pointer", className)}
        onClick={onUserClick}
      >
        {userContent}
      </div>
    );
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className={cn(
            "h-auto p-2 justify-start hover:bg-sidebar-accent hover:text-sidebar-accent-foreground data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",
            variant === 'header' && "h-8 px-2",
            variant === 'compact' && "h-8 w-8 p-0",
            className
          )}
        >
          {userContent}
          
          {variant !== 'compact' && (
            <ChevronUp className={cn(
              "ml-auto h-4 w-4 transition-transform duration-200",
              isOpen && "rotate-180"
            )} />
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent
        className="w-56"
        align={dropdownAlign}
        side={dropdownSide}
        sideOffset={4}
      >
        {/* User Info Header */}
        <DropdownMenuLabel className="p-3">
          <div className="flex items-center gap-3">
            <UserAvatar
              src={userDisplayInfo.avatarUrl}
              name={userDisplayInfo.name}
              initials={userDisplayInfo.initials}
              size="md"
              status={showStatus ? userDisplayInfo.status as any : undefined}
              showStatus={showStatus}
            />
            <div className="flex flex-col min-w-0">
              <span className="font-medium text-sm truncate">
                {userDisplayInfo.name}
              </span>
              <span className="text-xs text-muted-foreground truncate">
                {userDisplayInfo.email}
              </span>
              {showRole && (
                <Badge 
                  variant="secondary" 
                  className={cn("text-xs px-1 py-0 h-auto mt-1 w-fit", userDisplayInfo.roleColor)}
                >
                  {userDisplayInfo.role}
                </Badge>
              )}
            </div>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        {/* User Actions */}
        <DropdownMenuGroup>
          {actions.map((action) => {
            if (action.separator) {
              return <DropdownMenuSeparator key={action.id} />;
            }

            const IconComponent = action.icon;
            
            return (
              <DropdownMenuItem
                key={action.id}
                onClick={() => handleAction(action.id)}
                disabled={action.disabled}
                className={cn(
                  "flex items-center gap-2 cursor-pointer",
                  action.variant === 'destructive' && "text-destructive focus:text-destructive"
                )}
              >
                {IconComponent && <IconComponent className="h-4 w-4" />}
                <span className="flex-1">{action.label}</span>
                
                {action.badge && (
                  <Badge variant="secondary" className="text-xs ml-auto">
                    {action.badge}
                  </Badge>
                )}
                
                {action.shortcut && (
                  <span className="text-xs text-muted-foreground ml-auto">
                    {action.shortcut}
                  </span>
                )}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Compact variant for headers
export function UserInfoCompact(props: Omit<UserInfoProps, 'variant'>) {
  return <UserInfo {...props} variant="compact" />;
}

// Header variant
export function UserInfoHeader(props: Omit<UserInfoProps, 'variant'>) {
  return <UserInfo {...props} variant="header" />;
}

// Sidebar variant (default)
export function UserInfoSidebar(props: Omit<UserInfoProps, 'variant'>) {
  return <UserInfo {...props} variant="sidebar" />;
}

// Full variant with all details
export function UserInfoFull(props: Omit<UserInfoProps, 'variant'>) {
  return <UserInfo {...props} variant="full" showStatus showRole showLastActive />;
}
