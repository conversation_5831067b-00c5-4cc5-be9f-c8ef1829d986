"use client";

import React, { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { 
  UserInfo,
  UserInfoSidebar, 
  UserInfoHeader, 
  UserInfoCompact, 
  UserInfoFull 
} from "@/components/user/user-info";
import { UserAvatar, UserAvatarGroup } from "@/components/ui/user-avatar";
import { 
  Users, 
  Settings, 
  Palette, 
  Monitor, 
  Smartphone, 
  Tablet,
  Star,
  Heart,
  Zap
} from "lucide-react";

/**
 * Demo component to showcase the Modular User Info System
 * This component demonstrates all variants, configurations, and features
 */
export function UserInfoDemo() {
  const [selectedVariant, setSelectedVariant] = useState<'sidebar' | 'header' | 'compact' | 'full'>('sidebar');
  const [selectedSize, setSelectedSize] = useState<'sm' | 'md' | 'lg'>('md');
  const [selectedLayout, setSelectedLayout] = useState<'horizontal' | 'vertical'>('horizontal');
  const [showStatus, setShowStatus] = useState(false);
  const [showRole, setShowRole] = useState(true);
  const [showLastActive, setShowLastActive] = useState(false);
  const [showDropdown, setShowDropdown] = useState(true);

  const customActions = [
    {
      id: 'favorite',
      label: 'Favorites',
      icon: Star,
      href: '/favorites',
      description: 'View your favorite items',
      badge: 5,
      category: 'custom'
    },
    {
      id: 'wishlist',
      label: 'Wishlist',
      icon: Heart,
      href: '/wishlist',
      description: 'Manage your wishlist',
      category: 'custom'
    }
  ];

  const variants = [
    { value: 'sidebar', label: 'Sidebar', description: 'Full sidebar layout with dropdown' },
    { value: 'header', label: 'Header', description: 'Compact header layout' },
    { value: 'compact', label: 'Compact', description: 'Avatar-only minimal display' },
    { value: 'full', label: 'Full', description: 'Complete user information display' }
  ];

  const sizes = [
    { value: 'sm', label: 'Small', description: '32px avatar, compact text' },
    { value: 'md', label: 'Medium', description: '40px avatar, standard text' },
    { value: 'lg', label: 'Large', description: '48px avatar, larger text' }
  ];

  const layouts = [
    { value: 'horizontal', label: 'Horizontal', description: 'Avatar beside text' },
    { value: 'vertical', label: 'Vertical', description: 'Avatar above text' }
  ];

  const mockUsers = [
    { name: 'John Doe', src: undefined },
    { name: 'Jane Smith', src: undefined },
    { name: 'Bob Johnson', src: undefined },
    { name: 'Alice Brown', src: undefined },
    { name: 'Charlie Wilson', src: undefined },
  ];

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-6 w-6 text-primary" />
            Modular User Info Component Demo
          </CardTitle>
          <p className="text-muted-foreground">
            Test and explore all variants and configurations of the user info system.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Configuration Panel */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-muted rounded-lg">
            <div className="space-y-2">
              <Label>Variant</Label>
              <Select value={selectedVariant} onValueChange={(value: any) => setSelectedVariant(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {variants.map((variant) => (
                    <SelectItem key={variant.value} value={variant.value}>
                      <div className="flex flex-col">
                        <span className="font-medium">{variant.label}</span>
                        <span className="text-xs text-muted-foreground">{variant.description}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Size</Label>
              <Select value={selectedSize} onValueChange={(value: any) => setSelectedSize(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {sizes.map((size) => (
                    <SelectItem key={size.value} value={size.value}>
                      <div className="flex flex-col">
                        <span className="font-medium">{size.label}</span>
                        <span className="text-xs text-muted-foreground">{size.description}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Layout</Label>
              <Select value={selectedLayout} onValueChange={(value: any) => setSelectedLayout(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {layouts.map((layout) => (
                    <SelectItem key={layout.value} value={layout.value}>
                      <div className="flex flex-col">
                        <span className="font-medium">{layout.label}</span>
                        <span className="text-xs text-muted-foreground">{layout.description}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <Label>Options</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch id="status" checked={showStatus} onCheckedChange={setShowStatus} />
                  <Label htmlFor="status" className="text-sm">Show Status</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="role" checked={showRole} onCheckedChange={setShowRole} />
                  <Label htmlFor="role" className="text-sm">Show Role</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="lastActive" checked={showLastActive} onCheckedChange={setShowLastActive} />
                  <Label htmlFor="lastActive" className="text-sm">Last Active</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="dropdown" checked={showDropdown} onCheckedChange={setShowDropdown} />
                  <Label htmlFor="dropdown" className="text-sm">Dropdown</Label>
                </div>
              </div>
            </div>
          </div>

          {/* Live Preview */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Live Preview</h3>
            <div className="p-6 border rounded-lg bg-background">
              <div className="flex items-center justify-center min-h-[100px]">
                <UserInfo
                  variant={selectedVariant}
                  size={selectedSize}
                  layout={selectedLayout}
                  showStatus={showStatus}
                  showRole={showRole}
                  showLastActive={showLastActive}
                  showDropdown={showDropdown}
                  customActions={customActions}
                />
              </div>
            </div>
          </div>

          {/* All Variants Showcase */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">All Variants</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Monitor className="h-4 w-4" />
                    Sidebar
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <UserInfoSidebar size="md" showRole={true} />
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Tablet className="h-4 w-4" />
                    Header
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <UserInfoHeader size="md" showRole={false} />
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Smartphone className="h-4 w-4" />
                    Compact
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <UserInfoCompact size="sm" />
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Full
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <UserInfoFull size="lg" layout="vertical" />
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Avatar Components Showcase */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Avatar Components</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Individual Avatars</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-3">
                    <UserAvatar name="John Doe" size="sm" showStatus status="online" />
                    <UserAvatar name="Jane Smith" size="md" showStatus status="away" />
                    <UserAvatar name="Bob Johnson" size="lg" showStatus status="busy" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Avatar Group</CardTitle>
                </CardHeader>
                <CardContent>
                  <UserAvatarGroup 
                    users={mockUsers}
                    max={3}
                    size="md"
                    showCount={true}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">Role Badges</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <UserAvatar name="Admin User" role="admin" showRole size="md" />
                  <UserAvatar name="Manager User" role="manager" showRole size="md" />
                  <UserAvatar name="Regular User" role="user" showRole size="md" />
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Code Example */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Current Configuration Code</h3>
            <div className="p-4 bg-slate-100 dark:bg-slate-800 rounded-lg">
              <pre className="text-sm overflow-x-auto">
{`<UserInfo 
  variant="${selectedVariant}"
  size="${selectedSize}"
  layout="${selectedLayout}"
  showStatus={${showStatus}}
  showRole={${showRole}}
  showLastActive={${showLastActive}}
  showDropdown={${showDropdown}}
  customActions={customActions}
/>`}
              </pre>
            </div>
          </div>

          {/* Features List */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Key Features</h3>
            <ul className="space-y-1 text-sm text-muted-foreground">
              <li>• 🎨 Multiple variants for different contexts (sidebar, header, compact, full)</li>
              <li>• 👤 Smart avatar system with fallbacks and status indicators</li>
              <li>• ⚙️ Configurable actions with role-based filtering</li>
              <li>• 🔗 Built-in Asset Module Editor link for admin users</li>
              <li>• 📱 Responsive design that adapts to screen sizes</li>
              <li>• 🎯 Full TypeScript support with comprehensive interfaces</li>
              <li>• 🔄 Real-time user data synchronization</li>
              <li>• 🎪 Customizable layouts, sizes, and display options</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default UserInfoDemo;
