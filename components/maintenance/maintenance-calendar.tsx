"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Calendar as CalendarIcon,
  ChevronLeft,
  ChevronRight,
  Plus,
  Filter,
  Grid3X3,
  List,
  Clock,
  MapPin,
  User,
  Wrench,
  AlertTriangle,
} from "lucide-react"
import { format, startOfMonth, endOf<PERSON><PERSON><PERSON>, eachDayOfInterval, isSameDay, isToday } from "date-fns"

interface MaintenanceTask {
  id: string
  title: string
  description?: string
  type: string
  priority: string
  status: string
  scheduledDate: Date
  dueDate: Date
  assignedTo?: string
  estimatedDuration?: number
  isOverdue?: boolean
  isDueSoon?: boolean
  asset?: {
    id: string
    name: string
    location?: string
    assetType?: {
      name: string
      color?: string
    }
  }
}

interface CalendarEvent {
  id: string
  taskId?: string
  title: string
  startDate: Date
  endDate: Date
  color?: string
  type: string
  priority: string
  status: string
  task?: MaintenanceTask
}

interface MaintenanceCalendarProps {
  tasks: MaintenanceTask[]
  onTaskUpdate: (taskId: string, updates: any) => void
  onTaskCreate: (taskData: any) => void
}

export function MaintenanceCalendar({
  tasks,
  onTaskUpdate,
  onTaskCreate,
}: MaintenanceCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [viewMode, setViewMode] = useState<"month" | "week" | "day">("month")
  const [filterStatus, setFilterStatus] = useState<string>("all")
  const [filterPriority, setFilterPriority] = useState<string>("all")
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null)
  const [isEventDialogOpen, setIsEventDialogOpen] = useState(false)
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([])

  // Convert tasks to calendar events
  useEffect(() => {
    const events: CalendarEvent[] = tasks.map(task => ({
      id: `task-${task.id}`,
      taskId: task.id,
      title: task.title,
      startDate: new Date(task.scheduledDate),
      endDate: new Date(task.dueDate),
      color: getTaskColor(task),
      type: task.type,
      priority: task.priority,
      status: task.status,
      task,
    }))

    setCalendarEvents(events)
  }, [tasks])

  const getTaskColor = (task: MaintenanceTask): string => {
    if (task.isOverdue) return "#ef4444" // red
    if (task.priority === "critical") return "#dc2626" // red-600
    if (task.priority === "high") return "#ea580c" // orange-600
    if (task.priority === "medium") return "#ca8a04" // yellow-600
    if (task.priority === "low") return "#16a34a" // green-600
    
    // Default colors by type
    switch (task.type) {
      case "preventive": return "#3b82f6" // blue-500
      case "corrective": return "#f97316" // orange-500
      case "predictive": return "#8b5cf6" // violet-500
      case "emergency": return "#ef4444" // red-500
      default: return "#6b7280" // gray-500
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "scheduled":
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Scheduled</Badge>
      case "in_progress":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">In Progress</Badge>
      case "completed":
        return <Badge variant="outline" className="text-green-600 border-green-600">Completed</Badge>
      case "overdue":
        return <Badge variant="destructive">Overdue</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "critical":
        return <Badge variant="destructive">Critical</Badge>
      case "high":
        return <Badge variant="destructive" className="bg-orange-500">High</Badge>
      case "medium":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Medium</Badge>
      case "low":
        return <Badge variant="secondary">Low</Badge>
      default:
        return <Badge variant="outline">{priority}</Badge>
    }
  }

  const filteredEvents = calendarEvents.filter(event => {
    if (filterStatus !== "all" && event.status !== filterStatus) return false
    if (filterPriority !== "all" && event.priority !== filterPriority) return false
    return true
  })

  const getEventsForDate = (date: Date) => {
    return filteredEvents.filter(event =>
      isSameDay(event.startDate, date) || 
      (event.startDate <= date && event.endDate >= date)
    )
  }

  const handleEventClick = (event: CalendarEvent) => {
    setSelectedEvent(event)
    setIsEventDialogOpen(true)
  }

  const handleDateClick = (date: Date) => {
    setSelectedDate(date)
  }

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate)
    if (direction === "prev") {
      newDate.setMonth(newDate.getMonth() - 1)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  const renderMonthView = () => {
    const monthStart = startOfMonth(currentDate)
    const monthEnd = endOfMonth(currentDate)
    const days = eachDayOfInterval({ start: monthStart, end: monthEnd })

    return (
      <div className="grid grid-cols-7 gap-1">
        {/* Day headers */}
        {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(day => (
          <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground">
            {day}
          </div>
        ))}
        
        {/* Calendar days */}
        {days.map(day => {
          const dayEvents = getEventsForDate(day)
          const isSelected = selectedDate && isSameDay(day, selectedDate)
          const isCurrentDay = isToday(day)
          
          return (
            <div
              key={day.toISOString()}
              className={`
                min-h-[100px] p-1 border border-gray-200 cursor-pointer hover:bg-gray-50
                ${isSelected ? "bg-blue-50 border-blue-300" : ""}
                ${isCurrentDay ? "bg-yellow-50" : ""}
              `}
              onClick={() => handleDateClick(day)}
            >
              <div className={`text-sm font-medium mb-1 ${isCurrentDay ? "text-blue-600" : ""}`}>
                {format(day, "d")}
              </div>
              
              <div className="space-y-1">
                {dayEvents.slice(0, 3).map(event => (
                  <div
                    key={event.id}
                    className="text-xs p-1 rounded cursor-pointer hover:opacity-80"
                    style={{ backgroundColor: event.color + "20", borderLeft: `3px solid ${event.color}` }}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleEventClick(event)
                    }}
                  >
                    <div className="font-medium truncate">{event.title}</div>
                    <div className="text-muted-foreground">
                      {format(event.startDate, "HH:mm")}
                    </div>
                  </div>
                ))}
                
                {dayEvents.length > 3 && (
                  <div className="text-xs text-muted-foreground">
                    +{dayEvents.length - 3} more
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  const renderListView = () => {
    const selectedDateEvents = selectedDate ? getEventsForDate(selectedDate) : []
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">
            {selectedDate ? format(selectedDate, "MMMM d, yyyy") : "Select a date"}
          </h3>
          <Badge variant="outline">
            {selectedDateEvents.length} tasks
          </Badge>
        </div>
        
        {selectedDateEvents.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No maintenance tasks scheduled for this date
          </div>
        ) : (
          <div className="space-y-3">
            {selectedDateEvents.map(event => (
              <Card key={event.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4" onClick={() => handleEventClick(event)}>
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: event.color }}
                        />
                        <h4 className="font-medium">{event.title}</h4>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {format(event.startDate, "HH:mm")} - {format(event.endDate, "HH:mm")}
                        </div>
                        
                        {event.task?.asset?.name && (
                          <div className="flex items-center">
                            <Wrench className="h-4 w-4 mr-1" />
                            {event.task.asset.name}
                          </div>
                        )}
                        
                        {event.task?.asset?.location && (
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1" />
                            {event.task.asset.location}
                          </div>
                        )}
                        
                        {event.task?.assignedTo && (
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            {event.task.assignedTo}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(event.status)}
                        {getPriorityBadge(event.priority)}
                        {event.task?.isOverdue && (
                          <Badge variant="destructive" className="flex items-center">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            Overdue
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Calendar Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth("prev")}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <h2 className="text-xl font-semibold min-w-[200px] text-center">
              {format(currentDate, "MMMM yyyy")}
            </h2>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth("next")}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentDate(new Date())}
          >
            Today
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="scheduled">Scheduled</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="overdue">Overdue</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={filterPriority} onValueChange={setFilterPriority}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priority</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>
          
          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "month" | "week" | "day")}>
            <TabsList>
              <TabsTrigger value="month">
                <Grid3X3 className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger value="list">
                <List className="h-4 w-4" />
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {/* Calendar Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3">
          <Card>
            <CardContent className="p-6">
              {viewMode === "month" ? renderMonthView() : renderListView()}
            </CardContent>
          </Card>
        </div>
        
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Mini Calendar</CardTitle>
            </CardHeader>
            <CardContent>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                className="rounded-md border"
              />
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Legend</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-blue-500" />
                <span className="text-sm">Preventive</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-orange-500" />
                <span className="text-sm">Corrective</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-violet-500" />
                <span className="text-sm">Predictive</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500" />
                <span className="text-sm">Emergency</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Event Details Dialog */}
      <Dialog open={isEventDialogOpen} onOpenChange={setIsEventDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{selectedEvent?.title}</DialogTitle>
            <DialogDescription>
              Maintenance task details and actions
            </DialogDescription>
          </DialogHeader>
          
          {selectedEvent?.task && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Asset</label>
                  <p className="text-sm text-muted-foreground">
                    {selectedEvent.task.asset?.name}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Location</label>
                  <p className="text-sm text-muted-foreground">
                    {selectedEvent.task.asset?.location || "Not specified"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Scheduled</label>
                  <p className="text-sm text-muted-foreground">
                    {format(selectedEvent.startDate, "MMM dd, yyyy HH:mm")}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Due</label>
                  <p className="text-sm text-muted-foreground">
                    {format(selectedEvent.endDate, "MMM dd, yyyy HH:mm")}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Assigned To</label>
                  <p className="text-sm text-muted-foreground">
                    {selectedEvent.task.assignedTo || "Unassigned"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Duration</label>
                  <p className="text-sm text-muted-foreground">
                    {selectedEvent.task.estimatedDuration ? `${selectedEvent.task.estimatedDuration} minutes` : "Not specified"}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {getStatusBadge(selectedEvent.status)}
                {getPriorityBadge(selectedEvent.priority)}
              </div>
              
              {selectedEvent.task.description && (
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {selectedEvent.task.description}
                  </p>
                </div>
              )}
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEventDialogOpen(false)}>
              Close
            </Button>
            <Button onClick={() => {
              // Handle edit action
              setIsEventDialogOpen(false)
            }}>
              Edit Task
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
