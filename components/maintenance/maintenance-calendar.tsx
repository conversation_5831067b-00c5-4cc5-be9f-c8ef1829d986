"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import {
  Dialog,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogTit<PERSON>,
} from "@/components/ui/dialog"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Calendar as CalendarIcon,
  ChevronLeft,
  ChevronRight,
  Plus,
  Filter,
  Grid3X3,
  List,
  Clock,
  MapPin,
  User,
  Wrench,
  AlertTriangle,
  Move,
  Copy,
  Edit,
  Trash2,
  Eye,
  Settings,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Save,
  Download,
  Upload,
  Share,
  Maximize2,
  Minimize2,
  Calendar as CalendarViewIcon,
  BarChart3,
  Users,
  Target,
} from "lucide-react"
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameDay,
  isToday,
  startOfWeek,
  endOfWeek,
  addDays,
  addWeeks,
  addMonths,
  subDays,
  subWeeks,
  subMonths,
  startOfDay,
  endOfDay,
  differenceInMinutes,
  addMinutes,
  parseISO,
  isWithinInterval,
  getHours,
  getMinutes,
} from "date-fns"
import { Progress } from "../ui/progress"

interface MaintenanceTask {
  id: string
  title: string
  description?: string
  type: string
  priority: string
  status: string
  scheduledDate: Date
  dueDate: Date
  assignedTo?: string
  estimatedDuration?: number
  isOverdue?: boolean
  isDueSoon?: boolean
  asset?: {
    id: string
    name: string
    location?: string
    assetType?: {
      name: string
      color?: string
    }
  }
}

interface CalendarEvent {
  id: string
  taskId?: string
  title: string
  startDate: Date
  endDate: Date
  color?: string
  type: string
  priority: string
  status: string
  task?: MaintenanceTask
  assignedTo?: string
  location?: string
  duration?: number
  isBlocked?: boolean
  isRecurring?: boolean
  conflictsWith?: string[]
  resourceRequirements?: {
    technicians: number
    equipment: string[]
    parts: string[]
  }
}

interface TimeSlot {
  start: Date
  end: Date
  available: boolean
  conflicts: CalendarEvent[]
  utilization: number
}

interface ResourceAvailability {
  resourceId: string
  resourceName: string
  resourceType: "technician" | "equipment" | "location"
  availability: TimeSlot[]
  currentLoad: number
  maxCapacity: number
}

interface DragState {
  isDragging: boolean
  draggedEvent?: CalendarEvent
  dragOffset?: { x: number; y: number }
  dropTarget?: { date: Date; time?: string }
  isResizing?: boolean
  resizeHandle?: "start" | "end"
}

interface MaintenanceCalendarProps {
  tasks: MaintenanceTask[]
  onTaskUpdate: (taskId: string, updates: any) => void
  onTaskCreate: (taskData: any) => void
  onTaskDelete?: (taskId: string) => void
  resources?: ResourceAvailability[]
  workingHours?: { start: string; end: string }
  enableDragDrop?: boolean
  enableResourcePlanning?: boolean
  enableConflictDetection?: boolean
}

export function MaintenanceCalendar({
  tasks,
  onTaskUpdate,
  onTaskCreate,
  onTaskDelete,
  resources = [],
  workingHours = { start: "09:00", end: "17:00" },
  enableDragDrop = true,
  enableResourcePlanning = true,
  enableConflictDetection = true,
}: MaintenanceCalendarProps) {
  // Core state
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [viewMode, setViewMode] = useState<"month" | "week" | "day" | "timeline" | "resource" | "list">("month")
  const [timelineScale, setTimelineScale] = useState<"hour" | "day" | "week">("day")
  const [zoomLevel, setZoomLevel] = useState(1)

  // Filtering and display
  const [filterStatus, setFilterStatus] = useState<string>("all")
  const [filterPriority, setFilterPriority] = useState<string>("all")
  const [filterAssignee, setFilterAssignee] = useState<string>("all")
  const [filterLocation, setFilterLocation] = useState<string>("all")
  const [showConflicts, setShowConflicts] = useState(true)
  const [showResourceUtilization, setShowResourceUtilization] = useState(true)

  // Event management
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null)
  const [isEventDialogOpen, setIsEventDialogOpen] = useState(false)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([])
  const [conflicts, setConflicts] = useState<{ [eventId: string]: string[] }>({})

  // Drag and drop state
  const [dragState, setDragState] = useState<DragState>({ isDragging: false })
  const [isFullscreen, setIsFullscreen] = useState(false)

  // Refs for drag and drop
  const calendarRef = useRef<HTMLDivElement>(null)
  const dragPreviewRef = useRef<HTMLDivElement>(null)

  // Additional state for enhanced features
  const [workingHoursState, setWorkingHours] = useState(workingHours)

  // Convert tasks to calendar events with enhanced data
  useEffect(() => {
    const events: CalendarEvent[] = tasks.map(task => ({
      id: `task-${task.id}`,
      taskId: task.id,
      title: task.title,
      startDate: new Date(task.scheduledDate),
      endDate: new Date(task.dueDate),
      color: getTaskColor(task),
      type: task.type,
      priority: task.priority,
      status: task.status,
      task,
      assignedTo: task.assignedTo,
      location: task.asset?.location,
      duration: task.estimatedDuration,
      isBlocked: false,
      isRecurring: false, // Will be enhanced when recurring tasks are implemented
      conflictsWith: [],
      resourceRequirements: {
        technicians: 1,
        equipment: [],
        parts: [],
      },
    }))

    setCalendarEvents(events)

    // Detect conflicts if enabled
    if (enableConflictDetection) {
      detectConflicts(events)
    }
  }, [tasks, enableConflictDetection])

  // Conflict detection
  const detectConflicts = useCallback((events: CalendarEvent[]) => {
    const conflictMap: { [eventId: string]: string[] } = {}

    events.forEach((event, index) => {
      const eventConflicts: string[] = []

      events.forEach((otherEvent, otherIndex) => {
        if (index !== otherIndex && event.assignedTo === otherEvent.assignedTo) {
          // Check for time overlap
          const eventStart = event.startDate.getTime()
          const eventEnd = event.endDate.getTime()
          const otherStart = otherEvent.startDate.getTime()
          const otherEnd = otherEvent.endDate.getTime()

          if (
            (eventStart >= otherStart && eventStart < otherEnd) ||
            (eventEnd > otherStart && eventEnd <= otherEnd) ||
            (eventStart <= otherStart && eventEnd >= otherEnd)
          ) {
            eventConflicts.push(otherEvent.id)
          }
        }
      })

      if (eventConflicts.length > 0) {
        conflictMap[event.id] = eventConflicts
      }
    })

    setConflicts(conflictMap)
  }, [])

  // Drag and drop handlers
  const handleDragStart = useCallback((event: CalendarEvent, clientX: number, clientY: number) => {
    if (!enableDragDrop) return

    setDragState({
      isDragging: true,
      draggedEvent: event,
      dragOffset: { x: clientX, y: clientY },
    })
  }, [enableDragDrop])

  const handleDragMove = useCallback((clientX: number, clientY: number) => {
    if (!dragState.isDragging || !dragState.draggedEvent) return

    // Calculate drop target based on mouse position
    const calendarElement = calendarRef.current
    if (!calendarElement) return

    const rect = calendarElement.getBoundingClientRect()
    const relativeX = clientX - rect.left
    const relativeY = clientY - rect.top

    // Determine target date and time based on view mode and position
    const dropTarget = calculateDropTarget(relativeX, relativeY)

    setDragState(prev => ({
      ...prev,
      dropTarget,
    }))
  }, [dragState.isDragging, dragState.draggedEvent])

  const handleDragEnd = useCallback(async () => {
    if (!dragState.isDragging || !dragState.draggedEvent || !dragState.dropTarget) {
      setDragState({ isDragging: false })
      return
    }

    const { draggedEvent, dropTarget } = dragState

    // Calculate new start and end times
    const originalDuration = draggedEvent.endDate.getTime() - draggedEvent.startDate.getTime()
    const newStartDate = dropTarget.date
    const newEndDate = new Date(newStartDate.getTime() + originalDuration)

    // Update the event
    try {
      await onTaskUpdate(draggedEvent.taskId!, {
        scheduledDate: newStartDate,
        dueDate: newEndDate,
      })

      toast({
        title: "Task Rescheduled",
        description: `${draggedEvent.title} moved to ${format(newStartDate, "MMM dd, yyyy HH:mm")}`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reschedule task",
        variant: "destructive",
      })
    }

    setDragState({ isDragging: false })
  }, [dragState, onTaskUpdate])

  const calculateDropTarget = useCallback((x: number, y: number): { date: Date; time?: string } => {
    // This is a simplified calculation - in a real implementation,
    // you'd calculate based on the actual calendar grid layout
    const baseDate = selectedDate || currentDate
    const dayOffset = Math.floor(x / 100) // Simplified calculation
    const hourOffset = Math.floor(y / 50) // Simplified calculation

    const targetDate = addDays(baseDate, dayOffset)
    targetDate.setHours(9 + hourOffset, 0, 0, 0) // Start from 9 AM

    return { date: targetDate }
  }, [selectedDate, currentDate])

  const getTaskColor = (task: MaintenanceTask): string => {
    if (task.isOverdue) return "#ef4444" // red
    if (task.priority === "critical") return "#dc2626" // red-600
    if (task.priority === "high") return "#ea580c" // orange-600
    if (task.priority === "medium") return "#ca8a04" // yellow-600
    if (task.priority === "low") return "#16a34a" // green-600
    
    // Default colors by type
    switch (task.type) {
      case "preventive": return "#3b82f6" // blue-500
      case "corrective": return "#f97316" // orange-500
      case "predictive": return "#8b5cf6" // violet-500
      case "emergency": return "#ef4444" // red-500
      default: return "#6b7280" // gray-500
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "scheduled":
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Scheduled</Badge>
      case "in_progress":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">In Progress</Badge>
      case "completed":
        return <Badge variant="outline" className="text-green-600 border-green-600">Completed</Badge>
      case "overdue":
        return <Badge variant="destructive">Overdue</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "critical":
        return <Badge variant="destructive">Critical</Badge>
      case "high":
        return <Badge variant="destructive" className="bg-orange-500">High</Badge>
      case "medium":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Medium</Badge>
      case "low":
        return <Badge variant="secondary">Low</Badge>
      default:
        return <Badge variant="outline">{priority}</Badge>
    }
  }

  const filteredEvents = calendarEvents.filter(event => {
    if (filterStatus !== "all" && event.status !== filterStatus) return false
    if (filterPriority !== "all" && event.priority !== filterPriority) return false
    return true
  })

  const getEventsForDate = (date: Date) => {
    return filteredEvents.filter(event =>
      isSameDay(event.startDate, date) || 
      (event.startDate <= date && event.endDate >= date)
    )
  }

  const handleEventClick = (event: CalendarEvent) => {
    setSelectedEvent(event)
    setIsEventDialogOpen(true)
  }

  const handleDateClick = (date: Date) => {
    setSelectedDate(date)
  }

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate)
    if (direction === "prev") {
      newDate.setMonth(newDate.getMonth() - 1)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  // Enhanced timeline view
  const renderTimelineView = () => {
    const timelineStart = startOfDay(currentDate)
    const timelineEnd = endOfDay(addDays(currentDate, timelineScale === "hour" ? 1 : timelineScale === "day" ? 7 : 30))

    const timeSlots = []
    let current = timelineStart

    while (current <= timelineEnd) {
      const slotEvents = getEventsForTimeSlot(current)
      timeSlots.push({
        time: current,
        events: slotEvents,
        utilization: calculateUtilization(slotEvents),
      })

      current = timelineScale === "hour"
        ? addMinutes(current, 60)
        : timelineScale === "day"
        ? addDays(current, 1)
        : addWeeks(current, 1)
    }

    return (
      <div className="timeline-view" style={{ zoom: zoomLevel }}>
        <div className="timeline-header grid grid-cols-12 gap-1 mb-4">
          <div className="col-span-2 font-medium text-sm">Time</div>
          <div className="col-span-3 font-medium text-sm">Tasks</div>
          <div className="col-span-2 font-medium text-sm">Assignee</div>
          <div className="col-span-2 font-medium text-sm">Location</div>
          <div className="col-span-2 font-medium text-sm">Status</div>
          <div className="col-span-1 font-medium text-sm">Load</div>
        </div>

        <ScrollArea className="h-[600px]">
          <div className="space-y-1">
            {timeSlots.map((slot, index) => (
              <div
                key={index}
                className={`
                  timeline-slot grid grid-cols-12 gap-1 p-2 rounded border
                  ${slot.utilization > 0.8 ? "bg-red-50 border-red-200" :
                    slot.utilization > 0.6 ? "bg-yellow-50 border-yellow-200" :
                    "bg-gray-50 border-gray-200"}
                  hover:bg-gray-100 transition-colors
                `}
                onDrop={(e) => handleTimeSlotDrop(e, slot.time)}
                onDragOver={(e) => e.preventDefault()}
              >
                <div className="col-span-2 text-sm font-medium">
                  {format(slot.time, timelineScale === "hour" ? "HH:mm" : "MMM dd")}
                </div>

                <div className="col-span-3 space-y-1">
                  {slot.events.slice(0, 3).map((event) => (
                    <div
                      key={event.id}
                      className="text-xs p-1 rounded cursor-pointer"
                      style={{
                        backgroundColor: event.color + "20",
                        borderLeft: `3px solid ${event.color}`
                      }}
                      draggable={enableDragDrop}
                      onDragStart={(e) => handleEventDragStart(e, event)}
                      onClick={() => handleEventClick(event)}
                    >
                      <div className="font-medium truncate">{event.title}</div>
                      {event.duration && (
                        <div className="text-muted-foreground">
                          {event.duration}min
                        </div>
                      )}
                    </div>
                  ))}
                  {slot.events.length > 3 && (
                    <div className="text-xs text-muted-foreground">
                      +{slot.events.length - 3} more
                    </div>
                  )}
                </div>

                <div className="col-span-2 text-sm">
                  {slot.events.map(e => e.assignedTo).filter(Boolean).slice(0, 2).join(", ")}
                </div>

                <div className="col-span-2 text-sm">
                  {slot.events.map(e => e.location).filter(Boolean).slice(0, 2).join(", ")}
                </div>

                <div className="col-span-2">
                  <div className="flex space-x-1">
                    {slot.events.slice(0, 3).map((event) => (
                      <Badge key={event.id} variant="outline" className="text-xs">
                        {event.status}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="col-span-1">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              slot.utilization > 0.8 ? "bg-red-500" :
                              slot.utilization > 0.6 ? "bg-yellow-500" :
                              "bg-green-500"
                            }`}
                            style={{ width: `${Math.min(slot.utilization * 100, 100)}%` }}
                          />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Utilization: {(slot.utilization * 100).toFixed(1)}%</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>
    )
  }

  const getEventsForTimeSlot = (time: Date) => {
    return filteredEvents.filter(event => {
      const eventStart = event.startDate
      const eventEnd = event.endDate

      if (timelineScale === "hour") {
        const slotStart = time
        const slotEnd = addMinutes(time, 60)
        return isWithinInterval(eventStart, { start: slotStart, end: slotEnd }) ||
               isWithinInterval(eventEnd, { start: slotStart, end: slotEnd }) ||
               (eventStart <= slotStart && eventEnd >= slotEnd)
      } else if (timelineScale === "day") {
        return isSameDay(eventStart, time) ||
               (eventStart <= time && eventEnd >= time)
      } else {
        const weekStart = startOfWeek(time)
        const weekEnd = endOfWeek(time)
        return isWithinInterval(eventStart, { start: weekStart, end: weekEnd }) ||
               isWithinInterval(eventEnd, { start: weekStart, end: weekEnd })
      }
    })
  }

  const calculateUtilization = (events: CalendarEvent[]) => {
    if (events.length === 0) return 0

    const totalDuration = events.reduce((sum, event) => {
      return sum + (event.duration || 60) // Default 60 minutes if no duration
    }, 0)

    const maxCapacity = timelineScale === "hour" ? 60 : timelineScale === "day" ? 480 : 2400 // minutes
    return Math.min(totalDuration / maxCapacity, 1)
  }

  const handleTimeSlotDrop = (e: React.DragEvent, targetTime: Date) => {
    e.preventDefault()
    if (dragState.draggedEvent) {
      setDragState(prev => ({
        ...prev,
        dropTarget: { date: targetTime },
      }))
      handleDragEnd()
    }
  }

  const handleEventDragStart = (e: React.DragEvent, event: CalendarEvent) => {
    handleDragStart(event, e.clientX, e.clientY)
  }

  const renderMonthView = () => {
    const monthStart = startOfMonth(currentDate)
    const monthEnd = endOfMonth(currentDate)
    const days = eachDayOfInterval({ start: monthStart, end: monthEnd })

    return (
      <div className="grid grid-cols-7 gap-1" ref={calendarRef}>
        {/* Day headers */}
        {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(day => (
          <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground">
            {day}
          </div>
        ))}

        {/* Calendar days */}
        {days.map(day => {
          const dayEvents = getEventsForDate(day)
          const isSelected = selectedDate && isSameDay(day, selectedDate)
          const isCurrentDay = isToday(day)
          const hasConflicts = dayEvents.some(event => conflicts[event.id])
          const utilization = calculateDayUtilization(dayEvents)

          return (
            <div
              key={day.toISOString()}
              className={`
                min-h-[120px] p-1 border border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors
                ${isSelected ? "bg-blue-50 border-blue-300" : ""}
                ${isCurrentDay ? "bg-yellow-50" : ""}
                ${hasConflicts && showConflicts ? "border-red-300 bg-red-50" : ""}
                ${dragState.isDragging && dragState.dropTarget?.date && isSameDay(dragState.dropTarget.date, day)
                  ? "bg-green-50 border-green-300" : ""}
              `}
              onClick={() => handleDateClick(day)}
              onDrop={(e) => handleDayDrop(e, day)}
              onDragOver={(e) => e.preventDefault()}
              onDragEnter={(e) => e.preventDefault()}
            >
              <div className="flex items-center justify-between mb-1">
                <div className={`text-sm font-medium ${isCurrentDay ? "text-blue-600" : ""}`}>
                  {format(day, "d")}
                </div>

                {/* Utilization indicator */}
                {showResourceUtilization && utilization > 0 && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <div className={`w-2 h-2 rounded-full ${
                          utilization > 0.8 ? "bg-red-500" :
                          utilization > 0.6 ? "bg-yellow-500" :
                          "bg-green-500"
                        }`} />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Utilization: {(utilization * 100).toFixed(1)}%</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>

              <div className="space-y-1">
                {dayEvents.slice(0, 3).map(event => (
                  <ContextMenu key={event.id}>
                    <ContextMenuTrigger>
                      <div
                        className={`
                          text-xs p-1 rounded cursor-pointer hover:opacity-80 transition-opacity
                          ${conflicts[event.id] && showConflicts ? "border border-red-400" : ""}
                        `}
                        style={{
                          backgroundColor: event.color + "20",
                          borderLeft: `3px solid ${event.color}`
                        }}
                        draggable={enableDragDrop}
                        onDragStart={(e) => handleEventDragStart(e, event)}
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEventClick(event)
                        }}
                      >
                        <div className="font-medium truncate">{event.title}</div>
                        <div className="text-muted-foreground flex items-center justify-between">
                          <span>{format(event.startDate, "HH:mm")}</span>
                          {conflicts[event.id] && showConflicts && (
                            <AlertTriangle className="h-3 w-3 text-red-500" />
                          )}
                        </div>
                      </div>
                    </ContextMenuTrigger>
                    <ContextMenuContent>
                      <ContextMenuItem onClick={() => handleEventClick(event)}>
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </ContextMenuItem>
                      <ContextMenuItem onClick={() => handleEditEvent(event)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Task
                      </ContextMenuItem>
                      <ContextMenuItem onClick={() => handleDuplicateEvent(event)}>
                        <Copy className="mr-2 h-4 w-4" />
                        Duplicate
                      </ContextMenuItem>
                      <ContextMenuSeparator />
                      <ContextMenuItem
                        onClick={() => handleDeleteEvent(event)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </ContextMenuItem>
                    </ContextMenuContent>
                  </ContextMenu>
                ))}

                {dayEvents.length > 3 && (
                  <div className="text-xs text-muted-foreground">
                    +{dayEvents.length - 3} more
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  const calculateDayUtilization = (events: CalendarEvent[]) => {
    const totalDuration = events.reduce((sum, event) => {
      return sum + (event.duration || 60)
    }, 0)
    return Math.min(totalDuration / 480, 1) // 8 hours = 480 minutes
  }

  const handleDayDrop = (e: React.DragEvent, day: Date) => {
    e.preventDefault()
    if (dragState.draggedEvent) {
      setDragState(prev => ({
        ...prev,
        dropTarget: { date: day },
      }))
      handleDragEnd()
    }
  }

  const handleEditEvent = (event: CalendarEvent) => {
    setSelectedEvent(event)
    setIsEventDialogOpen(true)
  }

  const handleDuplicateEvent = async (event: CalendarEvent) => {
    if (!event.task) return

    const duplicatedTask = {
      ...event.task,
      title: `${event.task.title} (Copy)`,
      scheduledDate: addDays(event.startDate, 1),
      dueDate: addDays(event.endDate, 1),
    }

    try {
      await onTaskCreate(duplicatedTask)
      toast({
        title: "Task Duplicated",
        description: "Task has been duplicated successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate task",
        variant: "destructive",
      })
    }
  }

  const handleDeleteEvent = async (event: CalendarEvent) => {
    if (!event.taskId || !onTaskDelete) return

    try {
      await onTaskDelete(event.taskId)
      toast({
        title: "Task Deleted",
        description: "Task has been deleted successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete task",
        variant: "destructive",
      })
    }
  }

  // Enhanced week view with resource planning
  const renderWeekView = () => {
    const weekStart = startOfWeek(currentDate)
    const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i))
    const hours = Array.from({ length: 12 }, (_, i) => i + 8) // 8 AM to 8 PM

    return (
      <div className="week-view">
        <div className="grid grid-cols-8 gap-1 mb-4">
          <div className="text-sm font-medium">Time</div>
          {weekDays.map(day => (
            <div key={day.toISOString()} className="text-sm font-medium text-center">
              <div>{format(day, "EEE")}</div>
              <div className={`text-lg ${isToday(day) ? "text-blue-600 font-bold" : ""}`}>
                {format(day, "dd")}
              </div>
            </div>
          ))}
        </div>

        <ScrollArea className="h-[600px]">
          <div className="grid grid-cols-8 gap-1">
            {hours.map(hour => (
              <React.Fragment key={hour}>
                <div className="text-sm text-muted-foreground p-2 border-r">
                  {hour}:00
                </div>
                {weekDays.map(day => {
                  const slotStart = new Date(day)
                  slotStart.setHours(hour, 0, 0, 0)
                  const slotEnd = new Date(slotStart)
                  slotEnd.setHours(hour + 1, 0, 0, 0)

                  const slotEvents = getEventsForTimeSlot(slotStart)
                  const hasConflicts = slotEvents.some(event => conflicts[event.id])

                  return (
                    <div
                      key={`${day.toISOString()}-${hour}`}
                      className={`
                        min-h-[60px] p-1 border border-gray-200 hover:bg-gray-50 transition-colors
                        ${hasConflicts && showConflicts ? "bg-red-50 border-red-300" : ""}
                        ${dragState.isDragging && dragState.dropTarget?.date &&
                          isSameDay(dragState.dropTarget.date, day) ? "bg-green-50 border-green-300" : ""}
                      `}
                      onDrop={(e) => handleTimeSlotDrop(e, slotStart)}
                      onDragOver={(e) => e.preventDefault()}
                      onClick={() => handleTimeSlotClick(slotStart)}
                    >
                      {slotEvents.map(event => {
                        const eventStart = event.startDate
                        const eventDuration = event.duration || 60
                        const eventHeight = Math.min((eventDuration / 60) * 60, 60) // Max 60px height

                        return (
                          <div
                            key={event.id}
                            className={`
                              text-xs p-1 rounded cursor-pointer mb-1 relative
                              ${conflicts[event.id] && showConflicts ? "border border-red-400" : ""}
                            `}
                            style={{
                              backgroundColor: event.color + "40",
                              borderLeft: `3px solid ${event.color}`,
                              height: `${eventHeight}px`,
                              minHeight: "20px"
                            }}
                            draggable={enableDragDrop}
                            onDragStart={(e) => handleEventDragStart(e, event)}
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEventClick(event)
                            }}
                          >
                            <div className="font-medium truncate">{event.title}</div>
                            {event.assignedTo && (
                              <div className="text-muted-foreground truncate">
                                {event.assignedTo}
                              </div>
                            )}
                            {conflicts[event.id] && showConflicts && (
                              <AlertTriangle className="absolute top-0 right-0 h-3 w-3 text-red-500" />
                            )}
                          </div>
                        )
                      })}
                    </div>
                  )
                })}
              </React.Fragment>
            ))}
          </div>
        </ScrollArea>
      </div>
    )
  }

  const handleTimeSlotClick = (time: Date) => {
    setSelectedDate(time)
    setIsCreateDialogOpen(true)
  }

  // Enhanced resource planning view
  const renderResourceView = () => {
    if (!enableResourcePlanning) return null

    const weekStart = startOfWeek(currentDate)
    const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i))

    return (
      <div className="resource-view space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Resource Planning</h3>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">
              {resources.length} Resources
            </Badge>
            <Button variant="outline" size="sm">
              <Users className="h-4 w-4 mr-2" />
              Manage Resources
            </Button>
          </div>
        </div>

        <div className="grid gap-4">
          {resources.map(resource => (
            <Card key={resource.resourceId}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-base">{resource.resourceName}</CardTitle>
                    <CardDescription>{resource.resourceType}</CardDescription>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {((resource.currentLoad / resource.maxCapacity) * 100).toFixed(1)}% Utilized
                    </div>
                    <Progress
                      value={(resource.currentLoad / resource.maxCapacity) * 100}
                      className="w-20 h-2 mt-1"
                    />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-7 gap-1">
                  {weekDays.map(day => {
                    const dayAvailability = resource.availability.find(slot =>
                      isSameDay(slot.start, day)
                    )
                    const dayEvents = getEventsForDate(day).filter(event =>
                      event.assignedTo === resource.resourceName ||
                      event.resourceRequirements?.equipment.includes(resource.resourceName)
                    )

                    return (
                      <div key={day.toISOString()} className="text-center">
                        <div className="text-xs font-medium mb-1">
                          {format(day, "EEE dd")}
                        </div>
                        <div className={`
                          h-16 rounded border-2 border-dashed p-1
                          ${dayAvailability?.available ? "border-green-300 bg-green-50" : "border-red-300 bg-red-50"}
                        `}>
                          <div className="space-y-1">
                            {dayEvents.slice(0, 2).map(event => (
                              <div
                                key={event.id}
                                className="text-xs p-1 rounded bg-white border"
                                style={{ borderColor: event.color }}
                              >
                                {event.title.substring(0, 10)}...
                              </div>
                            ))}
                            {dayEvents.length > 2 && (
                              <div className="text-xs text-muted-foreground">
                                +{dayEvents.length - 2}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const renderListView = () => {
    const selectedDateEvents = selectedDate ? getEventsForDate(selectedDate) : []
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">
            {selectedDate ? format(selectedDate, "MMMM d, yyyy") : "Select a date"}
          </h3>
          <Badge variant="outline">
            {selectedDateEvents.length} tasks
          </Badge>
        </div>
        
        {selectedDateEvents.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No maintenance tasks scheduled for this date
          </div>
        ) : (
          <div className="space-y-3">
            {selectedDateEvents.map(event => (
              <Card key={event.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4" onClick={() => handleEventClick(event)}>
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: event.color }}
                        />
                        <h4 className="font-medium">{event.title}</h4>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {format(event.startDate, "HH:mm")} - {format(event.endDate, "HH:mm")}
                        </div>
                        
                        {event.task?.asset?.name && (
                          <div className="flex items-center">
                            <Wrench className="h-4 w-4 mr-1" />
                            {event.task.asset.name}
                          </div>
                        )}
                        
                        {event.task?.asset?.location && (
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1" />
                            {event.task.asset.location}
                          </div>
                        )}
                        
                        {event.task?.assignedTo && (
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            {event.task.assignedTo}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(event.status)}
                        {getPriorityBadge(event.priority)}
                        {event.task?.isOverdue && (
                          <Badge variant="destructive" className="flex items-center">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            Overdue
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${isFullscreen ? "fixed inset-0 z-50 bg-white p-6 overflow-auto" : ""}`}>
      {/* Enhanced Calendar Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth("prev")}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <h2 className="text-xl font-semibold min-w-[200px] text-center">
              {format(currentDate, viewMode === "day" ? "MMMM dd, yyyy" : "MMMM yyyy")}
            </h2>

            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth("next")}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentDate(new Date())}
          >
            Today
          </Button>

          {/* Zoom controls for timeline view */}
          {viewMode === "timeline" && (
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setZoomLevel(Math.max(0.5, zoomLevel - 0.25))}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm">{Math.round(zoomLevel * 100)}%</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setZoomLevel(Math.min(2, zoomLevel + 0.25))}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* Advanced Filters */}
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="scheduled">Scheduled</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="overdue">Overdue</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterPriority} onValueChange={setFilterPriority}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priority</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterAssignee} onValueChange={setFilterAssignee}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Assignee" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Assignees</SelectItem>
              {/* Add dynamic assignee options */}
            </SelectContent>
          </Select>

          {/* Timeline scale selector */}
          {viewMode === "timeline" && (
            <Select value={timelineScale} onValueChange={(value: any) => setTimelineScale(value)}>
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hour">Hour</SelectItem>
                <SelectItem value="day">Day</SelectItem>
                <SelectItem value="week">Week</SelectItem>
              </SelectContent>
            </Select>
          )}

          {/* View Mode Tabs */}
          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)}>
            <TabsList>
              <TabsTrigger value="month">
                <Grid3X3 className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger value="week">
                <CalendarViewIcon className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger value="timeline">
                <BarChart3 className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger value="resource">
                <Users className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger value="list">
                <List className="h-4 w-4" />
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Display Options */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <h4 className="font-medium">Display Options</h4>

                <div className="flex items-center justify-between">
                  <Label htmlFor="show-conflicts">Show Conflicts</Label>
                  <Switch
                    id="show-conflicts"
                    checked={showConflicts}
                    onCheckedChange={setShowConflicts}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="show-utilization">Show Resource Utilization</Label>
                  <Switch
                    id="show-utilization"
                    checked={showResourceUtilization}
                    onCheckedChange={setShowResourceUtilization}
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>Working Hours</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="time"
                      value={workingHoursState.start}
                      onChange={(e) => setWorkingHours(prev => ({ ...prev, start: e.target.value }))}
                    />
                    <Input
                      type="time"
                      value={workingHoursState.end}
                      onChange={(e) => setWorkingHours(prev => ({ ...prev, end: e.target.value }))}
                    />
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Action Buttons */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>

          <Button size="sm" onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Task
          </Button>
        </div>
      </div>

      {/* Calendar Content */}
      <div className={`grid gap-6 ${viewMode === "resource" ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-4"}`}>
        <div className={viewMode === "resource" ? "col-span-1" : "lg:col-span-3"}>
          <Card>
            <CardContent className="p-6">
              {viewMode === "month" && renderMonthView()}
              {viewMode === "week" && renderWeekView()}
              {viewMode === "timeline" && renderTimelineView()}
              {viewMode === "resource" && renderResourceView()}
              {viewMode === "list" && renderListView()}
            </CardContent>
          </Card>
        </div>
        
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Mini Calendar</CardTitle>
            </CardHeader>
            <CardContent>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                className="rounded-md border"
              />
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Legend</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-blue-500" />
                <span className="text-sm">Preventive</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-orange-500" />
                <span className="text-sm">Corrective</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-violet-500" />
                <span className="text-sm">Predictive</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500" />
                <span className="text-sm">Emergency</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Event Details Dialog */}
      <Dialog open={isEventDialogOpen} onOpenChange={setIsEventDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{selectedEvent?.title}</DialogTitle>
            <DialogDescription>
              Maintenance task details and actions
            </DialogDescription>
          </DialogHeader>
          
          {selectedEvent?.task && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Asset</label>
                  <p className="text-sm text-muted-foreground">
                    {selectedEvent.task.asset?.name}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Location</label>
                  <p className="text-sm text-muted-foreground">
                    {selectedEvent.task.asset?.location || "Not specified"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Scheduled</label>
                  <p className="text-sm text-muted-foreground">
                    {format(selectedEvent.startDate, "MMM dd, yyyy HH:mm")}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Due</label>
                  <p className="text-sm text-muted-foreground">
                    {format(selectedEvent.endDate, "MMM dd, yyyy HH:mm")}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Assigned To</label>
                  <p className="text-sm text-muted-foreground">
                    {selectedEvent.task.assignedTo || "Unassigned"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Duration</label>
                  <p className="text-sm text-muted-foreground">
                    {selectedEvent.task.estimatedDuration ? `${selectedEvent.task.estimatedDuration} minutes` : "Not specified"}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {getStatusBadge(selectedEvent.status)}
                {getPriorityBadge(selectedEvent.priority)}
              </div>
              
              {selectedEvent.task.description && (
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {selectedEvent.task.description}
                  </p>
                </div>
              )}
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEventDialogOpen(false)}>
              Close
            </Button>
            <Button onClick={() => {
              // Handle edit action
              setIsEventDialogOpen(false)
            }}>
              Edit Task
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
