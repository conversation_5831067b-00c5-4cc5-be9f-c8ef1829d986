"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  <PERSON>,
  Filter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Edit,
  Trash2,
  Play,
  Pause,
  Square,
  Eye,
  Calendar,
  User,
  MapPin,
  Clock,
  Alert<PERSON>riangle,
  CheckCircle,
} from "lucide-react"
import { format } from "date-fns"

interface MaintenanceTask {
  id: string
  title: string
  description?: string
  type: string
  priority: string
  status: string
  scheduledDate: Date
  dueDate: Date
  completedDate?: Date
  assignedTo?: string
  assignedTeam?: string
  estimatedDuration?: number
  actualDuration?: number
  estimatedCost?: number
  actualCost?: number
  isOverdue?: boolean
  isDueSoon?: boolean
  daysUntilDue?: number
  completionPercentage?: number
  asset?: {
    id: string
    name: string
    location?: string
    assetType?: {
      name: string
      icon?: string
      color?: string
    }
  }
}

interface MaintenanceTasksTableProps {
  tasks: MaintenanceTask[]
  loading?: boolean
  onUpdateTask: (taskId: string, updates: any) => void
  onDeleteTask: (taskId: string) => void
  compact?: boolean
  filters?: any
  onFiltersChange?: (filters: any) => void
  pagination?: any
  onPaginationChange?: (pagination: any) => void
}

export function MaintenanceTasksTable({
  tasks,
  loading = false,
  onUpdateTask,
  onDeleteTask,
  compact = false,
  filters,
  onFiltersChange,
  pagination,
  onPaginationChange,
}: MaintenanceTasksTableProps) {
  const [selectedTasks, setSelectedTasks] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "scheduled":
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Scheduled</Badge>
      case "in_progress":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">In Progress</Badge>
      case "completed":
        return <Badge variant="outline" className="text-green-600 border-green-600">Completed</Badge>
      case "overdue":
        return <Badge variant="destructive">Overdue</Badge>
      case "cancelled":
        return <Badge variant="secondary">Cancelled</Badge>
      case "on_hold":
        return <Badge variant="outline" className="text-gray-600 border-gray-600">On Hold</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "critical":
        return <Badge variant="destructive">Critical</Badge>
      case "high":
        return <Badge variant="destructive" className="bg-orange-500">High</Badge>
      case "medium":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Medium</Badge>
      case "low":
        return <Badge variant="secondary">Low</Badge>
      default:
        return <Badge variant="outline">{priority}</Badge>
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "preventive":
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Preventive</Badge>
      case "corrective":
        return <Badge variant="outline" className="text-orange-600 border-orange-600">Corrective</Badge>
      case "predictive":
        return <Badge variant="outline" className="text-purple-600 border-purple-600">Predictive</Badge>
      case "emergency":
        return <Badge variant="destructive">Emergency</Badge>
      case "condition_based":
        return <Badge variant="outline" className="text-green-600 border-green-600">Condition Based</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  const handleStatusChange = (taskId: string, newStatus: string) => {
    const updates: any = { status: newStatus }
    
    if (newStatus === "completed") {
      updates.completedDate = new Date()
    }
    
    onUpdateTask(taskId, updates)
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTasks(tasks.map(task => task.id))
    } else {
      setSelectedTasks([])
    }
  }

  const handleSelectTask = (taskId: string, checked: boolean) => {
    if (checked) {
      setSelectedTasks([...selectedTasks, taskId])
    } else {
      setSelectedTasks(selectedTasks.filter(id => id !== taskId))
    }
  }

  const filteredTasks = tasks.filter(task =>
    task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    task.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    task.asset?.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="h-8 w-64 bg-gray-200 rounded animate-pulse" />
          <div className="h-8 w-32 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-12 bg-gray-100 rounded animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {!compact && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 w-64"
              />
            </div>
            <Select>
              <SelectTrigger className="w-32">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Tasks</SelectItem>
                <SelectItem value="overdue">Overdue</SelectItem>
                <SelectItem value="due-soon">Due Soon</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {selectedTasks.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                {selectedTasks.length} selected
              </span>
              <Button variant="outline" size="sm">
                Bulk Actions
              </Button>
            </div>
          )}
        </div>
      )}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {!compact && (
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedTasks.length === tasks.length}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
              )}
              <TableHead>Task</TableHead>
              <TableHead>Asset</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Priority</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Scheduled</TableHead>
              <TableHead>Due</TableHead>
              {!compact && <TableHead>Assigned</TableHead>}
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTasks.map((task) => (
              <TableRow key={task.id}>
                {!compact && (
                  <TableCell>
                    <Checkbox
                      checked={selectedTasks.includes(task.id)}
                      onCheckedChange={(checked) => handleSelectTask(task.id, checked as boolean)}
                    />
                  </TableCell>
                )}
                <TableCell>
                  <div className="space-y-1">
                    <div className="font-medium">{task.title}</div>
                    {task.description && (
                      <div className="text-sm text-muted-foreground line-clamp-1">
                        {task.description}
                      </div>
                    )}
                    {task.isOverdue && (
                      <div className="flex items-center text-red-600 text-xs">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Overdue by {Math.abs(task.daysUntilDue || 0)} days
                      </div>
                    )}
                    {task.isDueSoon && (
                      <div className="flex items-center text-yellow-600 text-xs">
                        <Clock className="h-3 w-3 mr-1" />
                        Due in {task.daysUntilDue} days
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="font-medium">{task.asset?.name}</div>
                    {task.asset?.location && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <MapPin className="h-3 w-3 mr-1" />
                        {task.asset.location}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>{getTypeBadge(task.type)}</TableCell>
                <TableCell>{getPriorityBadge(task.priority)}</TableCell>
                <TableCell>{getStatusBadge(task.status)}</TableCell>
                <TableCell>
                  <div className="text-sm">
                    {format(new Date(task.scheduledDate), "MMM dd, yyyy")}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {format(new Date(task.scheduledDate), "HH:mm")}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {format(new Date(task.dueDate), "MMM dd, yyyy")}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {format(new Date(task.dueDate), "HH:mm")}
                  </div>
                </TableCell>
                {!compact && (
                  <TableCell>
                    {task.assignedTo && (
                      <div className="flex items-center text-sm">
                        <User className="h-3 w-3 mr-1" />
                        {task.assignedTo}
                      </div>
                    )}
                    {task.assignedTeam && (
                      <div className="text-xs text-muted-foreground">
                        Team: {task.assignedTeam}
                      </div>
                    )}
                  </TableCell>
                )}
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Task
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {task.status === "scheduled" && (
                        <DropdownMenuItem onClick={() => handleStatusChange(task.id, "in_progress")}>
                          <Play className="mr-2 h-4 w-4" />
                          Start Task
                        </DropdownMenuItem>
                      )}
                      {task.status === "in_progress" && (
                        <>
                          <DropdownMenuItem onClick={() => handleStatusChange(task.id, "on_hold")}>
                            <Pause className="mr-2 h-4 w-4" />
                            Pause Task
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleStatusChange(task.id, "completed")}>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Complete Task
                          </DropdownMenuItem>
                        </>
                      )}
                      <DropdownMenuSeparator />
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Task
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Maintenance Task</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete "{task.title}"? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => onDeleteTask(task.id)}>
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {!compact && pagination && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {filteredTasks.length} of {tasks.length} tasks
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPaginationChange?.({ ...pagination, page: pagination.page - 1 })}
              disabled={pagination.page <= 1}
            >
              Previous
            </Button>
            <span className="text-sm">
              Page {pagination.page} of {Math.ceil(tasks.length / pagination.limit)}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPaginationChange?.({ ...pagination, page: pagination.page + 1 })}
              disabled={pagination.page >= Math.ceil(tasks.length / pagination.limit)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
