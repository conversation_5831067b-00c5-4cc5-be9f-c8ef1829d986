"use client"

import { useState, useMemo } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart,
} from "recharts"
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Clock,
  Wrench,
  AlertTriangle,
  CheckCircle,
  Activity,
  Calendar,
  Users,
  Target,
  Zap,
} from "lucide-react"
import { format, subDays, subMonths, startOfMonth, endOfMonth, eachDayOfInterval } from "date-fns"

interface MaintenanceTask {
  id: string
  title: string
  type: string
  priority: string
  status: string
  scheduledDate: Date
  dueDate: Date
  completedDate?: Date
  estimatedCost?: number
  actualCost?: number
  estimatedDuration?: number
  actualDuration?: number
  assignedTo?: string
  asset?: {
    id: string
    name: string
    assetType?: {
      name: string
    }
  }
}

interface MaintenanceStats {
  totalTasks: number
  scheduledTasks: number
  inProgressTasks: number
  completedTasks: number
  overdueTasks: number
  upcomingTasks: number
  averageCompletionTime: number
  totalCost: number
  averageCost: number
  completionRate: number
  averageResponseTime: number
  tasksByPriority: Array<{ priority: string; count: number }>
  tasksByType: Array<{ type: string; count: number }>
  tasksByStatus: Array<{ status: string; count: number }>
}

interface MaintenanceAnalyticsProps {
  stats: MaintenanceStats | null
  tasks: MaintenanceTask[]
}

const COLORS = {
  primary: "#3b82f6",
  success: "#10b981",
  warning: "#f59e0b",
  danger: "#ef4444",
  info: "#06b6d4",
  purple: "#8b5cf6",
  orange: "#f97316",
  gray: "#6b7280",
}

const PIE_COLORS = [COLORS.primary, COLORS.success, COLORS.warning, COLORS.danger, COLORS.info, COLORS.purple]

export function MaintenanceAnalytics({ stats, tasks }: MaintenanceAnalyticsProps) {
  const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d" | "1y">("30d")
  const [selectedMetric, setSelectedMetric] = useState<"tasks" | "cost" | "duration">("tasks")

  // Calculate time-based analytics
  const timeBasedData = useMemo(() => {
    const now = new Date()
    let startDate: Date
    
    switch (timeRange) {
      case "7d":
        startDate = subDays(now, 7)
        break
      case "30d":
        startDate = subDays(now, 30)
        break
      case "90d":
        startDate = subDays(now, 90)
        break
      case "1y":
        startDate = subDays(now, 365)
        break
      default:
        startDate = subDays(now, 30)
    }

    const filteredTasks = tasks.filter(task => 
      new Date(task.scheduledDate) >= startDate
    )

    // Group tasks by date
    const tasksByDate = filteredTasks.reduce((acc, task) => {
      const dateKey = format(new Date(task.scheduledDate), "yyyy-MM-dd")
      if (!acc[dateKey]) {
        acc[dateKey] = []
      }
      acc[dateKey].push(task)
      return acc
    }, {} as Record<string, MaintenanceTask[]>)

    // Create chart data
    const chartData = eachDayOfInterval({ start: startDate, end: now }).map(date => {
      const dateKey = format(date, "yyyy-MM-dd")
      const dayTasks = tasksByDate[dateKey] || []
      
      return {
        date: format(date, "MMM dd"),
        fullDate: dateKey,
        tasks: dayTasks.length,
        completed: dayTasks.filter(t => t.status === "completed").length,
        scheduled: dayTasks.filter(t => t.status === "scheduled").length,
        inProgress: dayTasks.filter(t => t.status === "in_progress").length,
        overdue: dayTasks.filter(t => t.status === "overdue").length,
        cost: dayTasks.reduce((sum, t) => sum + (t.actualCost || t.estimatedCost || 0), 0),
        duration: dayTasks.reduce((sum, t) => sum + (t.actualDuration || t.estimatedDuration || 0), 0),
      }
    })

    return chartData
  }, [tasks, timeRange])

  // Calculate performance metrics
  const performanceMetrics = useMemo(() => {
    const completedTasks = tasks.filter(t => t.status === "completed")
    const overdueTasks = tasks.filter(t => t.status === "overdue")
    
    const onTimeCompletion = completedTasks.filter(t => 
      t.completedDate && t.dueDate && new Date(t.completedDate) <= new Date(t.dueDate)
    ).length

    const avgCompletionTime = completedTasks.reduce((sum, t) => {
      if (t.completedDate && t.scheduledDate) {
        return sum + (new Date(t.completedDate).getTime() - new Date(t.scheduledDate).getTime())
      }
      return sum
    }, 0) / completedTasks.length / (1000 * 60 * 60 * 24) // Convert to days

    const totalCost = tasks.reduce((sum, t) => sum + (t.actualCost || t.estimatedCost || 0), 0)
    const avgCost = totalCost / tasks.length

    return {
      onTimeRate: completedTasks.length > 0 ? (onTimeCompletion / completedTasks.length) * 100 : 0,
      overdueRate: tasks.length > 0 ? (overdueTasks.length / tasks.length) * 100 : 0,
      avgCompletionTime: avgCompletionTime || 0,
      totalCost,
      avgCost: avgCost || 0,
      efficiency: completedTasks.length > 0 ? (onTimeCompletion / completedTasks.length) * 100 : 0,
    }
  }, [tasks])

  // Asset type breakdown
  const assetTypeData = useMemo(() => {
    const assetTypes = tasks.reduce((acc, task) => {
      const type = task.asset?.assetType?.name || "Unknown"
      acc[type] = (acc[type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(assetTypes).map(([name, count]) => ({ name, count }))
  }, [tasks])

  // Technician performance
  const technicianData = useMemo(() => {
    const technicians = tasks.reduce((acc, task) => {
      const tech = task.assignedTo || "Unassigned"
      if (!acc[tech]) {
        acc[tech] = { name: tech, total: 0, completed: 0, overdue: 0 }
      }
      acc[tech].total++
      if (task.status === "completed") acc[tech].completed++
      if (task.status === "overdue") acc[tech].overdue++
      return acc
    }, {} as Record<string, { name: string; total: number; completed: number; overdue: number }>)

    return Object.values(technicians).map(tech => ({
      ...tech,
      completionRate: tech.total > 0 ? (tech.completed / tech.total) * 100 : 0,
    }))
  }, [tasks])

  const StatCard = ({ title, value, change, icon: Icon, color = "primary" }: {
    title: string
    value: string | number
    change?: number
    icon: any
    color?: keyof typeof COLORS
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-4 w-4 text-${color}-600`} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change !== undefined && (
          <p className={`text-xs ${change >= 0 ? "text-green-600" : "text-red-600"} flex items-center`}>
            {change >= 0 ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
            {Math.abs(change)}% from last period
          </p>
        )}
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Tasks"
          value={stats?.totalTasks || 0}
          icon={Wrench}
          color="primary"
        />
        <StatCard
          title="Completion Rate"
          value={`${performanceMetrics.onTimeRate.toFixed(1)}%`}
          icon={CheckCircle}
          color="success"
        />
        <StatCard
          title="Total Cost"
          value={`$${performanceMetrics.totalCost.toLocaleString()}`}
          icon={DollarSign}
          color="warning"
        />
        <StatCard
          title="Avg Response Time"
          value={`${performanceMetrics.avgCompletionTime.toFixed(1)} days`}
          icon={Clock}
          color="info"
        />
      </div>

      {/* Time Range Selector */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Analytics Dashboard</h3>
        <div className="flex items-center space-x-2">
          <Select value={selectedMetric} onValueChange={(value: any) => setSelectedMetric(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="tasks">Tasks</SelectItem>
              <SelectItem value="cost">Cost</SelectItem>
              <SelectItem value="duration">Duration</SelectItem>
            </SelectContent>
          </Select>
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 days</SelectItem>
              <SelectItem value="30d">30 days</SelectItem>
              <SelectItem value="90d">90 days</SelectItem>
              <SelectItem value="1y">1 year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Trend Chart */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Maintenance Trends</CardTitle>
            <CardDescription>Task completion over time</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={timeBasedData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Area
                  type="monotone"
                  dataKey="completed"
                  stackId="1"
                  stroke={COLORS.success}
                  fill={COLORS.success}
                  fillOpacity={0.6}
                />
                <Area
                  type="monotone"
                  dataKey="inProgress"
                  stackId="1"
                  stroke={COLORS.warning}
                  fill={COLORS.warning}
                  fillOpacity={0.6}
                />
                <Area
                  type="monotone"
                  dataKey="scheduled"
                  stackId="1"
                  stroke={COLORS.primary}
                  fill={COLORS.primary}
                  fillOpacity={0.6}
                />
                <Area
                  type="monotone"
                  dataKey="overdue"
                  stackId="1"
                  stroke={COLORS.danger}
                  fill={COLORS.danger}
                  fillOpacity={0.6}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Task Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Task Distribution</CardTitle>
            <CardDescription>By priority level</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={stats?.tasksByPriority || []}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                  label={({ priority, count }) => `${priority}: ${count}`}
                >
                  {(stats?.tasksByPriority || []).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Asset Type Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Asset Types</CardTitle>
            <CardDescription>Maintenance by asset category</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={assetTypeData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill={COLORS.primary} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Performance Metrics</CardTitle>
            <CardDescription>Key performance indicators</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>On-time Completion</span>
                <span>{performanceMetrics.onTimeRate.toFixed(1)}%</span>
              </div>
              <Progress value={performanceMetrics.onTimeRate} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Overdue Rate</span>
                <span>{performanceMetrics.overdueRate.toFixed(1)}%</span>
              </div>
              <Progress value={performanceMetrics.overdueRate} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Efficiency Score</span>
                <span>{performanceMetrics.efficiency.toFixed(1)}%</span>
              </div>
              <Progress value={performanceMetrics.efficiency} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Technician Performance</CardTitle>
            <CardDescription>Individual completion rates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {technicianData.slice(0, 5).map((tech, index) => (
                <div key={tech.name} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                      <User className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium text-sm">{tech.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {tech.completed}/{tech.total} completed
                      </div>
                    </div>
                  </div>
                  <Badge variant={tech.completionRate >= 80 ? "default" : "secondary"}>
                    {tech.completionRate.toFixed(0)}%
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
