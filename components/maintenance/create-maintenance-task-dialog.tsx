"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DatePicker } from "@/components/ui/date-picker"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  Plus,
  Minus,
  Calendar,
  Clock,
  User,
  Wrench,
  DollarSign,
  FileText,
  AlertTriangle,
  CheckSquare,
  X,
} from "lucide-react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { MaintenanceTaskCreateSchema } from "@/lib/schemas/maintenance"
import { z } from "zod"

interface CreateMaintenanceTaskDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: any) => void
  assets?: Array<{
    id: string
    name: string
    location?: string
    assetType?: {
      name: string
    }
  }>
  users?: Array<{
    id: string
    name: string
    email: string
  }>
}

type FormData = z.infer<typeof MaintenanceTaskCreateSchema>

export function CreateMaintenanceTaskDialog({
  open,
  onOpenChange,
  onSubmit,
  assets = [],
  users = [],
}: CreateMaintenanceTaskDialogProps) {
  const [checklistItems, setChecklistItems] = useState<Array<{
    id: string
    title: string
    description?: string
    required: boolean
  }>>([])
  const [requiredParts, setRequiredParts] = useState<Array<{
    id: string
    name: string
    quantity: number
    estimatedCost?: number
  }>>([])
  const [skillsRequired, setSkillsRequired] = useState<string[]>([])
  const [newSkill, setNewSkill] = useState("")

  const form = useForm<FormData>({
    resolver: zodResolver(MaintenanceTaskCreateSchema),
    defaultValues: {
      type: "preventive",
      priority: "medium",
      status: "scheduled",
      isRecurring: false,
      skillsRequired: [],
      tags: [],
      attachments: [],
    },
  })

  const handleSubmit = (data: FormData) => {
    const taskData = {
      ...data,
      checklistItems,
      partsUsed: requiredParts,
      skillsRequired,
    }
    onSubmit(taskData)
    form.reset()
    setChecklistItems([])
    setRequiredParts([])
    setSkillsRequired([])
  }

  const addChecklistItem = () => {
    const newItem = {
      id: `item-${Date.now()}`,
      title: "",
      description: "",
      required: false,
    }
    setChecklistItems([...checklistItems, newItem])
  }

  const updateChecklistItem = (id: string, updates: Partial<typeof checklistItems[0]>) => {
    setChecklistItems(items =>
      items.map(item => item.id === id ? { ...item, ...updates } : item)
    )
  }

  const removeChecklistItem = (id: string) => {
    setChecklistItems(items => items.filter(item => item.id !== id))
  }

  const addRequiredPart = () => {
    const newPart = {
      id: `part-${Date.now()}`,
      name: "",
      quantity: 1,
      estimatedCost: 0,
    }
    setRequiredParts([...requiredParts, newPart])
  }

  const updateRequiredPart = (id: string, updates: Partial<typeof requiredParts[0]>) => {
    setRequiredParts(parts =>
      parts.map(part => part.id === id ? { ...part, ...updates } : part)
    )
  }

  const removeRequiredPart = (id: string) => {
    setRequiredParts(parts => parts.filter(part => part.id !== id))
  }

  const addSkill = () => {
    if (newSkill.trim() && !skillsRequired.includes(newSkill.trim())) {
      setSkillsRequired([...skillsRequired, newSkill.trim()])
      setNewSkill("")
    }
  }

  const removeSkill = (skill: string) => {
    setSkillsRequired(skills => skills.filter(s => s !== skill))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Create Maintenance Task</DialogTitle>
          <DialogDescription>
            Create a new maintenance task with detailed specifications
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="scheduling">Scheduling</TabsTrigger>
                <TabsTrigger value="checklist">Checklist</TabsTrigger>
                <TabsTrigger value="resources">Resources</TabsTrigger>
              </TabsList>

              <ScrollArea className="h-[500px] mt-4">
                <TabsContent value="basic" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Task Title</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter task title" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="assetId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Asset</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select asset" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {assets.map((asset) => (
                                <SelectItem key={asset.id} value={asset.id}>
                                  {asset.name} {asset.location && `(${asset.location})`}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe the maintenance task"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Type</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="preventive">Preventive</SelectItem>
                              <SelectItem value="corrective">Corrective</SelectItem>
                              <SelectItem value="predictive">Predictive</SelectItem>
                              <SelectItem value="emergency">Emergency</SelectItem>
                              <SelectItem value="condition_based">Condition Based</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="priority"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Priority</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="low">Low</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="high">High</SelectItem>
                              <SelectItem value="critical">Critical</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Status</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="scheduled">Scheduled</SelectItem>
                              <SelectItem value="in_progress">In Progress</SelectItem>
                              <SelectItem value="on_hold">On Hold</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="instructions"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Instructions</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Detailed maintenance instructions"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="scheduling" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="scheduledDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Scheduled Date</FormLabel>
                          <FormControl>
                            <DatePicker
                              date={field.value}
                              onDateChange={field.onChange}
                              placeholder="Select scheduled date"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dueDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Due Date</FormLabel>
                          <FormControl>
                            <DatePicker
                              date={field.value}
                              onDateChange={field.onChange}
                              placeholder="Select due date"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="estimatedDuration"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estimated Duration (minutes)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="120"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="estimatedCost"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estimated Cost ($)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="100.00"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="assignedTo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Assigned To</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select technician" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {users.map((user) => (
                                <SelectItem key={user.id} value={user.id}>
                                  {user.name} ({user.email})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="assignedTeam"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Assigned Team</FormLabel>
                          <FormControl>
                            <Input placeholder="Team name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="isRecurring"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Recurring Task</FormLabel>
                          <FormDescription>
                            This task will repeat based on a schedule
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="checklist" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Task Checklist</h3>
                    <Button type="button" variant="outline" size="sm" onClick={addChecklistItem}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Item
                    </Button>
                  </div>

                  <div className="space-y-3">
                    {checklistItems.map((item, index) => (
                      <Card key={item.id}>
                        <CardContent className="p-4">
                          <div className="flex items-start space-x-3">
                            <Checkbox
                              checked={item.required}
                              onCheckedChange={(checked) =>
                                updateChecklistItem(item.id, { required: checked as boolean })
                              }
                            />
                            <div className="flex-1 space-y-2">
                              <Input
                                placeholder="Checklist item title"
                                value={item.title}
                                onChange={(e) =>
                                  updateChecklistItem(item.id, { title: e.target.value })
                                }
                              />
                              <Textarea
                                placeholder="Description (optional)"
                                value={item.description}
                                onChange={(e) =>
                                  updateChecklistItem(item.id, { description: e.target.value })
                                }
                                className="resize-none"
                                rows={2}
                              />
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeChecklistItem(item.id)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {checklistItems.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No checklist items added yet
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="resources" className="space-y-4">
                  {/* Required Skills */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-medium">Required Skills</h3>
                    <div className="flex items-center space-x-2">
                      <Input
                        placeholder="Add skill requirement"
                        value={newSkill}
                        onChange={(e) => setNewSkill(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addSkill())}
                      />
                      <Button type="button" variant="outline" onClick={addSkill}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {skillsRequired.map((skill) => (
                        <Badge key={skill} variant="secondary" className="flex items-center gap-1">
                          {skill}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removeSkill(skill)}
                          />
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  {/* Required Parts */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">Required Parts</h3>
                      <Button type="button" variant="outline" size="sm" onClick={addRequiredPart}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Part
                      </Button>
                    </div>

                    <div className="space-y-3">
                      {requiredParts.map((part) => (
                        <Card key={part.id}>
                          <CardContent className="p-4">
                            <div className="grid grid-cols-4 gap-3 items-center">
                              <Input
                                placeholder="Part name"
                                value={part.name}
                                onChange={(e) =>
                                  updateRequiredPart(part.id, { name: e.target.value })
                                }
                              />
                              <Input
                                type="number"
                                placeholder="Qty"
                                value={part.quantity}
                                onChange={(e) =>
                                  updateRequiredPart(part.id, { quantity: parseInt(e.target.value) || 1 })
                                }
                              />
                              <Input
                                type="number"
                                step="0.01"
                                placeholder="Cost"
                                value={part.estimatedCost}
                                onChange={(e) =>
                                  updateRequiredPart(part.id, { estimatedCost: parseFloat(e.target.value) || 0 })
                                }
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeRequiredPart(part.id)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>

                    {requiredParts.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        No parts specified
                      </div>
                    )}
                  </div>

                  <Separator />

                  {/* Safety Notes */}
                  <FormField
                    control={form.control}
                    name="safetyNotes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Safety Notes</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Important safety considerations"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>
              </ScrollArea>
            </Tabs>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit">Create Task</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
