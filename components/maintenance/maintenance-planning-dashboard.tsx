"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { toast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Calendar,
  Clock,
  <PERSON>,
  Al<PERSON><PERSON><PERSON>gle,
  <PERSON><PERSON><PERSON>cle,
  TrendingUp,
  BarChart3,
  Settings,
  Zap,
  Target,
  Activity,
  MapPin,
  Wrench,
  Plus,
  RefreshCw,
  Download,
  Upload,
  Share,
  Filter,
} from "lucide-react"
import { format, addDays, startOfWeek, endOfWeek } from "date-fns"
import { MaintenanceCalendar } from "./maintenance-calendar"

interface PlanningMetrics {
  totalScheduled: number
  upcomingWeek: number
  overdue: number
  conflicts: number
  resourceUtilization: number
  averageLeadTime: number
  completionRate: number
  costEfficiency: number
}

interface ResourceLoad {
  resourceId: string
  resourceName: string
  resourceType: string
  currentLoad: number
  maxCapacity: number
  utilization: number
  upcomingTasks: number
  conflicts: number
}

interface SchedulingConflict {
  id: string
  type: "resource_conflict" | "time_conflict" | "location_conflict"
  severity: "low" | "medium" | "high" | "critical"
  description: string
  affectedTasks: string[]
  suggestedResolution: string
  autoResolvable: boolean
}

export function MaintenancePlanningDashboard() {
  const [metrics, setMetrics] = useState<PlanningMetrics | null>(null)
  const [resourceLoads, setResourceLoads] = useState<ResourceLoad[]>([])
  const [conflicts, setConflicts] = useState<SchedulingConflict[]>([])
  const [selectedTimeRange, setSelectedTimeRange] = useState("week")
  const [loading, setLoading] = useState(true)
  const [optimizationDialogOpen, setOptimizationDialogOpen] = useState(false)
  const [selectedConflict, setSelectedConflict] = useState<SchedulingConflict | null>(null)

  useEffect(() => {
    fetchPlanningData()
  }, [selectedTimeRange])

  const fetchPlanningData = async () => {
    try {
      setLoading(true)
      
      // Fetch planning metrics
      const metricsResponse = await fetch(`/api/maintenance/planning/metrics?range=${selectedTimeRange}`)
      const metricsData = await metricsResponse.json()
      
      // Fetch resource loads
      const resourceResponse = await fetch(`/api/maintenance/planning/resources?range=${selectedTimeRange}`)
      const resourceData = await resourceResponse.json()
      
      // Fetch conflicts
      const conflictsResponse = await fetch(`/api/maintenance/planning/conflicts`)
      const conflictsData = await conflictsResponse.json()

      if (metricsData.success) {
        setMetrics(metricsData.data)
      }
      
      if (resourceData.success) {
        setResourceLoads(resourceData.data)
      }
      
      if (conflictsData.success) {
        setConflicts(conflictsData.data)
      }
    } catch (error) {
      console.error("Error fetching planning data:", error)
      // Mock data for demonstration
      setMetrics({
        totalScheduled: 45,
        upcomingWeek: 12,
        overdue: 3,
        conflicts: 2,
        resourceUtilization: 78.5,
        averageLeadTime: 5.2,
        completionRate: 92.3,
        costEfficiency: 87.1,
      })
      
      setResourceLoads([
        {
          resourceId: "tech-1",
          resourceName: "John Smith",
          resourceType: "technician",
          currentLoad: 32,
          maxCapacity: 40,
          utilization: 80,
          upcomingTasks: 8,
          conflicts: 1,
        },
        {
          resourceId: "tech-2",
          resourceName: "Sarah Johnson",
          resourceType: "technician",
          currentLoad: 28,
          maxCapacity: 40,
          utilization: 70,
          upcomingTasks: 6,
          conflicts: 0,
        },
      ])
      
      setConflicts([
        {
          id: "conflict-1",
          type: "resource_conflict",
          severity: "high",
          description: "John Smith has overlapping tasks on March 15th",
          affectedTasks: ["task-1", "task-2"],
          suggestedResolution: "Reschedule task-2 to March 16th or assign to Sarah Johnson",
          autoResolvable: true,
        },
      ])
    } finally {
      setLoading(false)
    }
  }

  const handleOptimizeSchedule = async () => {
    try {
      const response = await fetch("/api/maintenance/planning/optimize", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          timeRange: selectedTimeRange,
          objectives: ["minimize_conflicts", "balance_workload"],
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Schedule Optimized",
          description: `${data.data.optimizedTasks} tasks were rescheduled to improve efficiency`,
        })
        fetchPlanningData()
      } else {
        toast({
          title: "Optimization Failed",
          description: data.error || "Failed to optimize schedule",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error optimizing schedule:", error)
      toast({
        title: "Error",
        description: "Failed to optimize schedule",
        variant: "destructive",
      })
    }
  }

  const handleResolveConflict = async (conflictId: string, resolution: string) => {
    try {
      const response = await fetch(`/api/maintenance/planning/conflicts/${conflictId}/resolve`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ resolution }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Conflict Resolved",
          description: "The scheduling conflict has been resolved",
        })
        fetchPlanningData()
      } else {
        toast({
          title: "Resolution Failed",
          description: data.error || "Failed to resolve conflict",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error resolving conflict:", error)
      toast({
        title: "Error",
        description: "Failed to resolve conflict",
        variant: "destructive",
      })
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical": return "text-red-600 bg-red-50 border-red-200"
      case "high": return "text-orange-600 bg-orange-50 border-orange-200"
      case "medium": return "text-yellow-600 bg-yellow-50 border-yellow-200"
      case "low": return "text-green-600 bg-green-50 border-green-200"
      default: return "text-gray-600 bg-gray-50 border-gray-200"
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="space-y-3 animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4" />
                  <div className="h-8 bg-gray-200 rounded w-1/2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Maintenance Planning</h2>
          <p className="text-muted-foreground">
            Optimize schedules, manage resources, and resolve conflicts
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={fetchPlanningData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          
          <Button onClick={() => setOptimizationDialogOpen(true)}>
            <Zap className="h-4 w-4 mr-2" />
            Optimize Schedule
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Scheduled</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.totalScheduled}</div>
            <p className="text-xs text-muted-foreground">
              {metrics?.upcomingWeek} this week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resource Utilization</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.resourceUtilization.toFixed(1)}%</div>
            <Progress value={metrics?.resourceUtilization} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Conflicts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{metrics?.conflicts}</div>
            <p className="text-xs text-muted-foreground">
              {conflicts.filter(c => c.autoResolvable).length} auto-resolvable
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{metrics?.completionRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {metrics?.overdue} overdue tasks
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="calendar" className="w-full">
        <TabsList>
          <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          <TabsTrigger value="resources">Resource Management</TabsTrigger>
          <TabsTrigger value="conflicts">Conflict Resolution</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="calendar" className="space-y-4">
          <MaintenanceCalendar
            tasks={[]} // This would be populated with actual tasks
            onTaskUpdate={() => {}}
            onTaskCreate={() => {}}
            onTaskDelete={() => {}}
            enableDragDrop={true}
            enableResourcePlanning={true}
            enableConflictDetection={true}
          />
        </TabsContent>

        <TabsContent value="resources" className="space-y-4">
          <div className="grid gap-4">
            {resourceLoads.map((resource) => (
              <Card key={resource.resourceId}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{resource.resourceName}</CardTitle>
                      <CardDescription>{resource.resourceType}</CardDescription>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold">
                        {resource.utilization.toFixed(1)}%
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {resource.currentLoad}h / {resource.maxCapacity}h
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Progress value={resource.utilization} className="h-2" />
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <Activity className="h-4 w-4 text-muted-foreground" />
                        <span>{resource.upcomingTasks} upcoming tasks</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                        <span>{resource.conflicts} conflicts</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Target className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {resource.utilization > 90 ? "Overloaded" :
                           resource.utilization > 75 ? "High Load" :
                           resource.utilization > 50 ? "Optimal" : "Available"}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="conflicts" className="space-y-4">
          <div className="space-y-4">
            {conflicts.map((conflict) => (
              <Card key={conflict.id} className={`border-l-4 ${getSeverityColor(conflict.severity)}`}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{conflict.type.replace('_', ' ')}</CardTitle>
                      <CardDescription>{conflict.description}</CardDescription>
                    </div>
                    <Badge variant="outline" className={getSeverityColor(conflict.severity)}>
                      {conflict.severity}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium">Suggested Resolution:</Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        {conflict.suggestedResolution}
                      </p>
                    </div>
                    
                    <div className="flex space-x-2">
                      {conflict.autoResolvable && (
                        <Button 
                          size="sm"
                          onClick={() => handleResolveConflict(conflict.id, "auto")}
                        >
                          Auto Resolve
                        </Button>
                      )}
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setSelectedConflict(conflict)}
                      >
                        Manual Resolution
                      </Button>
                      <Button variant="ghost" size="sm">
                        Ignore
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Planning Efficiency</CardTitle>
                <CardDescription>Key performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Average Lead Time</span>
                    <span>{metrics?.averageLeadTime.toFixed(1)} days</span>
                  </div>
                  <Progress value={(metrics?.averageLeadTime || 0) * 10} className="h-2" />
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Cost Efficiency</span>
                    <span>{metrics?.costEfficiency.toFixed(1)}%</span>
                  </div>
                  <Progress value={metrics?.costEfficiency} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Resource Distribution</CardTitle>
                <CardDescription>Workload across team members</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {resourceLoads.map((resource) => (
                    <div key={resource.resourceId} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{resource.resourceName}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${resource.utilization}%` }}
                          />
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {resource.utilization.toFixed(0)}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Optimization Dialog */}
      <Dialog open={optimizationDialogOpen} onOpenChange={setOptimizationDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Optimize Schedule</DialogTitle>
            <DialogDescription>
              Automatically optimize the maintenance schedule to improve efficiency and resolve conflicts.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label>Optimization Goals</Label>
              <div className="space-y-2 mt-2">
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="minimize-conflicts" defaultChecked />
                  <label htmlFor="minimize-conflicts" className="text-sm">Minimize conflicts</label>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="balance-workload" defaultChecked />
                  <label htmlFor="balance-workload" className="text-sm">Balance workload</label>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="minimize-cost" />
                  <label htmlFor="minimize-cost" className="text-sm">Minimize cost</label>
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setOptimizationDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleOptimizeSchedule}>
              Optimize Schedule
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
