"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Bell,
  BellRing,
  Clock,
  AlertTriangle,
  CheckCircle,
  X,
  MoreHorizontal,
  Eye,
  Trash2,
  Settings,
} from "lucide-react"
import { format, formatDistanceToNow } from "date-fns"
import { toast } from "@/components/ui/use-toast"

interface MaintenanceNotification {
  id: string
  type: string
  title: string
  message: string
  status: string
  scheduledFor: Date
  readAt?: Date
  task?: {
    id: string
    title: string
    priority: string
    asset?: {
      name: string
    }
  }
}

export function MaintenanceNotifications() {
  const [notifications, setNotifications] = useState<MaintenanceNotification[]>([])
  const [loading, setLoading] = useState(true)
  const [unreadCount, setUnreadCount] = useState(0)

  useEffect(() => {
    fetchNotifications()
  }, [])

  const fetchNotifications = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/maintenance/notifications?unreadOnly=false&limit=20")
      const data = await response.json()

      if (data.success) {
        setNotifications(data.data.notifications)
        setUnreadCount(data.data.summary.unreadCount)
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch notifications",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error fetching notifications:", error)
      toast({
        title: "Error",
        description: "Failed to fetch notifications",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch("/api/maintenance/notifications", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: notificationId,
          readAt: new Date(),
          status: "read",
        }),
      })

      const data = await response.json()

      if (data.success) {
        setNotifications(notifications =>
          notifications.map(notification =>
            notification.id === notificationId
              ? { ...notification, readAt: new Date(), status: "read" }
              : notification
          )
        )
        setUnreadCount(count => Math.max(0, count - 1))
      }
    } catch (error) {
      console.error("Error marking notification as read:", error)
    }
  }

  const markAllAsRead = async () => {
    try {
      const unreadIds = notifications
        .filter(n => !n.readAt && n.status !== "dismissed")
        .map(n => n.id)

      if (unreadIds.length === 0) return

      const response = await fetch("/api/maintenance/notifications", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ids: unreadIds,
          action: "mark_read",
        }),
      })

      const data = await response.json()

      if (data.success) {
        setNotifications(notifications =>
          notifications.map(notification =>
            unreadIds.includes(notification.id)
              ? { ...notification, readAt: new Date(), status: "read" }
              : notification
          )
        )
        setUnreadCount(0)
        toast({
          title: "Success",
          description: "All notifications marked as read",
        })
      }
    } catch (error) {
      console.error("Error marking all as read:", error)
      toast({
        title: "Error",
        description: "Failed to mark notifications as read",
        variant: "destructive",
      })
    }
  }

  const dismissNotification = async (notificationId: string) => {
    try {
      const response = await fetch("/api/maintenance/notifications", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: notificationId,
          dismissedAt: new Date(),
          status: "dismissed",
        }),
      })

      const data = await response.json()

      if (data.success) {
        setNotifications(notifications =>
          notifications.filter(notification => notification.id !== notificationId)
        )
        toast({
          title: "Success",
          description: "Notification dismissed",
        })
      }
    } catch (error) {
      console.error("Error dismissing notification:", error)
      toast({
        title: "Error",
        description: "Failed to dismiss notification",
        variant: "destructive",
      })
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "due_soon":
        return <Clock className="h-4 w-4 text-yellow-600" />
      case "overdue":
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "assigned":
        return <Bell className="h-4 w-4 text-blue-600" />
      default:
        return <Bell className="h-4 w-4 text-gray-600" />
    }
  }

  const getNotificationBadge = (type: string) => {
    switch (type) {
      case "due_soon":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Due Soon</Badge>
      case "overdue":
        return <Badge variant="destructive">Overdue</Badge>
      case "completed":
        return <Badge variant="outline" className="text-green-600 border-green-600">Completed</Badge>
      case "assigned":
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Assigned</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-start space-x-3 p-3 rounded-lg border animate-pulse">
            <div className="w-8 h-8 bg-gray-200 rounded-full" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4" />
              <div className="h-3 bg-gray-200 rounded w-1/2" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <BellRing className="h-5 w-5" />
          <span className="font-medium">Notifications</span>
          {unreadCount > 0 && (
            <Badge variant="destructive" className="text-xs">
              {unreadCount}
            </Badge>
          )}
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={markAllAsRead} disabled={unreadCount === 0}>
              <CheckCircle className="mr-2 h-4 w-4" />
              Mark All as Read
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              Notification Settings
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Notifications List */}
      <ScrollArea className="h-[400px]">
        <div className="space-y-2">
          {notifications.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No notifications</p>
            </div>
          ) : (
            notifications.map((notification, index) => (
              <div key={notification.id}>
                <div
                  className={`
                    flex items-start space-x-3 p-3 rounded-lg border cursor-pointer transition-colors
                    ${!notification.readAt && notification.status !== "dismissed" 
                      ? "bg-blue-50 border-blue-200 hover:bg-blue-100" 
                      : "hover:bg-gray-50"
                    }
                  `}
                  onClick={() => !notification.readAt && markAsRead(notification.id)}
                >
                  <div className="flex-shrink-0 mt-1">
                    {getNotificationIcon(notification.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className={`text-sm font-medium ${
                          !notification.readAt && notification.status !== "dismissed" 
                            ? "text-gray-900" 
                            : "text-gray-700"
                        }`}>
                          {notification.title}
                        </p>
                        <p className="text-sm text-gray-600 mt-1">
                          {notification.message}
                        </p>
                        
                        {notification.task && (
                          <div className="flex items-center space-x-2 mt-2">
                            <span className="text-xs text-gray-500">
                              Task: {notification.task.title}
                            </span>
                            {notification.task.asset && (
                              <span className="text-xs text-gray-500">
                                • Asset: {notification.task.asset.name}
                              </span>
                            )}
                          </div>
                        )}
                        
                        <div className="flex items-center space-x-2 mt-2">
                          {getNotificationBadge(notification.type)}
                          <span className="text-xs text-gray-500">
                            {formatDistanceToNow(new Date(notification.scheduledFor), { addSuffix: true })}
                          </span>
                        </div>
                      </div>
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {!notification.readAt && notification.status !== "dismissed" && (
                            <DropdownMenuItem onClick={() => markAsRead(notification.id)}>
                              <Eye className="mr-2 h-4 w-4" />
                              Mark as Read
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem onClick={() => dismissNotification(notification.id)}>
                            <X className="mr-2 h-4 w-4" />
                            Dismiss
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
                
                {index < notifications.length - 1 && <Separator className="my-2" />}
              </div>
            ))
          )}
        </div>
      </ScrollArea>

      {/* Footer */}
      {notifications.length > 0 && (
        <div className="text-center">
          <Button variant="ghost" size="sm" onClick={fetchNotifications}>
            Refresh
          </Button>
        </div>
      )}
    </div>
  )
}
