"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  Brain,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Target,
  Activity,
  BarChart3,
  Lightbulb,
  Wrench,
  Calendar,
  DollarSign,
} from "lucide-react"
import { format, addDays } from "date-fns"
import { toast } from "@/components/ui/use-toast"

interface PredictiveInsight {
  id: string
  assetId: string
  assetName: string
  predictionType: string
  predictedValue: number
  confidence: number
  validUntil: Date
  recommendation: string
  priority: "low" | "medium" | "high" | "critical"
  estimatedCost?: number
  suggestedDate?: Date
}

interface MaintenanceRecommendation {
  id: string
  assetId: string
  assetName: string
  recommendationType: string
  priority: "low" | "medium" | "high" | "critical"
  reasoning: string
  suggestedDate?: Date
  estimatedCost?: number
  confidence: number
}

export function PredictiveMaintenancePanel() {
  const [insights, setInsights] = useState<PredictiveInsight[]>([])
  const [recommendations, setRecommendations] = useState<MaintenanceRecommendation[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedAsset, setSelectedAsset] = useState<string | null>(null)

  useEffect(() => {
    fetchPredictiveData()
  }, [])

  const fetchPredictiveData = async () => {
    try {
      setLoading(true)
      
      // Fetch predictive insights
      const insightsResponse = await fetch("/api/maintenance/predictions")
      const insightsData = await insightsResponse.json()
      
      // Fetch recommendations
      const recommendationsResponse = await fetch("/api/maintenance/recommendations")
      const recommendationsData = await recommendationsResponse.json()

      if (insightsData.success) {
        setInsights(insightsData.data || [])
      }
      
      if (recommendationsData.success) {
        setRecommendations(recommendationsData.data || [])
      }
    } catch (error) {
      console.error("Error fetching predictive data:", error)
      // Mock data for demonstration
      setInsights([
        {
          id: "1",
          assetId: "asset-1",
          assetName: "HVAC Unit #1",
          predictionType: "failure_probability",
          predictedValue: 0.75,
          confidence: 0.85,
          validUntil: addDays(new Date(), 30),
          recommendation: "Schedule preventive maintenance within 7 days",
          priority: "high",
          estimatedCost: 1200,
          suggestedDate: addDays(new Date(), 7),
        },
        {
          id: "2",
          assetId: "asset-2",
          assetName: "Generator #2",
          predictionType: "remaining_useful_life",
          predictedValue: 45,
          confidence: 0.92,
          validUntil: addDays(new Date(), 60),
          recommendation: "Plan replacement in 45 days",
          priority: "medium",
          estimatedCost: 15000,
          suggestedDate: addDays(new Date(), 45),
        },
        {
          id: "3",
          assetId: "asset-3",
          assetName: "Pump #3",
          predictionType: "condition_score",
          predictedValue: 0.3,
          confidence: 0.78,
          validUntil: addDays(new Date(), 14),
          recommendation: "Immediate inspection required",
          priority: "critical",
          estimatedCost: 500,
          suggestedDate: addDays(new Date(), 1),
        },
      ])
      
      setRecommendations([
        {
          id: "1",
          assetId: "asset-1",
          assetName: "HVAC Unit #1",
          recommendationType: "schedule_maintenance",
          priority: "high",
          reasoning: "High failure probability detected based on usage patterns",
          suggestedDate: addDays(new Date(), 7),
          estimatedCost: 1200,
          confidence: 0.85,
        },
        {
          id: "2",
          assetId: "asset-4",
          assetName: "Conveyor Belt #1",
          recommendationType: "replace_part",
          priority: "medium",
          reasoning: "Belt wear exceeds normal parameters",
          suggestedDate: addDays(new Date(), 14),
          estimatedCost: 800,
          confidence: 0.72,
        },
      ])
    } finally {
      setLoading(false)
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical": return "text-red-600 bg-red-50 border-red-200"
      case "high": return "text-orange-600 bg-orange-50 border-orange-200"
      case "medium": return "text-yellow-600 bg-yellow-50 border-yellow-200"
      case "low": return "text-green-600 bg-green-50 border-green-200"
      default: return "text-gray-600 bg-gray-50 border-gray-200"
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "critical": return <AlertTriangle className="h-4 w-4" />
      case "high": return <TrendingUp className="h-4 w-4" />
      case "medium": return <Activity className="h-4 w-4" />
      case "low": return <CheckCircle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const formatPredictionValue = (type: string, value: number) => {
    switch (type) {
      case "failure_probability":
        return `${(value * 100).toFixed(1)}%`
      case "remaining_useful_life":
        return `${value} days`
      case "condition_score":
        return `${(value * 100).toFixed(1)}%`
      default:
        return value.toString()
    }
  }

  const handleAcceptRecommendation = async (recommendationId: string) => {
    try {
      const recommendation = recommendations.find(r => r.id === recommendationId)
      if (!recommendation) return

      // Create maintenance task based on recommendation
      const taskData = {
        title: `${recommendation.recommendationType.replace('_', ' ')} - ${recommendation.assetName}`,
        description: recommendation.reasoning,
        assetId: recommendation.assetId,
        type: "predictive",
        priority: recommendation.priority,
        scheduledDate: recommendation.suggestedDate || addDays(new Date(), 7),
        dueDate: addDays(recommendation.suggestedDate || new Date(), 14),
        estimatedCost: recommendation.estimatedCost,
      }

      const response = await fetch("/api/maintenance/tasks", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(taskData),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: "Maintenance task created from recommendation",
        })
        // Remove the accepted recommendation
        setRecommendations(recs => recs.filter(r => r.id !== recommendationId))
      } else {
        toast({
          title: "Error",
          description: "Failed to create maintenance task",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error accepting recommendation:", error)
      toast({
        title: "Error",
        description: "Failed to create maintenance task",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid gap-4 md:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="space-y-3 animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4" />
                  <div className="h-8 bg-gray-200 rounded w-1/2" />
                  <div className="h-3 bg-gray-200 rounded w-full" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Predictions</CardTitle>
            <Brain className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{insights.length}</div>
            <p className="text-xs text-muted-foreground">
              AI-powered insights
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Priority</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {insights.filter(i => i.priority === "critical" || i.priority === "high").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Require immediate attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recommendations</CardTitle>
            <Lightbulb className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{recommendations.length}</div>
            <p className="text-xs text-muted-foreground">
              Actionable suggestions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="insights" className="w-full">
        <TabsList>
          <TabsTrigger value="insights">Predictive Insights</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="models">AI Models</TabsTrigger>
        </TabsList>

        <TabsContent value="insights" className="space-y-4">
          <div className="grid gap-4">
            {insights.map((insight) => (
              <Card key={insight.id} className={`border-l-4 ${getPriorityColor(insight.priority)}`}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getPriorityIcon(insight.priority)}
                      <CardTitle className="text-lg">{insight.assetName}</CardTitle>
                      <Badge variant="outline" className={getPriorityColor(insight.priority)}>
                        {insight.priority}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold">
                        {formatPredictionValue(insight.predictionType, insight.predictedValue)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {insight.predictionType.replace('_', ' ')}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Confidence</span>
                        <span>{(insight.confidence * 100).toFixed(1)}%</span>
                      </div>
                      <Progress value={insight.confidence * 100} className="h-2" />
                    </div>
                    
                    <p className="text-sm text-gray-700">{insight.recommendation}</p>
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      {insight.suggestedDate && (
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>{format(insight.suggestedDate, "MMM dd")}</span>
                        </div>
                      )}
                      {insight.estimatedCost && (
                        <div className="flex items-center space-x-1">
                          <DollarSign className="h-4 w-4 text-muted-foreground" />
                          <span>${insight.estimatedCost.toLocaleString()}</span>
                        </div>
                      )}
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>Valid until {format(insight.validUntil, "MMM dd")}</span>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button size="sm" onClick={() => handleAcceptRecommendation(insight.id)}>
                        <Wrench className="h-4 w-4 mr-2" />
                        Schedule Maintenance
                      </Button>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <div className="space-y-4">
            {recommendations.map((rec) => (
              <Card key={rec.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Lightbulb className="h-5 w-5 text-yellow-600" />
                      <CardTitle className="text-lg">{rec.assetName}</CardTitle>
                      <Badge variant="outline" className={getPriorityColor(rec.priority)}>
                        {rec.priority}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {(rec.confidence * 100).toFixed(1)}% confidence
                    </div>
                  </div>
                  <CardDescription>{rec.recommendationType.replace('_', ' ')}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <p className="text-sm text-gray-700">{rec.reasoning}</p>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      {rec.suggestedDate && (
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>Suggested: {format(rec.suggestedDate, "MMM dd, yyyy")}</span>
                        </div>
                      )}
                      {rec.estimatedCost && (
                        <div className="flex items-center space-x-1">
                          <DollarSign className="h-4 w-4 text-muted-foreground" />
                          <span>Est. Cost: ${rec.estimatedCost.toLocaleString()}</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button size="sm" onClick={() => handleAcceptRecommendation(rec.id)}>
                        Accept Recommendation
                      </Button>
                      <Button variant="outline" size="sm">
                        Modify
                      </Button>
                      <Button variant="ghost" size="sm">
                        Dismiss
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="models" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>AI Model Performance</CardTitle>
              <CardDescription>
                Overview of predictive maintenance models and their accuracy
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Failure Prediction Model</span>
                      <span>87.5% accuracy</span>
                    </div>
                    <Progress value={87.5} className="h-2" />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Condition Assessment Model</span>
                      <span>92.1% accuracy</span>
                    </div>
                    <Progress value={92.1} className="h-2" />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Remaining Life Model</span>
                      <span>78.3% accuracy</span>
                    </div>
                    <Progress value={78.3} className="h-2" />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Anomaly Detection Model</span>
                      <span>94.7% accuracy</span>
                    </div>
                    <Progress value={94.7} className="h-2" />
                  </div>
                </div>
                
                <Separator />
                
                <div className="text-center">
                  <Button variant="outline">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    View Detailed Analytics
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
