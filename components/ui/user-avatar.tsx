"use client";

import React, { useState, useCallback } from "react";
import { cn } from "@/lib/utils";
import { User, Loader2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";

export interface UserAvatarProps {
  src?: string;
  alt?: string;
  name?: string;
  initials?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  shape?: 'circle' | 'square' | 'rounded';
  status?: 'online' | 'offline' | 'away' | 'busy' | 'invisible';
  showStatus?: boolean;
  loading?: boolean;
  fallbackIcon?: React.ComponentType<any>;
  className?: string;
  imageClassName?: string;
  statusClassName?: string;
  onClick?: () => void;
  onImageError?: () => void;
  onImageLoad?: () => void;
  role?: string;
  showRole?: boolean;
  rolePosition?: 'top' | 'bottom' | 'overlay';
}

const sizeClasses = {
  xs: 'h-6 w-6 text-xs',
  sm: 'h-8 w-8 text-sm',
  md: 'h-10 w-10 text-base',
  lg: 'h-12 w-12 text-lg',
  xl: 'h-16 w-16 text-xl',
  '2xl': 'h-20 w-20 text-2xl'
};

const shapeClasses = {
  circle: 'rounded-full',
  square: 'rounded-none',
  rounded: 'rounded-lg'
};

const statusColors = {
  online: 'bg-green-500',
  offline: 'bg-gray-400',
  away: 'bg-yellow-500',
  busy: 'bg-red-500',
  invisible: 'bg-gray-300'
};

const statusSizes = {
  xs: 'h-2 w-2',
  sm: 'h-2.5 w-2.5',
  md: 'h-3 w-3',
  lg: 'h-3.5 w-3.5',
  xl: 'h-4 w-4',
  '2xl': 'h-5 w-5'
};

const roleColors = {
  admin: 'bg-purple-100 text-purple-800 border-purple-200',
  manager: 'bg-blue-100 text-blue-800 border-blue-200',
  user: 'bg-green-100 text-green-800 border-green-200',
  client: 'bg-orange-100 text-orange-800 border-orange-200',
  technician: 'bg-cyan-100 text-cyan-800 border-cyan-200',
  guest: 'bg-gray-100 text-gray-800 border-gray-200'
};

export function UserAvatar({
  src,
  alt,
  name,
  initials,
  size = 'md',
  shape = 'circle',
  status,
  showStatus = false,
  loading = false,
  fallbackIcon: FallbackIcon = User,
  className,
  imageClassName,
  statusClassName,
  onClick,
  onImageError,
  onImageLoad,
  role,
  showRole = false,
  rolePosition = 'bottom',
  ...props
}: UserAvatarProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(!!src);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setImageLoading(false);
    onImageError?.();
  }, [onImageError]);

  const handleImageLoad = useCallback(() => {
    setImageLoading(false);
    onImageLoad?.();
  }, [onImageLoad]);

  // Generate initials from name if not provided
  const displayInitials = initials || (name ? 
    name.split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
    : '');

  const avatarContent = () => {
    if (loading || imageLoading) {
      return (
        <div className="flex items-center justify-center h-full w-full">
          <Loader2 className="h-1/2 w-1/2 animate-spin text-muted-foreground" />
        </div>
      );
    }

    if (src && !imageError) {
      return (
        <img
          src={src}
          alt={alt || name || 'User avatar'}
          className={cn(
            'h-full w-full object-cover',
            shapeClasses[shape],
            imageClassName
          )}
          onError={handleImageError}
          onLoad={handleImageLoad}
        />
      );
    }

    if (displayInitials) {
      return (
        <div className="flex items-center justify-center h-full w-full bg-muted text-muted-foreground font-medium">
          {displayInitials}
        </div>
      );
    }

    return (
      <div className="flex items-center justify-center h-full w-full bg-muted text-muted-foreground">
        <FallbackIcon className="h-1/2 w-1/2" />
      </div>
    );
  };

  const statusIndicator = showStatus && status && (
    <div
      className={cn(
        'absolute -bottom-0.5 -right-0.5 rounded-full border-2 border-background',
        statusColors[status],
        statusSizes[size],
        statusClassName
      )}
      title={`Status: ${status}`}
    />
  );

  const roleIndicator = showRole && role && (
    <Badge
      variant="secondary"
      className={cn(
        'absolute text-xs px-1 py-0 h-auto min-h-0 font-medium border',
        roleColors[role as keyof typeof roleColors] || roleColors.guest,
        {
          '-top-1 left-1/2 -translate-x-1/2': rolePosition === 'top',
          '-bottom-1 left-1/2 -translate-x-1/2': rolePosition === 'bottom',
          'top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-black/50 text-white border-transparent': rolePosition === 'overlay'
        }
      )}
    >
      {role}
    </Badge>
  );

  return (
    <div className="relative inline-block">
      <div
        className={cn(
          'relative inline-flex items-center justify-center overflow-hidden bg-muted',
          sizeClasses[size],
          shapeClasses[shape],
          onClick && 'cursor-pointer hover:opacity-80 transition-opacity',
          className
        )}
        onClick={onClick}
        {...props}
      >
        {avatarContent()}
        {statusIndicator}
      </div>
      {roleIndicator}
    </div>
  );
}

// Avatar Group Component for displaying multiple avatars
export interface UserAvatarGroupProps {
  users: Array<{
    src?: string;
    name?: string;
    initials?: string;
    alt?: string;
  }>;
  max?: number;
  size?: UserAvatarProps['size'];
  shape?: UserAvatarProps['shape'];
  className?: string;
  spacing?: 'tight' | 'normal' | 'loose';
  showCount?: boolean;
  onAvatarClick?: (index: number) => void;
  onMoreClick?: () => void;
}

const spacingClasses = {
  tight: '-space-x-1',
  normal: '-space-x-2',
  loose: '-space-x-3'
};

export function UserAvatarGroup({
  users,
  max = 5,
  size = 'md',
  shape = 'circle',
  className,
  spacing = 'normal',
  showCount = true,
  onAvatarClick,
  onMoreClick,
}: UserAvatarGroupProps) {
  const displayUsers = users.slice(0, max);
  const remainingCount = users.length - max;

  return (
    <div className={cn('flex items-center', spacingClasses[spacing], className)}>
      {displayUsers.map((user, index) => (
        <UserAvatar
          key={index}
          src={user.src}
          name={user.name}
          initials={user.initials}
          alt={user.alt}
          size={size}
          shape={shape}
          className="ring-2 ring-background"
          onClick={() => onAvatarClick?.(index)}
        />
      ))}
      
      {remainingCount > 0 && showCount && (
        <div
          className={cn(
            'relative inline-flex items-center justify-center bg-muted text-muted-foreground font-medium ring-2 ring-background cursor-pointer hover:bg-muted/80 transition-colors',
            sizeClasses[size],
            shapeClasses[shape]
          )}
          onClick={onMoreClick}
          title={`${remainingCount} more users`}
        >
          +{remainingCount}
        </div>
      )}
    </div>
  );
}

// Avatar with Tooltip Component
export interface UserAvatarWithTooltipProps extends UserAvatarProps {
  tooltip?: React.ReactNode;
  tooltipContent?: {
    name?: string;
    email?: string;
    role?: string;
    status?: string;
    lastActive?: string;
  };
}

export function UserAvatarWithTooltip({
  tooltip,
  tooltipContent,
  ...avatarProps
}: UserAvatarWithTooltipProps) {
  const [showTooltip, setShowTooltip] = useState(false);

  const tooltipElement = tooltip || (tooltipContent && (
    <div className="p-2 bg-popover text-popover-foreground rounded-md shadow-md border text-sm">
      {tooltipContent.name && (
        <div className="font-medium">{tooltipContent.name}</div>
      )}
      {tooltipContent.email && (
        <div className="text-muted-foreground">{tooltipContent.email}</div>
      )}
      {tooltipContent.role && (
        <div className="text-xs mt-1">
          <Badge variant="secondary" className="text-xs">
            {tooltipContent.role}
          </Badge>
        </div>
      )}
      {tooltipContent.status && (
        <div className="text-xs text-muted-foreground mt-1">
          Status: {tooltipContent.status}
        </div>
      )}
      {tooltipContent.lastActive && (
        <div className="text-xs text-muted-foreground">
          Last active: {tooltipContent.lastActive}
        </div>
      )}
    </div>
  ));

  return (
    <div 
      className="relative inline-block"
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      <UserAvatar {...avatarProps} />
      {showTooltip && tooltipElement && (
        <div className="absolute z-50 bottom-full left-1/2 -translate-x-1/2 mb-2">
          {tooltipElement}
        </div>
      )}
    </div>
  );
}
