"use client";

import type React from "react";

import {
  LayoutDashboard,
  Package,
  Wrench,
  BarChart3,
  Settings,
  Building2,
  Users,
  Package2,
  Brain,
  Zap,
  Workflow,
  TrendingUp,
  TrendingDown,
  Wifi,
  Layers,
  DollarSign,
  ShoppingCart,
  Search,
  User,
  ChevronUp,
  Layout,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInput,
  SidebarMenu,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { UserInfoSidebar } from "@/components/user/user-info";

// Helper function to determine if a route is active
const isRouteActive = (pathname: string, itemUrl: string): boolean => {
  // Exact match for home page
  if (itemUrl === "/" && pathname === "/") {
    return true;
  }
  
  // For other routes, check if pathname starts with the item URL
  if (itemUrl !== "/" && pathname.startsWith(itemUrl)) {
    return true;
  }
  
  return false;
};

// Menu items data
const data = {
  navMain: [
    {
      title: "Core Modules",
      items: [
        {
          title: "Dashboard",
          url: "/admin",
          icon: LayoutDashboard,
        },
        {
          title: "Assets",
          url: "/admin/assets",
          icon: Package,
          badge: "1,247",
        },
        {
          title: "Asset Types",
          url: "/admin/asset-types",
          icon: Layers,
        },
        {
          title: "Financial Management",
          url: "/admin/financial",
          icon: DollarSign,
        },
        {
          title: "Asset Depreciation",
          url: "/admin/financial/depreciation",
          icon: TrendingDown,
        },
        {
          title: "Maintenance",
          url: "/admin/maintenance",
          icon: Wrench,
          badge: "23",
        },
        {
          title: "Reports",
          url: "/admin/reports",
          icon: BarChart3,
        },
      ],
    },
    {
      title: "Asset Operations",
      items: [
        {
          title: "Create Asset",
          url: "/admin/assets/new",
          icon: Package,
        },
        {
          title: "Inventory Audit",
          url: "/inventory/check",
          icon: Search,
        },
      ],
    },
    {
      title: "Supply Chain",
      items: [
        {
          title: "Asset Requisitions",
          url: "/requisitions",
          icon: ShoppingCart,
          badge: "New",
        },
        {
          title: "New Requisition",
          url: "/requisitions/new",
          icon: Package,
        },
        {
          title: "Supply Chain Dashboard",
          url: "/admin/supply-chain",
          icon: TrendingUp,
        },
      ],
    },
    {
      title: "Business Modules",
      items: [
        {
          title: "Asset Leasing",
          url: "/modules/asset-leasing",
          icon: Building2,
        },
        {
          title: "CRM",
          url: "/admin/modules/crm",
          icon: Users,
        },
        {
          title: "Inventory",
          url: "/admin/modules/inventory",
          icon: Package2,
        },
      ],
    },
    {
      title: "E-commerce",
      items: [
        {
          title: "Store Management",
          url: "/admin/ecommerce",
          icon: ShoppingCart,
        },
        {
          title: "Storefront",
          url: "/admin/storefront",
          icon: Package,
        },
        {
          title: "Customer Dashboard",
          url: "/admin/customer-dashboard",
          icon: Users,
        },
      ],
    },
    {
      title: "Advanced Features",
      items: [
        {
          title: "AI Insights",
          url: "/admin/advanced/ai-insights",
          icon: Brain,
        },
        {
          title: "AI Demo",
          url: "/admin/ai-demo",
          icon: Brain,
          badge: "New",
        },
        {
          title: "Real-time Collaboration",
          url: "/admin/advanced/real-time",
          icon: Zap,
        },
        {
          title: "Automation Hub",
          url: "/admin/asset-automation",
          icon: Workflow,
        },
        {
          title: "Analytics Dashboard",
          url: "/admin/advanced/analytics",
          icon: TrendingUp,
        },
        {
          title: "IoT Integration",
          url: "/admin/advanced/iot",
          icon: Wifi,
        },
      ],
    },
  ],
  settings: [
    {
      title: "Settings",
      url: "/admin/settings",
      icon: Settings,
    },
    {
      title: "Custom Fields",
      url: "/admin/settings/custom-fields",
      icon: Layers,
    },
    {
      title: "Form Builder",
      url: "/admin/settings/form-builder",
      icon: Layout,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname();

  return (
    <Sidebar variant="inset" {...props} collapsible="icon">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <Package2 className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">WizeAssets</span>
                  <span className="truncate text-xs">Enterprise ERP</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
        <form>
          <SidebarGroup className="py-0">
            <SidebarGroupContent className="relative">
              <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <SidebarInput
                placeholder="Search modules..."
                className="pl-8"
              />
            </SidebarGroupContent>
          </SidebarGroup>
        </form>
      </SidebarHeader>
      <SidebarContent>
        {/* Main Navigation Groups */}
        {data.navMain.map((group) => (
          <Collapsible
            key={group.title}
            defaultOpen
            className="group/collapsible"
          >
            <SidebarGroup>
              <SidebarGroupLabel asChild>
                <CollapsibleTrigger className="group/label text-sm text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground">
                  {group.title}
                </CollapsibleTrigger>
              </SidebarGroupLabel>
              <CollapsibleContent>
                <SidebarGroupContent>
                  <SidebarMenu>
                    {group.items.map((item) => {
                      const isActive = isRouteActive(pathname, item.url);
                      
                      return (
                        <SidebarMenuItem key={item.title}>
                          <SidebarMenuButton 
                            asChild 
                            isActive={isActive}
                            className={isActive ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium border-l-2 border-sidebar-primary" : ""}
                          >
                            <Link href={item.url}>
                              <item.icon className={isActive ? "text-sidebar-primary" : ""} />
                              <span>{item.title}</span>
                            </Link>
                          </SidebarMenuButton>
                          {item.badge && (
                            <SidebarMenuBadge className={isActive ? "bg-sidebar-primary text-sidebar-primary-foreground" : ""}>
                              {item.badge}
                            </SidebarMenuBadge>
                          )}
                        </SidebarMenuItem>
                      );
                    })}
                  </SidebarMenu>
                </SidebarGroupContent>
              </CollapsibleContent>
            </SidebarGroup>
          </Collapsible>
        ))}

        {/* Settings Section */}
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {data.settings.map((item) => {
                const isActive = isRouteActive(pathname, item.url);
                
                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton 
                      asChild 
                      isActive={isActive}
                      className={isActive ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium border-l-2 border-sidebar-primary" : ""}
                    >
                      <Link href={item.url}>
                        <item.icon className={isActive ? "text-sidebar-primary" : ""} />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <UserInfoSidebar
              size="lg"
              showStatus={false}
              showRole={true}
              dropdownAlign="end"
              dropdownSide="top"
              className="w-full"
            />
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
