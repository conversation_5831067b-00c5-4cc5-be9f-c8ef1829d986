"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useCreativeAlerts, AuthContext } from "@/hooks/use-creative-alerts";
import { <PERSON><PERSON>les, Zap, AlertTriangle, Info } from "lucide-react";

/**
 * Demo component to showcase the Creative Alerts System
 * This can be used for testing and demonstration purposes
 */
export function CreativeAlertsDemo() {
  const [selectedContext, setSelectedContext] = useState<AuthContext>('login');
  
  const { showSuccessAlert, showErrorAlert, showInfoAlert, showWarningAlert } = useCreativeAlerts({
    context: selectedContext,
    onSuccess: () => {
      console.log(`Success callback fired for context: ${selectedContext}`);
    },
    successDelay: 1000
  });

  const contexts: { value: AuthContext; label: string; description: string }[] = [
    { value: 'login', label: 'Login', description: 'Welcome back messages and access denied errors' },
    { value: 'register', label: 'Register', description: 'Account creation success and validation errors' },
    { value: 'forgot-password', label: 'Forgot Password', description: 'Reset link sent and email not found' },
    { value: 'reset-password', label: 'Reset Password', description: 'Password updated and invalid link errors' },
    { value: 'verify-email', label: 'Verify Email', description: 'Email verification success and failure' },
    { value: 'logout', label: 'Logout', description: 'Session ended and logout issues' }
  ];

  const simulateError = () => {
    const errors = [
      new Error('Invalid email format'),
      new Error('Password too weak'),
      new Error('Network connection failed'),
      new Error('User not found'),
      new Error('Authentication failed')
    ];
    return errors[Math.floor(Math.random() * errors.length)];
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-primary" />
            Creative Alerts System Demo
          </CardTitle>
          <p className="text-muted-foreground">
            Test the modular creative alerts system across different authentication contexts.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Context Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Context:</label>
            <Select value={selectedContext} onValueChange={(value: AuthContext) => setSelectedContext(value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Choose an authentication context" />
              </SelectTrigger>
              <SelectContent>
                {contexts.map((context) => (
                  <SelectItem key={context.value} value={context.value}>
                    <div className="flex flex-col">
                      <span className="font-medium">{context.label}</span>
                      <span className="text-xs text-muted-foreground">{context.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Current Context Info */}
          <div className="p-4 bg-muted rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="secondary">{selectedContext}</Badge>
              <span className="text-sm font-medium">Current Context</span>
            </div>
            <p className="text-sm text-muted-foreground">
              {contexts.find(c => c.value === selectedContext)?.description}
            </p>
          </div>

          {/* Alert Buttons */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button 
              onClick={() => showSuccessAlert()}
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
            >
              <Zap className="h-4 w-4" />
              Success Alert
            </Button>
            
            <Button 
              onClick={() => showErrorAlert(simulateError())}
              variant="destructive"
              className="flex items-center gap-2"
            >
              <AlertTriangle className="h-4 w-4" />
              Error Alert
            </Button>
            
            <Button 
              onClick={() => showInfoAlert("Info Message", "This is an informational alert")}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Info className="h-4 w-4" />
              Info Alert
            </Button>
            
            <Button 
              onClick={() => showWarningAlert("Warning Message", "This is a warning alert")}
              variant="outline"
              className="flex items-center gap-2 border-yellow-500 text-yellow-600 hover:bg-yellow-50"
            >
              <AlertTriangle className="h-4 w-4" />
              Warning Alert
            </Button>
          </div>

          {/* Custom Alert Examples */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Custom Alert Examples</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button 
                onClick={() => showSuccessAlert({
                  title: "🎊 Custom Success!",
                  description: "This is a custom success message with longer duration",
                  duration: 8000
                })}
                variant="outline"
                className="border-green-500 text-green-600 hover:bg-green-50"
              >
                Custom Success
              </Button>
              
              <Button 
                onClick={() => showErrorAlert(new Error("Custom error"), {
                  title: "🚨 Custom Error!",
                  description: "This is a custom error message",
                  hint: "💡 This is a custom hint for the user"
                })}
                variant="outline"
                className="border-red-500 text-red-600 hover:bg-red-50"
              >
                Custom Error
              </Button>
            </div>
          </div>

          {/* Usage Example */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Usage Example</h3>
            <div className="p-4 bg-slate-100 dark:bg-slate-800 rounded-lg">
              <pre className="text-sm overflow-x-auto">
{`const { showSuccessAlert, showErrorAlert } = useCreativeAlerts({
  context: '${selectedContext}',
  onSuccess: () => router.push('/dashboard'),
  successDelay: 1500
});

// In your form submission:
try {
  await authFunction(values);
  showSuccessAlert();
} catch (error) {
  showErrorAlert(error);
}`}
              </pre>
            </div>
          </div>

          {/* Features List */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Features</h3>
            <ul className="space-y-1 text-sm text-muted-foreground">
              <li>• 🎨 Randomized creative messages for each context</li>
              <li>• 🎭 Animated icons with bounce, pulse, and sparkle effects</li>
              <li>• 🎯 Contextual error messages with helpful hints</li>
              <li>• 🔄 Automatic success callbacks with configurable delays</li>
              <li>• 🎪 Visual themes and animations for different alert types</li>
              <li>• ⚡ Easy integration with existing auth forms</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default CreativeAlertsDemo;
