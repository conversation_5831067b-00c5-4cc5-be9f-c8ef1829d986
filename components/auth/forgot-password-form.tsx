"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"

import { But<PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import Link from "next/link"
import { useAuth } from "@/contexts/auth-context"
import { useCreativeAlerts } from "@/hooks/use-creative-alerts"

const formSchema = z.object({
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
})

export function ForgotPasswordForm() {
  const { requestPasswordReset, isLoading } = useAuth()
  const [isSubmitted, setIsSubmitted] = useState(false)

  // Initialize creative alerts for forgot-password context
  const { showSuccessAlert, showErrorAlert } = useCreativeAlerts({
    context: 'forgot-password',
    onSuccess: () => {
      setIsSubmitted(true);
    },
    successDelay: 1000
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      await requestPasswordReset(values.email)

      // Show creative success alert (will automatically set isSubmitted after delay)
      showSuccessAlert();

    } catch (error) {
      // Show creative error alert with contextual information
      showErrorAlert(error);
    }
  }

  if (isSubmitted) {
    return (
      <div className="space-y-4 text-center">
        <h3 className="text-lg font-medium">Check your email</h3>
        <p className="text-sm text-muted-foreground">
          If an account exists for {form.getValues().email}, we've sent a password reset link.
        </p>
        <Button asChild className="w-full">
          <Link href="/login">Back to login</Link>
        </Button>
      </div>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2 text-sm text-muted-foreground">
          <p>Enter your email address and we'll send you a link to reset your password.</p>
        </div>
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? "Sending reset link..." : "Send reset link"}
        </Button>
        <div className="text-center text-sm">
          <Link href="/login" className="underline underline-offset-4 hover:text-primary">
            Back to login
          </Link>
        </div>
      </form>
    </Form>
  )
}

