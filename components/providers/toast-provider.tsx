"use client";

import React, { ReactNode } from "react";
import { toast } from "sonner";
import { Toaster } from "../ui/sonner";

interface ToastProviderProps {
  children: ReactNode;
}

export function ToastProvider({ children }: ToastProviderProps) {
  return (
    <>
      {children}
      <Toaster />
    </>
  );
}

/**
 * Toast function to trigger toast notifications.
 * Usage: Toast.success("Message"), Toast.error("Message"), etc.
 */
export const Toast = toast;
