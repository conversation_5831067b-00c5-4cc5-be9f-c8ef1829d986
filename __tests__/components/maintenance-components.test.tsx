import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { MaintenanceTasksTable } from '@/components/maintenance/maintenance-tasks-table'
import { MaintenanceCalendar } from '@/components/maintenance/maintenance-calendar'
import { MaintenanceAnalytics } from '@/components/maintenance/maintenance-analytics'
import { CreateMaintenanceTaskDialog } from '@/components/maintenance/create-maintenance-task-dialog'
import { MaintenanceNotifications } from '@/components/maintenance/maintenance-notifications'
import { PredictiveMaintenancePanel } from '@/components/maintenance/predictive-maintenance-panel'

// Mock dependencies
jest.mock('@/components/ui/use-toast', () => ({
  toast: jest.fn(),
}))

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
}))

// Mock fetch globally
global.fetch = jest.fn()

const mockTasks = [
  {
    id: 'task-1',
    title: 'HVAC Maintenance',
    description: 'Regular HVAC system maintenance',
    type: 'preventive',
    priority: 'medium',
    status: 'scheduled',
    scheduledDate: new Date('2024-01-15T10:00:00Z'),
    dueDate: new Date('2024-01-15T12:00:00Z'),
    estimatedDuration: 120,
    estimatedCost: 500,
    assignedTo: 'John Smith',
    isOverdue: false,
    isDueSoon: true,
    daysUntilDue: 2,
    completionPercentage: 0,
    asset: {
      id: 'asset-1',
      name: 'HVAC Unit #1',
      location: 'Building A - Floor 2',
      assetType: {
        name: 'HVAC System',
        icon: 'wind',
        color: '#3b82f6',
      },
    },
  },
  {
    id: 'task-2',
    title: 'Generator Inspection',
    description: 'Monthly generator inspection',
    type: 'preventive',
    priority: 'high',
    status: 'in_progress',
    scheduledDate: new Date('2024-01-10T09:00:00Z'),
    dueDate: new Date('2024-01-10T11:00:00Z'),
    estimatedDuration: 90,
    estimatedCost: 300,
    assignedTo: 'Sarah Johnson',
    isOverdue: false,
    isDueSoon: false,
    daysUntilDue: -2,
    completionPercentage: 60,
    asset: {
      id: 'asset-2',
      name: 'Emergency Generator',
      location: 'Building A - Basement',
      assetType: {
        name: 'Generator',
        icon: 'zap',
        color: '#f59e0b',
      },
    },
  },
]

const mockStats = {
  totalTasks: 25,
  scheduledTasks: 8,
  inProgressTasks: 5,
  completedTasks: 10,
  overdueTasks: 2,
  upcomingTasks: 6,
  averageCompletionTime: 4.5,
  totalCost: 12500,
  averageCost: 500,
  completionRate: 92.3,
  averageResponseTime: 2.1,
  tasksByPriority: [
    { priority: 'critical', count: 2 },
    { priority: 'high', count: 5 },
    { priority: 'medium', count: 12 },
    { priority: 'low', count: 6 },
  ],
  tasksByType: [
    { type: 'preventive', count: 15 },
    { type: 'corrective', count: 7 },
    { type: 'predictive', count: 3 },
  ],
  tasksByStatus: [
    { status: 'scheduled', count: 8 },
    { status: 'in_progress', count: 5 },
    { status: 'completed', count: 10 },
    { status: 'overdue', count: 2 },
  ],
}

describe('Maintenance Components Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({ success: true, data: [] }),
    })
  })

  describe('MaintenanceTasksTable', () => {
    const defaultProps = {
      tasks: mockTasks,
      loading: false,
      onUpdateTask: jest.fn(),
      onDeleteTask: jest.fn(),
    }

    it('should render tasks correctly', () => {
      render(<MaintenanceTasksTable {...defaultProps} />)

      expect(screen.getByText('HVAC Maintenance')).toBeInTheDocument()
      expect(screen.getByText('Generator Inspection')).toBeInTheDocument()
      expect(screen.getByText('John Smith')).toBeInTheDocument()
      expect(screen.getByText('Sarah Johnson')).toBeInTheDocument()
    })

    it('should display task status badges correctly', () => {
      render(<MaintenanceTasksTable {...defaultProps} />)

      expect(screen.getByText('Scheduled')).toBeInTheDocument()
      expect(screen.getByText('In Progress')).toBeInTheDocument()
    })

    it('should display priority badges correctly', () => {
      render(<MaintenanceTasksTable {...defaultProps} />)

      expect(screen.getByText('Medium')).toBeInTheDocument()
      expect(screen.getByText('High')).toBeInTheDocument()
    })

    it('should show overdue indicators', () => {
      const overdueTask = {
        ...mockTasks[0],
        isOverdue: true,
        daysUntilDue: -3,
      }

      render(
        <MaintenanceTasksTable
          {...defaultProps}
          tasks={[overdueTask]}
        />
      )

      expect(screen.getByText(/Overdue by 3 days/)).toBeInTheDocument()
    })

    it('should handle task selection', async () => {
      const user = userEvent.setup()
      render(<MaintenanceTasksTable {...defaultProps} />)

      const checkboxes = screen.getAllByRole('checkbox')
      await user.click(checkboxes[1]) // First task checkbox (index 0 is select all)

      expect(screen.getByText('1 selected')).toBeInTheDocument()
    })

    it('should handle search functionality', async () => {
      const user = userEvent.setup()
      render(<MaintenanceTasksTable {...defaultProps} />)

      const searchInput = screen.getByPlaceholderText('Search tasks...')
      await user.type(searchInput, 'HVAC')

      // Should filter to show only HVAC task
      expect(screen.getByText('HVAC Maintenance')).toBeInTheDocument()
      expect(screen.queryByText('Generator Inspection')).not.toBeInTheDocument()
    })

    it('should handle task actions', async () => {
      const user = userEvent.setup()
      const onUpdateTask = jest.fn()
      
      render(
        <MaintenanceTasksTable
          {...defaultProps}
          onUpdateTask={onUpdateTask}
        />
      )

      // Click on the first task's action menu
      const actionButtons = screen.getAllByRole('button', { name: /More options/ })
      await user.click(actionButtons[0])

      // Click "Start Task" option
      const startButton = screen.getByText('Start Task')
      await user.click(startButton)

      expect(onUpdateTask).toHaveBeenCalledWith('task-1', { status: 'in_progress' })
    })

    it('should show loading state', () => {
      render(<MaintenanceTasksTable {...defaultProps} loading={true} />)

      expect(screen.getAllByRole('generic')).toHaveLength(5) // Loading skeletons
    })
  })

  describe('MaintenanceCalendar', () => {
    const defaultProps = {
      tasks: mockTasks,
      onTaskUpdate: jest.fn(),
      onTaskCreate: jest.fn(),
      onTaskDelete: jest.fn(),
    }

    it('should render calendar with tasks', () => {
      render(<MaintenanceCalendar {...defaultProps} />)

      expect(screen.getByText('HVAC Maintenance')).toBeInTheDocument()
      expect(screen.getByText('Generator Inspection')).toBeInTheDocument()
    })

    it('should switch between view modes', async () => {
      const user = userEvent.setup()
      render(<MaintenanceCalendar {...defaultProps} />)

      // Switch to week view
      const weekViewButton = screen.getByRole('tab', { name: /Week/ })
      await user.click(weekViewButton)

      // Should show week view structure
      expect(screen.getByText('Mon')).toBeInTheDocument()
      expect(screen.getByText('Tue')).toBeInTheDocument()
    })

    it('should handle date navigation', async () => {
      const user = userEvent.setup()
      render(<MaintenanceCalendar {...defaultProps} />)

      const nextButton = screen.getByRole('button', { name: /Next/ })
      await user.click(nextButton)

      // Should navigate to next month
      // This would be tested by checking if the month changed
    })

    it('should filter tasks by status', async () => {
      const user = userEvent.setup()
      render(<MaintenanceCalendar {...defaultProps} />)

      const statusFilter = screen.getByRole('combobox', { name: /Status/ })
      await user.click(statusFilter)
      
      const scheduledOption = screen.getByText('Scheduled')
      await user.click(scheduledOption)

      // Should filter to show only scheduled tasks
      expect(screen.getByText('HVAC Maintenance')).toBeInTheDocument()
      expect(screen.queryByText('Generator Inspection')).not.toBeInTheDocument()
    })

    it('should handle task click events', async () => {
      const user = userEvent.setup()
      render(<MaintenanceCalendar {...defaultProps} />)

      const taskElement = screen.getByText('HVAC Maintenance')
      await user.click(taskElement)

      // Should open task details dialog
      expect(screen.getByText('Maintenance task details and actions')).toBeInTheDocument()
    })
  })

  describe('MaintenanceAnalytics', () => {
    const defaultProps = {
      stats: mockStats,
      tasks: mockTasks,
    }

    it('should render analytics dashboard', () => {
      render(<MaintenanceAnalytics {...defaultProps} />)

      expect(screen.getByText('25')).toBeInTheDocument() // Total tasks
      expect(screen.getByText('92.3%')).toBeInTheDocument() // Completion rate
    })

    it('should display charts and metrics', () => {
      render(<MaintenanceAnalytics {...defaultProps} />)

      expect(screen.getByText('Maintenance Trends')).toBeInTheDocument()
      expect(screen.getByText('Task Distribution')).toBeInTheDocument()
      expect(screen.getByText('Performance Metrics')).toBeInTheDocument()
    })

    it('should handle time range selection', async () => {
      const user = userEvent.setup()
      render(<MaintenanceAnalytics {...defaultProps} />)

      const timeRangeSelect = screen.getByRole('combobox')
      await user.click(timeRangeSelect)
      
      const weekOption = screen.getByText('7 days')
      await user.click(weekOption)

      // Should update the analytics view
    })
  })

  describe('CreateMaintenanceTaskDialog', () => {
    const defaultProps = {
      open: true,
      onOpenChange: jest.fn(),
      onSubmit: jest.fn(),
      assets: [
        {
          id: 'asset-1',
          name: 'HVAC Unit #1',
          location: 'Building A',
          assetType: { name: 'HVAC System' },
        },
      ],
      users: [
        {
          id: 'user-1',
          name: 'John Smith',
          email: '<EMAIL>',
        },
      ],
    }

    it('should render create task form', () => {
      render(<CreateMaintenanceTaskDialog {...defaultProps} />)

      expect(screen.getByText('Create Maintenance Task')).toBeInTheDocument()
      expect(screen.getByLabelText('Task Title')).toBeInTheDocument()
      expect(screen.getByLabelText('Asset')).toBeInTheDocument()
    })

    it('should handle form submission', async () => {
      const user = userEvent.setup()
      const onSubmit = jest.fn()
      
      render(
        <CreateMaintenanceTaskDialog
          {...defaultProps}
          onSubmit={onSubmit}
        />
      )

      // Fill out the form
      await user.type(screen.getByLabelText('Task Title'), 'New Maintenance Task')
      await user.type(screen.getByLabelText('Description'), 'Test description')
      
      // Select asset
      const assetSelect = screen.getByRole('combobox', { name: /Asset/ })
      await user.click(assetSelect)
      await user.click(screen.getByText('HVAC Unit #1'))

      // Submit form
      const submitButton = screen.getByRole('button', { name: 'Create Task' })
      await user.click(submitButton)

      expect(onSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'New Maintenance Task',
          description: 'Test description',
          assetId: 'asset-1',
        })
      )
    })

    it('should validate required fields', async () => {
      const user = userEvent.setup()
      render(<CreateMaintenanceTaskDialog {...defaultProps} />)

      // Try to submit without filling required fields
      const submitButton = screen.getByRole('button', { name: 'Create Task' })
      await user.click(submitButton)

      // Should show validation errors
      expect(screen.getByText(/required/i)).toBeInTheDocument()
    })

    it('should handle tab navigation', async () => {
      const user = userEvent.setup()
      render(<CreateMaintenanceTaskDialog {...defaultProps} />)

      // Navigate to scheduling tab
      const schedulingTab = screen.getByRole('tab', { name: 'Scheduling' })
      await user.click(schedulingTab)

      expect(screen.getByLabelText('Scheduled Date')).toBeInTheDocument()
      expect(screen.getByLabelText('Due Date')).toBeInTheDocument()
    })
  })

  describe('MaintenanceNotifications', () => {
    beforeEach(() => {
      ;(global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            notifications: [
              {
                id: 'notif-1',
                type: 'due_soon',
                title: 'Task Due Soon',
                message: 'HVAC Maintenance is due in 2 days',
                status: 'sent',
                scheduledFor: new Date(),
                task: {
                  id: 'task-1',
                  title: 'HVAC Maintenance',
                  asset: { name: 'HVAC Unit #1' },
                },
              },
            ],
            summary: { unreadCount: 1 },
          },
        }),
      })
    })

    it('should render notifications', async () => {
      render(<MaintenanceNotifications />)

      await waitFor(() => {
        expect(screen.getByText('Task Due Soon')).toBeInTheDocument()
        expect(screen.getByText('HVAC Maintenance is due in 2 days')).toBeInTheDocument()
      })
    })

    it('should handle mark as read', async () => {
      const user = userEvent.setup()
      render(<MaintenanceNotifications />)

      await waitFor(() => {
        expect(screen.getByText('Task Due Soon')).toBeInTheDocument()
      })

      // Click on notification to mark as read
      const notification = screen.getByText('Task Due Soon')
      await user.click(notification)

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/maintenance/notifications',
        expect.objectContaining({
          method: 'PUT',
          body: expect.stringContaining('notif-1'),
        })
      )
    })

    it('should handle mark all as read', async () => {
      const user = userEvent.setup()
      render(<MaintenanceNotifications />)

      await waitFor(() => {
        expect(screen.getByText('1')).toBeInTheDocument() // Unread count badge
      })

      // Click actions menu
      const actionsButton = screen.getByRole('button', { name: /More options/ })
      await user.click(actionsButton)

      // Click mark all as read
      const markAllButton = screen.getByText('Mark All as Read')
      await user.click(markAllButton)

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/maintenance/notifications',
        expect.objectContaining({
          method: 'PUT',
          body: expect.stringContaining('mark_read'),
        })
      )
    })
  })

  describe('PredictiveMaintenancePanel', () => {
    beforeEach(() => {
      ;(global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({
          success: true,
          data: [
            {
              id: 'insight-1',
              assetName: 'HVAC Unit #1',
              predictionType: 'failure_probability',
              predictedValue: 0.75,
              confidence: 0.85,
              recommendation: 'Schedule preventive maintenance within 7 days',
              priority: 'high',
            },
          ],
        }),
      })
    })

    it('should render predictive insights', async () => {
      render(<PredictiveMaintenancePanel />)

      await waitFor(() => {
        expect(screen.getByText('HVAC Unit #1')).toBeInTheDocument()
        expect(screen.getByText('75.0%')).toBeInTheDocument() // Failure probability
        expect(screen.getByText('Schedule preventive maintenance within 7 days')).toBeInTheDocument()
      })
    })

    it('should handle accepting recommendations', async () => {
      const user = userEvent.setup()
      render(<PredictiveMaintenancePanel />)

      await waitFor(() => {
        expect(screen.getByText('Schedule Maintenance')).toBeInTheDocument()
      })

      const scheduleButton = screen.getByText('Schedule Maintenance')
      await user.click(scheduleButton)

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/maintenance/tasks',
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('predictive'),
        })
      )
    })

    it('should display model performance metrics', async () => {
      const user = userEvent.setup()
      render(<PredictiveMaintenancePanel />)

      // Switch to models tab
      const modelsTab = screen.getByRole('tab', { name: 'AI Models' })
      await user.click(modelsTab)

      await waitFor(() => {
        expect(screen.getByText('AI Model Performance')).toBeInTheDocument()
        expect(screen.getByText('87.5% accuracy')).toBeInTheDocument()
      })
    })
  })

  describe('Integration Scenarios', () => {
    it('should handle complete task lifecycle', async () => {
      const user = userEvent.setup()
      const onTaskUpdate = jest.fn()
      
      render(<MaintenanceTasksTable tasks={mockTasks} onUpdateTask={onTaskUpdate} onDeleteTask={jest.fn()} />)

      // Start a scheduled task
      const actionButtons = screen.getAllByRole('button', { name: /More options/ })
      await user.click(actionButtons[0])
      
      const startButton = screen.getByText('Start Task')
      await user.click(startButton)

      expect(onTaskUpdate).toHaveBeenCalledWith('task-1', { status: 'in_progress' })

      // Complete the task
      await user.click(actionButtons[0])
      const completeButton = screen.getByText('Complete Task')
      await user.click(completeButton)

      expect(onTaskUpdate).toHaveBeenCalledWith('task-1', { status: 'completed' })
    })

    it('should handle error states gracefully', async () => {
      ;(global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'))
      
      render(<MaintenanceNotifications />)

      // Should handle the error gracefully without crashing
      await waitFor(() => {
        expect(screen.getByText('No notifications')).toBeInTheDocument()
      })
    })
  })
})
