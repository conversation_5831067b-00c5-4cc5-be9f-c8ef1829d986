import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { maintenanceService } from '@/lib/services/maintenance-service'
import { MaintenanceTaskCreateSchema, MaintenanceTaskUpdateSchema } from '@/lib/schemas/maintenance'
import prisma from '@/lib/prisma'

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  maintenanceTask: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
    updateMany: jest.fn(),
    deleteMany: jest.fn(),
  },
  maintenanceSchedule: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
  maintenanceWorkLog: {
    create: jest.fn(),
    createMany: jest.fn(),
    deleteMany: jest.fn(),
  },
  maintenanceNotification: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    updateMany: jest.fn(),
    deleteMany: jest.fn(),
  },
  maintenanceCalendarEvent: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    updateMany: jest.fn(),
    deleteMany: jest.fn(),
  },
  asset: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
  },
  predictiveMaintenanceModel: {
    findFirst: jest.fn(),
    findUnique: jest.fn(),
  },
  maintenancePrediction: {
    create: jest.fn(),
    findMany: jest.fn(),
  },
}))

describe('Maintenance System Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('MaintenanceService', () => {
    describe('getMaintenanceTasks', () => {
      it('should fetch maintenance tasks with filters and pagination', async () => {
        const mockTasks = [
          {
            id: 'task-1',
            title: 'Test Task 1',
            status: 'scheduled',
            priority: 'high',
            type: 'preventive',
            scheduledDate: new Date(),
            dueDate: new Date(),
            asset: {
              id: 'asset-1',
              name: 'Test Asset',
              category: 'HVAC',
              location: 'Building A',
            },
          },
        ]

        ;(prisma.maintenanceTask.findMany as jest.Mock).mockResolvedValue(mockTasks)
        ;(prisma.maintenanceTask.count as jest.Mock).mockResolvedValue(1)

        const result = await maintenanceService.getMaintenanceTasks(
          {
            status: ['scheduled'],
            priority: ['high'],
            type: ['preventive'],
          },
          { page: 1, limit: 10, offset: 0 }
        )

        expect(result.tasks).toEqual(mockTasks)
        expect(result.total).toBe(1)
        expect(result.totalPages).toBe(1)
        expect(prisma.maintenanceTask.findMany).toHaveBeenCalledWith({
          where: {
            status: { in: ['scheduled'] },
            priority: { in: ['high'] },
            type: { in: ['preventive'] },
          },
          skip: 0,
          take: 10,
          orderBy: { scheduledDate: 'asc' },
          include: expect.any(Object),
        })
      })

      it('should handle search filters correctly', async () => {
        const mockTasks = []
        ;(prisma.maintenanceTask.findMany as jest.Mock).mockResolvedValue(mockTasks)
        ;(prisma.maintenanceTask.count as jest.Mock).mockResolvedValue(0)

        await maintenanceService.getMaintenanceTasks(
          {
            search: 'HVAC repair',
          },
          { page: 1, limit: 10, offset: 0 }
        )

        expect(prisma.maintenanceTask.findMany).toHaveBeenCalledWith({
          where: {
            OR: [
              { title: { contains: 'HVAC repair', mode: 'insensitive' } },
              { description: { contains: 'HVAC repair', mode: 'insensitive' } },
              { instructions: { contains: 'HVAC repair', mode: 'insensitive' } },
              { completionNotes: { contains: 'HVAC repair', mode: 'insensitive' } },
            ],
          },
          skip: 0,
          take: 10,
          orderBy: { scheduledDate: 'asc' },
          include: expect.any(Object),
        })
      })
    })

    describe('createMaintenanceTask', () => {
      it('should create a maintenance task with valid data', async () => {
        const taskData = {
          title: 'Test Maintenance Task',
          description: 'Test description',
          assetId: 'asset-1',
          type: 'preventive',
          priority: 'medium',
          status: 'scheduled',
          scheduledDate: new Date(),
          dueDate: new Date(),
          estimatedDuration: 120,
          estimatedCost: 500,
          instructions: 'Test instructions',
        }

        const mockCreatedTask = { id: 'task-1', ...taskData }
        ;(prisma.maintenanceTask.create as jest.Mock).mockResolvedValue(mockCreatedTask)

        const result = await maintenanceService.createMaintenanceTask(taskData)

        expect(result).toEqual(mockCreatedTask)
        expect(prisma.maintenanceTask.create).toHaveBeenCalledWith({
          data: expect.objectContaining(taskData),
        })
      })

      it('should throw validation error for invalid data', async () => {
        const invalidTaskData = {
          title: '', // Invalid: empty title
          assetId: 'invalid-id', // Invalid: not a CUID
          type: 'invalid-type', // Invalid: not in enum
        }

        await expect(
          maintenanceService.createMaintenanceTask(invalidTaskData)
        ).rejects.toThrow('Validation failed')
      })
    })

    describe('scheduleMaintenanceTask', () => {
      it('should schedule task with working hours consideration', async () => {
        const taskData = {
          scheduledDate: new Date('2024-01-15T06:00:00Z'), // 6 AM - before working hours
          estimatedDuration: 120,
          assignedTo: 'tech-1',
          priority: 'medium',
          type: 'preventive',
        }

        ;(prisma.maintenanceTask.findMany as jest.Mock).mockResolvedValue([]) // No conflicts

        const result = await maintenanceService.scheduleMaintenanceTask(taskData, {
          considerWorkingHours: true,
          workingHours: { start: '09:00', end: '17:00' },
          excludeWeekends: true,
        })

        expect(result.scheduledDate.getHours()).toBeGreaterThanOrEqual(9)
        expect(result.scheduledDate.getHours()).toBeLessThan(17)
        expect(result.conflicts).toEqual([])
      })

      it('should detect and handle scheduling conflicts', async () => {
        const taskData = {
          scheduledDate: new Date('2024-01-15T10:00:00Z'),
          estimatedDuration: 120,
          assignedTo: 'tech-1',
          priority: 'medium',
          type: 'preventive',
        }

        const conflictingTask = {
          id: 'existing-task',
          scheduledDate: new Date('2024-01-15T09:30:00Z'),
          dueDate: new Date('2024-01-15T11:30:00Z'),
          assignedTo: 'tech-1',
        }

        ;(prisma.maintenanceTask.findMany as jest.Mock).mockResolvedValue([conflictingTask])

        const result = await maintenanceService.scheduleMaintenanceTask(taskData)

        expect(result.conflicts).toHaveLength(1)
        expect(result.conflicts[0].id).toBe('existing-task')
      })
    })

    describe('generateMaintenanceRecommendations', () => {
      it('should generate recommendations based on asset history', async () => {
        const mockAsset = {
          id: 'asset-1',
          assetType: {
            maintenanceSchedules: [
              {
                id: 'schedule-1',
                name: 'Monthly Inspection',
                frequency: JSON.stringify({ type: 'monthly', interval: 1 }),
                estimatedCost: 200,
              },
            ],
          },
          maintenanceTasks: [
            {
              id: 'task-1',
              scheduleId: 'schedule-1',
              completedDate: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000), // 35 days ago
              type: 'preventive',
              actualCost: 180,
            },
          ],
        }

        ;(prisma.asset.findUnique as jest.Mock).mockResolvedValue(mockAsset)
        ;(prisma.maintenancePrediction.findMany as jest.Mock).mockResolvedValue([])

        const recommendations = await maintenanceService.generateMaintenanceRecommendations('asset-1')

        expect(recommendations).toHaveLength(1)
        expect(recommendations[0].recommendationType).toBe('schedule_maintenance')
        expect(recommendations[0].priority).toBe('high') // Overdue
        expect(recommendations[0].reasoning).toContain('Monthly Inspection')
      })

      it('should prioritize recommendations by risk and confidence', async () => {
        const mockAsset = {
          id: 'asset-1',
          assetType: { maintenanceSchedules: [] },
          maintenanceTasks: [
            {
              type: 'corrective',
              completedDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
              actualCost: 1000,
            },
            {
              type: 'corrective',
              completedDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
              actualCost: 1200,
            },
          ],
        }

        ;(prisma.asset.findUnique as jest.Mock).mockResolvedValue(mockAsset)
        ;(prisma.maintenancePrediction.findMany as jest.Mock).mockResolvedValue([])

        const recommendations = await maintenanceService.generateMaintenanceRecommendations('asset-1')

        expect(recommendations).toHaveLength(1)
        expect(recommendations[0].recommendationType).toBe('inspect_asset')
        expect(recommendations[0].priority).toBe('high')
        expect(recommendations[0].reasoning).toContain('failure risk')
      })
    })

    describe('createPredictiveMaintenance', () => {
      it('should create prediction with default model', async () => {
        const mockModel = {
          id: 'model-1',
          algorithm: 'linear_regression',
          version: 1,
        }

        const predictionInput = {
          assetId: 'asset-1',
          features: {
            age_months: 24,
            usage_hours: 2000,
            maintenance_count: 5,
            failure_count: 1,
          },
        }

        ;(prisma.predictiveMaintenanceModel.findFirst as jest.Mock).mockResolvedValue(mockModel)
        ;(prisma.maintenancePrediction.create as jest.Mock).mockResolvedValue({
          id: 'prediction-1',
          ...predictionInput,
          modelId: 'model-1',
          predictedValue: 0.65,
          confidence: 0.8,
        })

        const result = await maintenanceService.createPredictiveMaintenance(predictionInput)

        expect(result.predictedValue).toBeGreaterThan(0)
        expect(result.predictedValue).toBeLessThanOrEqual(1)
        expect(result.confidence).toBeGreaterThan(0)
        expect(result.confidence).toBeLessThanOrEqual(1)
        expect(prisma.maintenancePrediction.create).toHaveBeenCalled()
      })

      it('should throw error when no model is available', async () => {
        ;(prisma.predictiveMaintenanceModel.findFirst as jest.Mock).mockResolvedValue(null)

        const predictionInput = {
          assetId: 'asset-1',
          features: { age_months: 24 },
        }

        await expect(
          maintenanceService.createPredictiveMaintenance(predictionInput)
        ).rejects.toThrow('No predictive maintenance model available')
      })
    })
  })

  describe('Schema Validation', () => {
    describe('MaintenanceTaskCreateSchema', () => {
      it('should validate correct task data', () => {
        const validTaskData = {
          title: 'Test Task',
          description: 'Test description',
          assetId: 'clp123456789012345678',
          type: 'preventive',
          priority: 'medium',
          status: 'scheduled',
          scheduledDate: new Date(),
          dueDate: new Date(),
          estimatedDuration: 120,
          estimatedCost: 500,
          instructions: 'Test instructions',
        }

        const result = MaintenanceTaskCreateSchema.safeParse(validTaskData)
        expect(result.success).toBe(true)
      })

      it('should reject invalid task data', () => {
        const invalidTaskData = {
          title: '', // Empty title
          assetId: 'invalid-id', // Invalid CUID
          type: 'invalid-type', // Invalid enum value
          priority: 'invalid-priority', // Invalid enum value
          estimatedDuration: -10, // Negative duration
        }

        const result = MaintenanceTaskCreateSchema.safeParse(invalidTaskData)
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.errors).toHaveLength(5)
        }
      })
    })

    describe('MaintenanceTaskUpdateSchema', () => {
      it('should allow partial updates', () => {
        const partialUpdate = {
          status: 'completed',
          completedDate: new Date(),
          actualCost: 450,
        }

        const result = MaintenanceTaskUpdateSchema.safeParse(partialUpdate)
        expect(result.success).toBe(true)
      })

      it('should validate updated fields', () => {
        const invalidUpdate = {
          status: 'invalid-status',
          actualCost: -100,
        }

        const result = MaintenanceTaskUpdateSchema.safeParse(invalidUpdate)
        expect(result.success).toBe(false)
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      ;(prisma.maintenanceTask.findMany as jest.Mock).mockRejectedValue(
        new Error('Database connection failed')
      )

      await expect(
        maintenanceService.getMaintenanceTasks({}, { page: 1, limit: 10, offset: 0 })
      ).rejects.toThrow('Failed to fetch maintenance tasks')
    })

    it('should handle validation errors in service methods', async () => {
      const invalidData = {
        title: '', // Invalid
        assetId: 'invalid',
      }

      await expect(
        maintenanceService.createMaintenanceTask(invalidData)
      ).rejects.toThrow('Validation failed')
    })
  })

  describe('Performance Tests', () => {
    it('should handle large datasets efficiently', async () => {
      const largeMockDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: `task-${i}`,
        title: `Task ${i}`,
        status: 'scheduled',
        scheduledDate: new Date(),
        dueDate: new Date(),
      }))

      ;(prisma.maintenanceTask.findMany as jest.Mock).mockResolvedValue(largeMockDataset)
      ;(prisma.maintenanceTask.count as jest.Mock).mockResolvedValue(1000)

      const startTime = Date.now()
      const result = await maintenanceService.getMaintenanceTasks(
        {},
        { page: 1, limit: 1000, offset: 0 }
      )
      const endTime = Date.now()

      expect(result.tasks).toHaveLength(1000)
      expect(endTime - startTime).toBeLessThan(1000) // Should complete within 1 second
    })
  })
})
