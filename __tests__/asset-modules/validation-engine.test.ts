import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ModuleValidationEngine } from '@/lib/asset-modules/runtime/validation-engine';
import { ModuleField, AssetModule, ModuleExecutionContext } from '@/lib/types/asset-modules';

describe('Validation Engine', () => {
  let validationEngine: ModuleValidationEngine;
  let testField: ModuleField;
  let testModule: AssetModule;
  let testContext: ModuleExecutionContext;

  beforeEach(() => {
    validationEngine = ModuleValidationEngine.getInstance();
    
    testField = {
      id: 'test-field-1',
      name: 'testField',
      label: 'Test Field',
      type: 'text',
      isRequired: true,
      defaultValue: '',
      placeholder: 'Enter test value',
      helpText: 'This is a test field',
      validation: {
        required: true,
        minLength: 3,
        maxLength: 50,
        pattern: '^[a-zA-Z0-9]+$'
      },
      uiConfig: {
        width: 'full'
      },
      group: 'default',
      order: 1,
      dependsOn: [],
      isUnique: false
    };

    testModule = {
      id: 'test-module-1',
      name: 'Test Module',
      description: 'A test module',
      version: '1.0.0',
      fields: [testField],
      logic: { nodes: [], edges: [] },
      rendering: {},
      validation: {},
      dependencies: [],
      compatibleAssetTypes: ['test-asset-type'],
      tags: ['test'],
      isActive: true,
      isPublic: false,
      isBuiltIn: false,
      requiredPermissions: [],
      author: 'Test Author',
      authorId: 'test-author-id',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    testContext = {
      moduleId: testModule.id,
      assetId: 'test-asset-1',
      assetTypeId: 'test-asset-type',
      userId: 'test-user-1',
      userRole: 'user',
      permissions: ['field.validate'],
      sessionId: 'test-session-1',
      executionId: 'test-execution-1',
      timestamp: new Date().toISOString(),
      environment: 'development',
      metadata: {}
    };
  });

  describe('Field Validation', () => {
    it('should validate a valid field value', async () => {
      const validValue = 'validValue123';
      
      const result = await validationEngine.validateField(testField, validValue, testContext);
      
      expect(result.valid).toBe(true);
      expect(result.fieldId).toBe(testField.id);
      expect(result.value).toBe(validValue);
      expect(result.normalizedValue).toBe(validValue);
      expect(result.errors).toHaveLength(0);
    });

    it('should fail validation for required field with empty value', async () => {
      const emptyValue = '';
      
      const result = await validationEngine.validateField(testField, emptyValue, testContext);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].rule).toBe('required');
      expect(result.errors[0].message).toContain('required');
    });

    it('should fail validation for value below minimum length', async () => {
      const shortValue = 'ab'; // Less than minLength of 3
      
      const result = await validationEngine.validateField(testField, shortValue, testContext);
      
      expect(result.valid).toBe(false);
      expect(result.errors.some(e => e.rule === 'minLength')).toBe(true);
    });

    it('should fail validation for value above maximum length', async () => {
      const longValue = 'a'.repeat(51); // More than maxLength of 50
      
      const result = await validationEngine.validateField(testField, longValue, testContext);
      
      expect(result.valid).toBe(false);
      expect(result.errors.some(e => e.rule === 'maxLength')).toBe(true);
    });

    it('should fail validation for value not matching pattern', async () => {
      const invalidValue = 'invalid-value!'; // Contains special characters
      
      const result = await validationEngine.validateField(testField, invalidValue, testContext);
      
      expect(result.valid).toBe(false);
      expect(result.errors.some(e => e.rule === 'pattern')).toBe(true);
    });

    it('should validate email field type', async () => {
      const emailField = {
        ...testField,
        type: 'email',
        validation: {
          required: true
        }
      };
      
      const validEmail = '<EMAIL>';
      const invalidEmail = 'invalid-email';
      
      const validResult = await validationEngine.validateField(emailField, validEmail, testContext);
      expect(validResult.valid).toBe(true);
      
      const invalidResult = await validationEngine.validateField(emailField, invalidEmail, testContext);
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors.some(e => e.rule === 'email')).toBe(true);
    });

    it('should validate URL field type', async () => {
      const urlField = {
        ...testField,
        type: 'url',
        validation: {
          required: true
        }
      };
      
      const validUrl = 'https://example.com';
      const invalidUrl = 'not-a-url';
      
      const validResult = await validationEngine.validateField(urlField, validUrl, testContext);
      expect(validResult.valid).toBe(true);
      
      const invalidResult = await validationEngine.validateField(urlField, invalidUrl, testContext);
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors.some(e => e.rule === 'url')).toBe(true);
    });

    it('should validate number field with min/max constraints', async () => {
      const numberField = {
        ...testField,
        type: 'number',
        validation: {
          required: true,
          min: 10,
          max: 100
        }
      };
      
      const validNumber = 50;
      const tooSmall = 5;
      const tooLarge = 150;
      
      const validResult = await validationEngine.validateField(numberField, validNumber, testContext);
      expect(validResult.valid).toBe(true);
      
      const smallResult = await validationEngine.validateField(numberField, tooSmall, testContext);
      expect(smallResult.valid).toBe(false);
      expect(smallResult.errors.some(e => e.rule === 'min')).toBe(true);
      
      const largeResult = await validationEngine.validateField(numberField, tooLarge, testContext);
      expect(largeResult.valid).toBe(false);
      expect(largeResult.errors.some(e => e.rule === 'max')).toBe(true);
    });

    it('should apply value transformations', async () => {
      const value = '  Test Value  '; // Value with whitespace
      
      const result = await validationEngine.validateField(testField, value, testContext);
      
      // Should be trimmed and normalized
      expect(result.normalizedValue).not.toBe(value);
      expect(result.normalizedValue).toBe(value.trim());
    });

    it('should track validation performance', async () => {
      const result = await validationEngine.validateField(testField, 'validValue', testContext);
      
      expect(result.metadata.validationTime).toBeGreaterThan(0);
      expect(Array.isArray(result.metadata.rulesApplied)).toBe(true);
      expect(Array.isArray(result.metadata.transformationsApplied)).toBe(true);
    });
  });

  describe('Module Validation', () => {
    it('should validate all fields in a module', async () => {
      const data = {
        testField: 'validValue123'
      };
      
      const result = await validationEngine.validateModule(testModule, data, testContext);
      
      expect(result.valid).toBe(true);
      expect(result.moduleId).toBe(testModule.id);
      expect(Object.keys(result.fieldResults)).toHaveLength(1);
      expect(result.fieldResults[testField.id].valid).toBe(true);
    });

    it('should fail module validation if any field fails', async () => {
      const data = {
        testField: '' // Invalid empty value
      };
      
      const result = await validationEngine.validateModule(testModule, data, testContext);
      
      expect(result.valid).toBe(false);
      expect(result.fieldResults[testField.id].valid).toBe(false);
    });

    it('should validate multiple fields', async () => {
      const multiFieldModule = {
        ...testModule,
        fields: [
          testField,
          {
            ...testField,
            id: 'field-2',
            name: 'field2',
            label: 'Field 2',
            type: 'number',
            validation: {
              required: true,
              min: 1,
              max: 10
            }
          }
        ]
      };
      
      const data = {
        testField: 'validValue123',
        field2: 5
      };
      
      const result = await validationEngine.validateModule(multiFieldModule, data, testContext);
      
      expect(result.valid).toBe(true);
      expect(Object.keys(result.fieldResults)).toHaveLength(2);
      expect(result.fieldResults[testField.id].valid).toBe(true);
      expect(result.fieldResults['field-2'].valid).toBe(true);
    });

    it('should detect structural issues in module', async () => {
      const invalidModule = {
        ...testModule,
        fields: [
          testField,
          {
            ...testField,
            id: 'duplicate-field',
            name: 'testField', // Duplicate name
            label: 'Duplicate Field'
          }
        ]
      };
      
      const data = { testField: 'validValue123' };
      
      const result = await validationEngine.validateModule(invalidModule, data, testContext);
      
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(e => e.message.includes('Duplicate'))).toBe(true);
    });

    it('should track module validation performance', async () => {
      const data = { testField: 'validValue123' };
      
      const result = await validationEngine.validateModule(testModule, data, testContext);
      
      expect(result.metadata.totalValidationTime).toBeGreaterThan(0);
      expect(result.metadata.fieldsValidated).toBe(1);
      expect(result.metadata.rulesExecuted).toBeGreaterThan(0);
    });
  });

  describe('Validation Schema Creation', () => {
    it('should create validation schema from fields', async () => {
      const schema = await validationEngine.createValidationSchema([testField]);
      
      expect(schema).toBeDefined();
      expect(schema.fields[testField.id]).toBeDefined();
      expect(schema.fields[testField.id].type).toBe(testField.type);
      expect(schema.fields[testField.id].required).toBe(testField.isRequired);
      expect(Array.isArray(schema.fields[testField.id].rules)).toBe(true);
    });

    it('should include all validation rules in schema', async () => {
      const schema = await validationEngine.createValidationSchema([testField]);
      const fieldSchema = schema.fields[testField.id];
      
      const ruleTypes = fieldSchema.rules.map(rule => rule.type);
      expect(ruleTypes).toContain('required');
      expect(ruleTypes).toContain('minLength');
      expect(ruleTypes).toContain('maxLength');
      expect(ruleTypes).toContain('pattern');
    });
  });

  describe('Error Handling', () => {
    it('should handle validation errors gracefully', async () => {
      const invalidField = {
        ...testField,
        validation: {
          pattern: '[invalid regex' // Invalid regex pattern
        }
      };
      
      const result = await validationEngine.validateField(invalidField, 'test', testContext);
      
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should handle missing field gracefully', async () => {
      const data = {}; // Missing required field
      
      const result = await validationEngine.validateModule(testModule, data, testContext);
      
      expect(result.valid).toBe(false);
      expect(result.fieldResults[testField.id].valid).toBe(false);
    });

    it('should handle permission errors', async () => {
      const restrictedContext = {
        ...testContext,
        permissions: [] // No validation permission
      };
      
      const result = await validationEngine.validateField(testField, 'test', restrictedContext);
      
      expect(result.valid).toBe(false);
      expect(result.errors.some(e => e.message.includes('permission'))).toBe(true);
    });
  });

  describe('Performance', () => {
    it('should validate fields within reasonable time', async () => {
      const startTime = Date.now();
      
      await validationEngine.validateField(testField, 'validValue123', testContext);
      
      const validationTime = Date.now() - startTime;
      expect(validationTime).toBeLessThan(1000); // Should validate within 1 second
    });

    it('should benefit from caching on repeated validations', async () => {
      const value = 'validValue123';
      
      // First validation (cache miss)
      const startTime1 = Date.now();
      await validationEngine.validateField(testField, value, testContext);
      const validationTime1 = Date.now() - startTime1;
      
      // Second validation (cache hit)
      const startTime2 = Date.now();
      await validationEngine.validateField(testField, value, testContext);
      const validationTime2 = Date.now() - startTime2;
      
      // Cache hit should be faster
      expect(validationTime2).toBeLessThan(validationTime1);
    });

    it('should handle large modules efficiently', async () => {
      const largeModule = {
        ...testModule,
        fields: Array.from({ length: 100 }, (_, i) => ({
          ...testField,
          id: `field-${i}`,
          name: `field${i}`,
          label: `Field ${i}`
        }))
      };
      
      const data = Object.fromEntries(
        largeModule.fields.map(field => [field.name, 'validValue123'])
      );
      
      const startTime = Date.now();
      const result = await validationEngine.validateModule(largeModule, data, testContext);
      const validationTime = Date.now() - startTime;
      
      expect(result.metadata.fieldsValidated).toBe(100);
      expect(validationTime).toBeLessThan(5000); // Should validate 100 fields within 5 seconds
    });
  });
});
