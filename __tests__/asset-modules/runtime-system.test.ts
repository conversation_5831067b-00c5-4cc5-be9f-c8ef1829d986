import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { runtimeSystem } from '@/lib/asset-modules/runtime/runtime-system';
import { ModuleRegistry } from '@/lib/asset-modules/runtime/module-registry';
import { ModuleLoader } from '@/lib/asset-modules/runtime/module-loader';
import { editorIntegration } from '@/lib/asset-modules/runtime/editor-integration';
import { AssetModule, ModuleExecutionContext } from '@/lib/types/asset-modules';

// Mock dependencies
jest.mock('@/lib/db');
jest.mock('@/lib/utils/cache');
jest.mock('@/lib/utils/security');

describe('Asset Module Runtime System', () => {
  let testModule: AssetModule;
  let testContext: ModuleExecutionContext;

  beforeEach(() => {
    // Setup test module
    testModule = {
      id: 'test-module-1',
      name: 'Test Module',
      description: 'A test module for unit testing',
      version: '1.0.0',
      fields: [
        {
          id: 'field-1',
          name: 'testField',
          label: 'Test Field',
          type: 'text',
          isRequired: true,
          defaultValue: '',
          placeholder: 'Enter test value',
          helpText: 'This is a test field',
          validation: {
            required: true,
            minLength: 1,
            maxLength: 100
          },
          uiConfig: {
            width: 'full'
          },
          group: 'default',
          order: 1,
          dependsOn: [],
          isUnique: false
        }
      ],
      logic: {
        nodes: [
          {
            id: 'input-1',
            type: 'input',
            position: { x: 0, y: 0 },
            data: { fieldName: 'testField' }
          },
          {
            id: 'output-1',
            type: 'output',
            position: { x: 200, y: 0 },
            data: { outputField: 'result', inputField: 'testField' }
          }
        ],
        edges: [
          {
            id: 'edge-1',
            source: 'input-1',
            target: 'output-1',
            type: 'default'
          }
        ]
      },
      rendering: {},
      validation: {},
      dependencies: [],
      compatibleAssetTypes: ['test-asset-type'],
      tags: ['test'],
      isActive: true,
      isPublic: false,
      isBuiltIn: false,
      requiredPermissions: [],
      author: 'Test Author',
      authorId: 'test-author-id',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Setup test context
    testContext = {
      moduleId: testModule.id,
      assetId: 'test-asset-1',
      assetTypeId: 'test-asset-type',
      userId: 'test-user-1',
      userRole: 'user',
      permissions: ['module.execute', 'module.validate', 'module.render'],
      sessionId: 'test-session-1',
      executionId: 'test-execution-1',
      timestamp: new Date().toISOString(),
      environment: 'development',
      metadata: {}
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Module Registry', () => {
    it('should register a module successfully', async () => {
      const registry = ModuleRegistry.getInstance();
      
      const installationPackage = {
        module: testModule,
        dependencies: [],
        assets: { icons: [], templates: [], documentation: [] },
        installation: {},
        verification: { checksum: 'test-checksum', signature: '', certificate: '' }
      };

      const result = await registry.registerModule(installationPackage);
      
      expect(result.success).toBe(true);
      expect(result.moduleId).toBe(testModule.id);
    });

    it('should retrieve a registered module', async () => {
      const registry = ModuleRegistry.getInstance();
      
      // First register the module
      const installationPackage = {
        module: testModule,
        dependencies: [],
        assets: { icons: [], templates: [], documentation: [] },
        installation: {},
        verification: { checksum: 'test-checksum', signature: '', certificate: '' }
      };
      
      await registry.registerModule(installationPackage);
      
      // Then retrieve it
      const retrievedModule = await registry.getModule(testModule.id);
      
      expect(retrievedModule).toBeDefined();
      expect(retrievedModule?.id).toBe(testModule.id);
      expect(retrievedModule?.name).toBe(testModule.name);
    });

    it('should validate registry integrity', async () => {
      const registry = ModuleRegistry.getInstance();
      
      const validationResult = await registry.validateRegistry();
      
      expect(validationResult).toBeDefined();
      expect(typeof validationResult.valid).toBe('boolean');
      expect(Array.isArray(validationResult.errors)).toBe(true);
      expect(Array.isArray(validationResult.warnings)).toBe(true);
    });
  });

  describe('Module Loader', () => {
    it('should load a module successfully', async () => {
      const loader = ModuleLoader.getInstance();
      
      const result = await loader.loadModule(testModule.id, testContext);
      
      expect(result).toBeDefined();
      expect(result.moduleId).toBe(testModule.id);
      expect(['loading', 'loaded', 'active', 'error']).toContain(result.status);
    });

    it('should activate a loaded module', async () => {
      const loader = ModuleLoader.getInstance();
      
      // First load the module
      await loader.loadModule(testModule.id, testContext);
      
      // Then activate it
      const result = await loader.activateModule(testModule.id, testContext);
      
      expect(result.success).toBe(true);
      expect(result.moduleId).toBe(testModule.id);
    });

    it('should deactivate an active module', async () => {
      const loader = ModuleLoader.getInstance();
      
      // Load and activate the module
      await loader.loadModule(testModule.id, testContext);
      await loader.activateModule(testModule.id, testContext);
      
      // Then deactivate it
      const result = await loader.deactivateModule(testModule.id, testContext);
      
      expect(result.success).toBe(true);
      expect(result.moduleId).toBe(testModule.id);
    });
  });

  describe('Runtime System Integration', () => {
    it('should execute a module successfully', async () => {
      const inputData = { testField: 'test value' };
      
      const result = await runtimeSystem.executeModule(
        testModule.id,
        testContext.assetId,
        testContext.assetTypeId,
        inputData,
        testContext
      );
      
      expect(result).toBeDefined();
      expect(result.moduleId).toBe(testModule.id);
      expect(typeof result.success).toBe('boolean');
      expect(typeof result.executionTime).toBe('number');
      expect(result.outputs).toBeDefined();
    });

    it('should validate module data', async () => {
      const inputData = { testField: 'test value' };
      
      const result = await runtimeSystem.validateModuleData(
        testModule.id,
        inputData,
        testContext
      );
      
      expect(result).toBeDefined();
      expect(result.moduleId).toBe(testModule.id);
      expect(typeof result.valid).toBe('boolean');
      expect(result.fieldResults).toBeDefined();
    });

    it('should render module fields', async () => {
      const inputData = { testField: 'test value' };
      
      const result = await runtimeSystem.renderModuleFields(
        testModule.id,
        inputData,
        testContext
      );
      
      expect(result).toBeDefined();
      expect(typeof result.totalRenderTime).toBe('number');
      expect(Array.isArray(result.fields)).toBe(true);
      expect(typeof result.layout).toBe('string');
    });

    it('should get system metrics', () => {
      const metrics = runtimeSystem.getSystemMetrics();
      
      expect(metrics).toBeDefined();
      expect(typeof metrics.totalExecutions).toBe('number');
      expect(typeof metrics.successfulExecutions).toBe('number');
      expect(typeof metrics.failedExecutions).toBe('number');
      expect(typeof metrics.averageExecutionTime).toBe('number');
      expect(typeof metrics.activeModules).toBe('number');
    });
  });

  describe('Editor Integration', () => {
    it('should deploy a module from editor', async () => {
      const result = await editorIntegration.deployModule(
        testModule,
        testContext.userId,
        { autoActivate: true }
      );
      
      expect(result).toBeDefined();
      expect(result.moduleId).toBe(testModule.id);
      expect(typeof result.success).toBe('boolean');
      expect(result.deploymentId).toBeDefined();
    });

    it('should create a preview session', async () => {
      const result = await editorIntegration.createPreviewSession(
        testModule,
        testContext.userId,
        { duration: 30 * 60 * 1000 } // 30 minutes
      );
      
      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
      if (result.success) {
        expect(result.sessionId).toBeDefined();
        expect(result.originalModuleId).toBe(testModule.id);
      }
    });

    it('should execute in preview mode', async () => {
      // First create a preview session
      const previewResult = await editorIntegration.createPreviewSession(
        testModule,
        testContext.userId
      );
      
      if (previewResult.success) {
        const inputData = { testField: 'test value' };
        
        const result = await editorIntegration.executePreview(
          previewResult.sessionId,
          inputData,
          testContext.assetTypeId
        );
        
        expect(result).toBeDefined();
        expect(result.sessionId).toBe(previewResult.sessionId);
        expect(typeof result.success).toBe('boolean');
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle module not found error', async () => {
      const result = await runtimeSystem.executeModule(
        'non-existent-module',
        testContext.assetId,
        testContext.assetTypeId,
        {},
        testContext
      );
      
      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0].message).toContain('not found');
    });

    it('should handle validation errors', async () => {
      const invalidData = { testField: '' }; // Empty value for required field
      
      const result = await runtimeSystem.validateModuleData(
        testModule.id,
        invalidData,
        testContext
      );
      
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should handle permission errors', async () => {
      const restrictedContext = {
        ...testContext,
        permissions: [] // No permissions
      };
      
      const result = await runtimeSystem.executeModule(
        testModule.id,
        testContext.assetId,
        testContext.assetTypeId,
        {},
        restrictedContext
      );
      
      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('permission'))).toBe(true);
    });
  });

  describe('Performance', () => {
    it('should complete execution within reasonable time', async () => {
      const startTime = Date.now();
      
      await runtimeSystem.executeModule(
        testModule.id,
        testContext.assetId,
        testContext.assetTypeId,
        { testField: 'test value' },
        testContext
      );
      
      const executionTime = Date.now() - startTime;
      expect(executionTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle concurrent executions', async () => {
      const promises = Array.from({ length: 10 }, (_, i) =>
        runtimeSystem.executeModule(
          testModule.id,
          `test-asset-${i}`,
          testContext.assetTypeId,
          { testField: `test value ${i}` },
          { ...testContext, executionId: `test-execution-${i}` }
        )
      );
      
      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(10);
      results.forEach((result, i) => {
        expect(result).toBeDefined();
        expect(result.moduleId).toBe(testModule.id);
      });
    });
  });

  describe('Security', () => {
    it('should enforce module permissions', async () => {
      const unauthorizedContext = {
        ...testContext,
        permissions: ['module.read'] // Missing execute permission
      };
      
      const result = await runtimeSystem.executeModule(
        testModule.id,
        testContext.assetId,
        testContext.assetTypeId,
        {},
        unauthorizedContext
      );
      
      expect(result.success).toBe(false);
      expect(result.errors.some(e => e.message.includes('permission'))).toBe(true);
    });

    it('should sanitize input data', async () => {
      const maliciousData = {
        testField: '<script>alert("xss")</script>'
      };
      
      const result = await runtimeSystem.validateModuleData(
        testModule.id,
        maliciousData,
        testContext
      );
      
      // The validation should either reject the malicious input or sanitize it
      expect(result).toBeDefined();
      if (result.valid) {
        const normalizedValue = result.fieldResults['field-1']?.normalizedValue;
        expect(normalizedValue).not.toContain('<script>');
      }
    });
  });
});
