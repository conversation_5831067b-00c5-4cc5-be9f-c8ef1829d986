import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ModuleFieldRenderingEngine } from '@/lib/asset-modules/runtime/field-rendering-engine';
import { ModuleField, ModuleExecutionContext } from '@/lib/types/asset-modules';

describe('Field Rendering Engine', () => {
  let renderingEngine: ModuleFieldRenderingEngine;
  let testField: ModuleField;
  let testContext: ModuleExecutionContext;

  beforeEach(() => {
    renderingEngine = ModuleFieldRenderingEngine.getInstance();
    
    testField = {
      id: 'test-field-1',
      name: 'testField',
      label: 'Test Field',
      type: 'text',
      isRequired: true,
      defaultValue: '',
      placeholder: 'Enter test value',
      helpText: 'This is a test field',
      validation: {
        required: true,
        minLength: 1,
        maxLength: 100
      },
      uiConfig: {
        width: 'full',
        customClass: 'test-field-class'
      },
      group: 'default',
      order: 1,
      dependsOn: [],
      isUnique: false
    };

    testContext = {
      moduleId: 'test-module-1',
      assetId: 'test-asset-1',
      assetTypeId: 'test-asset-type',
      userId: 'test-user-1',
      userRole: 'user',
      permissions: ['field.render'],
      sessionId: 'test-session-1',
      executionId: 'test-execution-1',
      timestamp: new Date().toISOString(),
      environment: 'development',
      metadata: {}
    };
  });

  describe('Single Field Rendering', () => {
    it('should render a text field successfully', async () => {
      const value = 'test value';
      
      const result = await renderingEngine.renderField(testField, value, testContext);
      
      expect(result).toBeDefined();
      expect(result.fieldId).toBe(testField.id);
      expect(result.html).toContain('input');
      expect(result.html).toContain('type="text"');
      expect(result.html).toContain(value);
      expect(result.css).toBeDefined();
      expect(result.javascript).toBeDefined();
      expect(result.errors).toHaveLength(0);
    });

    it('should render a number field successfully', async () => {
      const numberField = {
        ...testField,
        type: 'number',
        validation: {
          ...testField.validation,
          min: 0,
          max: 100,
          step: 1
        }
      };
      
      const value = 42;
      
      const result = await renderingEngine.renderField(numberField, value, testContext);
      
      expect(result.html).toContain('type="number"');
      expect(result.html).toContain('min="0"');
      expect(result.html).toContain('max="100"');
      expect(result.html).toContain('step="1"');
      expect(result.html).toContain(String(value));
    });

    it('should render a select field successfully', async () => {
      const selectField = {
        ...testField,
        type: 'select',
        validation: {
          ...testField.validation,
          options: [
            { value: 'option1', label: 'Option 1' },
            { value: 'option2', label: 'Option 2' }
          ]
        }
      };
      
      const value = 'option1';
      
      const result = await renderingEngine.renderField(selectField, value, testContext);
      
      expect(result.html).toContain('<select');
      expect(result.html).toContain('Option 1');
      expect(result.html).toContain('Option 2');
      expect(result.html).toContain('value="option1"');
    });

    it('should handle required field validation', async () => {
      const requiredField = {
        ...testField,
        isRequired: true
      };
      
      const result = await renderingEngine.renderField(requiredField, '', testContext);
      
      expect(result.html).toContain('required');
    });

    it('should apply custom CSS classes', async () => {
      const customField = {
        ...testField,
        uiConfig: {
          ...testField.uiConfig,
          customClass: 'my-custom-class',
          labelClass: 'my-label-class',
          inputClass: 'my-input-class'
        }
      };
      
      const result = await renderingEngine.renderField(customField, 'test', testContext);
      
      expect(result.html).toContain('my-custom-class');
      expect(result.html).toContain('my-label-class');
      expect(result.html).toContain('my-input-class');
    });

    it('should sanitize dangerous values', async () => {
      const dangerousValue = '<script>alert("xss")</script>';
      
      const result = await renderingEngine.renderField(testField, dangerousValue, testContext);
      
      expect(result.html).not.toContain('<script>');
      expect(result.html).toContain('&lt;script&gt;');
    });

    it('should handle rendering errors gracefully', async () => {
      const invalidField = {
        ...testField,
        type: 'invalid-type'
      };
      
      const result = await renderingEngine.renderField(invalidField, 'test', testContext);
      
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('syntax');
      expect(result.html).toContain('Error rendering field');
    });
  });

  describe('Field Group Rendering', () => {
    it('should render multiple fields as a group', async () => {
      const fields = [
        testField,
        {
          ...testField,
          id: 'test-field-2',
          name: 'testField2',
          label: 'Test Field 2',
          type: 'number'
        }
      ];
      
      const values = {
        testField: 'text value',
        testField2: 42
      };
      
      const result = await renderingEngine.renderFieldGroup(fields, values, testContext);
      
      expect(result.fields).toHaveLength(2);
      expect(result.layout).toContain('field-group-container');
      expect(result.totalRenderTime).toBeGreaterThan(0);
    });

    it('should group fields by their group property', async () => {
      const fields = [
        { ...testField, group: 'group1' },
        { ...testField, id: 'field-2', name: 'field2', group: 'group2' },
        { ...testField, id: 'field-3', name: 'field3', group: 'group1' }
      ];
      
      const values = {
        testField: 'value1',
        field2: 'value2',
        field3: 'value3'
      };
      
      const result = await renderingEngine.renderFieldGroup(fields, values, testContext);
      
      expect(result.layout).toContain('data-group="group1"');
      expect(result.layout).toContain('data-group="group2"');
      expect(result.layout).toContain('field-group-title');
    });

    it('should track cache performance', async () => {
      const fields = [testField];
      const values = { testField: 'test value' };
      
      // First render
      const result1 = await renderingEngine.renderFieldGroup(fields, values, testContext);
      expect(result1.cacheHits).toBe(0);
      expect(result1.cacheMisses).toBe(1);
      
      // Second render with same data should hit cache
      const result2 = await renderingEngine.renderFieldGroup(fields, values, testContext);
      expect(result2.cacheHits).toBe(1);
      expect(result2.cacheMisses).toBe(0);
    });
  });

  describe('Validation and Security', () => {
    it('should validate rendering permissions', async () => {
      const restrictedContext = {
        ...testContext,
        permissions: [] // No render permission
      };
      
      const result = await renderingEngine.renderField(testField, 'test', restrictedContext);
      
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toContain('permission');
    });

    it('should validate field configuration', async () => {
      const invalidField = {
        ...testField,
        validation: {
          pattern: '[invalid regex'
        }
      };
      
      const result = await renderingEngine.renderField(invalidField, 'test', testContext);
      
      // Should handle invalid regex gracefully
      expect(result).toBeDefined();
    });

    it('should prevent XSS in field labels and help text', async () => {
      const xssField = {
        ...testField,
        label: '<script>alert("xss")</script>',
        helpText: '<img src="x" onerror="alert(\'xss\')">'
      };
      
      const result = await renderingEngine.renderField(xssField, 'test', testContext);
      
      expect(result.html).not.toContain('<script>');
      expect(result.html).not.toContain('onerror=');
    });
  });

  describe('Performance', () => {
    it('should render fields within reasonable time', async () => {
      const startTime = Date.now();
      
      await renderingEngine.renderField(testField, 'test value', testContext);
      
      const renderTime = Date.now() - startTime;
      expect(renderTime).toBeLessThan(1000); // Should render within 1 second
    });

    it('should handle large field groups efficiently', async () => {
      const fields = Array.from({ length: 50 }, (_, i) => ({
        ...testField,
        id: `field-${i}`,
        name: `field${i}`,
        label: `Field ${i}`
      }));
      
      const values = Object.fromEntries(
        fields.map(field => [field.name, `value-${field.name}`])
      );
      
      const startTime = Date.now();
      const result = await renderingEngine.renderFieldGroup(fields, values, testContext);
      const renderTime = Date.now() - startTime;
      
      expect(result.fields).toHaveLength(50);
      expect(renderTime).toBeLessThan(5000); // Should render 50 fields within 5 seconds
    });

    it('should benefit from caching on repeated renders', async () => {
      const value = 'test value';
      
      // First render (cache miss)
      const startTime1 = Date.now();
      await renderingEngine.renderField(testField, value, testContext);
      const renderTime1 = Date.now() - startTime1;
      
      // Second render (cache hit)
      const startTime2 = Date.now();
      await renderingEngine.renderField(testField, value, testContext);
      const renderTime2 = Date.now() - startTime2;
      
      // Cache hit should be significantly faster
      expect(renderTime2).toBeLessThan(renderTime1);
    });
  });

  describe('Responsive and Theming', () => {
    it('should generate responsive CSS when enabled', async () => {
      const responsiveField = {
        ...testField,
        uiConfig: {
          ...testField.uiConfig,
          responsive: true
        }
      };
      
      const result = await renderingEngine.renderField(responsiveField, 'test', testContext);
      
      expect(result.css).toContain('@media');
      expect(result.css).toContain('max-width');
    });

    it('should apply custom CSS when provided', async () => {
      const customField = {
        ...testField,
        uiConfig: {
          ...testField.uiConfig,
          customCSS: '.custom-style { color: red; }'
        }
      };
      
      const result = await renderingEngine.renderField(customField, 'test', testContext);
      
      expect(result.css).toContain('.custom-style { color: red; }');
    });

    it('should include custom JavaScript when provided', async () => {
      const customField = {
        ...testField,
        uiConfig: {
          ...testField.uiConfig,
          customJS: 'console.log("Custom field JS");'
        }
      };
      
      const result = await renderingEngine.renderField(customField, 'test', testContext);
      
      expect(result.javascript).toContain('console.log("Custom field JS");');
    });
  });
});
