import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { createMocks } from 'node-mocks-http'
import { getServerSession } from 'next-auth'

// Mock the API route handlers
const mockGET = jest.fn()
const mockPOST = jest.fn()
const mockPUT = jest.fn()
const mockDELETE = jest.fn()
const mockGetSchedule = jest.fn()
const mockPostSchedule = jest.fn()

jest.mock('@/app/api/maintenance/tasks/route', () => ({
  GET: mockGET,
  POST: mockPOST,
  PUT: mockPUT,
  DELETE: mockDELETE,
}))

jest.mock('@/app/api/maintenance/schedule/route', () => ({
  GET: mockGetSchedule,
  POST: mockPostSchedule,
}))

// Mock dependencies
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

jest.mock('@/lib/prisma', () => ({
  maintenanceTask: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
    updateMany: jest.fn(),
    deleteMany: jest.fn(),
  },
  maintenanceWorkLog: {
    create: jest.fn(),
    createMany: jest.fn(),
    deleteMany: jest.fn(),
  },
  maintenanceNotification: {
    deleteMany: jest.fn(),
  },
  maintenanceCalendarEvent: {
    create: jest.fn(),
    deleteMany: jest.fn(),
  },
}))

jest.mock('@/lib/services/maintenance-service', () => ({
  maintenanceService: {
    getMaintenanceTasks: jest.fn(),
    createMaintenanceTask: jest.fn(),
    updateMaintenanceTask: jest.fn(),
    scheduleMaintenanceTask: jest.fn(),
  },
}))

const mockSession = {
  user: {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
  },
}

describe('Maintenance API Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(getServerSession as jest.Mock).mockResolvedValue(mockSession)
  })

  describe('/api/maintenance/tasks', () => {
    describe('GET', () => {
      it('should return maintenance tasks with pagination', async () => {
        const { req, res } = createMocks({
          method: 'GET',
          url: '/api/maintenance/tasks?page=1&limit=10&status=scheduled',
        })

        const mockTasks = [
          {
            id: 'task-1',
            title: 'Test Task',
            status: 'scheduled',
            scheduledDate: new Date(),
            dueDate: new Date(),
            isOverdue: false,
            isDueSoon: false,
            daysUntilDue: 5,
            completionPercentage: 0,
          },
        ]

        const { maintenanceService } = require('@/lib/services/maintenance-service')
        maintenanceService.getMaintenanceTasks.mockResolvedValue({
          tasks: mockTasks,
          total: 1,
          totalPages: 1,
        })

        await GET(req)

        expect(res._getStatusCode()).toBe(200)
        const responseData = JSON.parse(res._getData())
        expect(responseData.success).toBe(true)
        expect(responseData.data.tasks).toEqual(mockTasks)
        expect(responseData.data.pagination).toEqual({
          page: 1,
          limit: 50, // Default limit
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        })
      })

      it('should return 401 for unauthenticated requests', async () => {
        ;(getServerSession as jest.Mock).mockResolvedValue(null)

        const { req, res } = createMocks({
          method: 'GET',
          url: '/api/maintenance/tasks',
        })

        await GET(req)

        expect(res._getStatusCode()).toBe(401)
        const responseData = JSON.parse(res._getData())
        expect(responseData.success).toBe(false)
        expect(responseData.error).toBe('Unauthorized')
      })

      it('should handle filter parameters correctly', async () => {
        const { req, res } = createMocks({
          method: 'GET',
          url: '/api/maintenance/tasks?status=scheduled,in_progress&priority=high&assignedTo=tech-1',
        })

        const { maintenanceService } = require('@/lib/services/maintenance-service')
        maintenanceService.getMaintenanceTasks.mockResolvedValue({
          tasks: [],
          total: 0,
          totalPages: 0,
        })

        await GET(req)

        expect(maintenanceService.getMaintenanceTasks).toHaveBeenCalledWith(
          expect.objectContaining({
            status: ['scheduled', 'in_progress'],
            priority: ['high'],
            assignedTo: 'tech-1',
          }),
          expect.any(Object)
        )
      })
    })

    describe('POST', () => {
      it('should create a new maintenance task', async () => {
        const taskData = {
          title: 'New Maintenance Task',
          description: 'Test description',
          assetId: 'asset-1',
          type: 'preventive',
          priority: 'medium',
          status: 'scheduled',
          scheduledDate: new Date().toISOString(),
          dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          estimatedDuration: 120,
          estimatedCost: 500,
        }

        const { req, res } = createMocks({
          method: 'POST',
          url: '/api/maintenance/tasks',
          body: taskData,
        })

        const mockCreatedTask = { id: 'task-1', ...taskData }
        const { maintenanceService } = require('@/lib/services/maintenance-service')
        maintenanceService.createMaintenanceTask.mockResolvedValue(mockCreatedTask)

        await POST(req)

        expect(res._getStatusCode()).toBe(200)
        const responseData = JSON.parse(res._getData())
        expect(responseData.success).toBe(true)
        expect(responseData.data).toEqual(mockCreatedTask)
      })

      it('should return 400 for invalid task data', async () => {
        const invalidTaskData = {
          title: '', // Invalid: empty title
          assetId: 'invalid-id', // Invalid: not a CUID
        }

        const { req, res } = createMocks({
          method: 'POST',
          url: '/api/maintenance/tasks',
          body: invalidTaskData,
        })

        const { maintenanceService } = require('@/lib/services/maintenance-service')
        maintenanceService.createMaintenanceTask.mockRejectedValue(
          new Error('Validation failed: Title is required')
        )

        await POST(req)

        expect(res._getStatusCode()).toBe(400)
        const responseData = JSON.parse(res._getData())
        expect(responseData.success).toBe(false)
        expect(responseData.error).toContain('Validation failed')
      })
    })
  })

  describe('/api/maintenance/tasks/[id]', () => {
    describe('PUT', () => {
      it('should update an existing maintenance task', async () => {
        const taskId = 'task-1'
        const updateData = {
          status: 'completed',
          completedDate: new Date().toISOString(),
          actualCost: 450,
        }

        const { req, res } = createMocks({
          method: 'PUT',
          url: `/api/maintenance/tasks/${taskId}`,
          body: updateData,
        })

        const mockUpdatedTask = { id: taskId, ...updateData }
        const { maintenanceService } = require('@/lib/services/maintenance-service')
        maintenanceService.updateMaintenanceTask.mockResolvedValue(mockUpdatedTask)

        const { PUT: updateTask } = require('@/app/api/maintenance/tasks/[id]/route')
        await updateTask(req, { params: { id: taskId } })

        expect(res._getStatusCode()).toBe(200)
        const responseData = JSON.parse(res._getData())
        expect(responseData.success).toBe(true)
        expect(responseData.data).toEqual(mockUpdatedTask)
      })

      it('should return 404 for non-existent task', async () => {
        const taskId = 'non-existent-task'
        const updateData = { status: 'completed' }

        const { req, res } = createMocks({
          method: 'PUT',
          url: `/api/maintenance/tasks/${taskId}`,
          body: updateData,
        })

        const prisma = require('@/lib/prisma')
        prisma.maintenanceTask.findUnique.mockResolvedValue(null)

        const { PUT: updateTask } = require('@/app/api/maintenance/tasks/[id]/route')
        await updateTask(req, { params: { id: taskId } })

        expect(res._getStatusCode()).toBe(404)
        const responseData = JSON.parse(res._getData())
        expect(responseData.success).toBe(false)
        expect(responseData.error).toBe('Task not found')
      })
    })

    describe('DELETE', () => {
      it('should delete a maintenance task', async () => {
        const taskId = 'task-1'

        const { req, res } = createMocks({
          method: 'DELETE',
          url: `/api/maintenance/tasks/${taskId}`,
        })

        const mockExistingTask = {
          id: taskId,
          status: 'scheduled',
          title: 'Test Task',
        }

        const prisma = require('@/lib/prisma')
        prisma.maintenanceTask.findUnique.mockResolvedValue(mockExistingTask)
        prisma.maintenanceWorkLog.deleteMany.mockResolvedValue({ count: 0 })
        prisma.maintenanceNotification.deleteMany.mockResolvedValue({ count: 0 })
        prisma.maintenanceCalendarEvent.deleteMany.mockResolvedValue({ count: 0 })
        prisma.maintenanceTask.delete.mockResolvedValue(mockExistingTask)

        const { DELETE: deleteTask } = require('@/app/api/maintenance/tasks/[id]/route')
        await deleteTask(req, { params: { id: taskId } })

        expect(res._getStatusCode()).toBe(200)
        const responseData = JSON.parse(res._getData())
        expect(responseData.success).toBe(true)
        expect(responseData.data.deletedTaskId).toBe(taskId)
      })

      it('should not delete in-progress tasks', async () => {
        const taskId = 'task-1'

        const { req, res } = createMocks({
          method: 'DELETE',
          url: `/api/maintenance/tasks/${taskId}`,
        })

        const mockExistingTask = {
          id: taskId,
          status: 'in_progress',
          title: 'Test Task',
        }

        const prisma = require('@/lib/prisma')
        prisma.maintenanceTask.findUnique.mockResolvedValue(mockExistingTask)

        const { DELETE: deleteTask } = require('@/app/api/maintenance/tasks/[id]/route')
        await deleteTask(req, { params: { id: taskId } })

        expect(res._getStatusCode()).toBe(400)
        const responseData = JSON.parse(res._getData())
        expect(responseData.success).toBe(false)
        expect(responseData.error).toBe('Cannot delete task that is in progress')
      })
    })
  })

  describe('/api/maintenance/schedule', () => {
    describe('GET', () => {
      it('should return scheduling information', async () => {
        const { req, res } = createMocks({
          method: 'GET',
          url: '/api/maintenance/schedule?startDate=2024-01-01&endDate=2024-01-31&includeAvailability=true',
        })

        const mockScheduledTasks = [
          {
            id: 'task-1',
            title: 'Scheduled Task',
            scheduledDate: new Date('2024-01-15'),
            status: 'scheduled',
          },
        ]

        const prisma = require('@/lib/prisma')
        prisma.maintenanceTask.findMany.mockResolvedValue(mockScheduledTasks)

        await getSchedule(req)

        expect(res._getStatusCode()).toBe(200)
        const responseData = JSON.parse(res._getData())
        expect(responseData.success).toBe(true)
        expect(responseData.data.scheduledTasks).toEqual(mockScheduledTasks)
        expect(responseData.data.availability).toBeDefined()
        expect(responseData.data.utilization).toBeDefined()
      })
    })

    describe('POST', () => {
      it('should schedule a new maintenance task with optimization', async () => {
        const scheduleRequest = {
          taskData: {
            title: 'Optimized Task',
            assetId: 'asset-1',
            type: 'preventive',
            priority: 'medium',
            estimatedDuration: 120,
            preferredDate: new Date().toISOString(),
          },
          options: {
            autoSchedule: true,
            checkConflicts: true,
          },
        }

        const { req, res } = createMocks({
          method: 'POST',
          url: '/api/maintenance/schedule',
          body: scheduleRequest,
        })

        const mockSchedulingResult = {
          scheduledDate: new Date(),
          dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
          conflicts: [],
        }

        const mockCreatedTask = {
          id: 'task-1',
          ...scheduleRequest.taskData,
          ...mockSchedulingResult,
        }

        const { maintenanceService } = require('@/lib/services/maintenance-service')
        maintenanceService.scheduleMaintenanceTask.mockResolvedValue(mockSchedulingResult)

        const prisma = require('@/lib/prisma')
        prisma.maintenanceTask.create.mockResolvedValue(mockCreatedTask)
        prisma.maintenanceCalendarEvent.create.mockResolvedValue({})

        await postSchedule(req)

        expect(res._getStatusCode()).toBe(200)
        const responseData = JSON.parse(res._getData())
        expect(responseData.success).toBe(true)
        expect(responseData.data.task).toEqual(mockCreatedTask)
        expect(responseData.data.scheduling.conflicts).toEqual([])
      })
    })
  })

  describe('Bulk Operations', () => {
    describe('/api/maintenance/tasks/bulk', () => {
      it('should perform bulk updates on multiple tasks', async () => {
        const bulkUpdateData = {
          taskIds: ['task-1', 'task-2', 'task-3'],
          updates: {
            status: 'on_hold',
            assignedTo: 'tech-2',
          },
        }

        const { req, res } = createMocks({
          method: 'PUT',
          url: '/api/maintenance/tasks/bulk',
          body: bulkUpdateData,
        })

        const mockExistingTasks = [
          { id: 'task-1', status: 'scheduled' },
          { id: 'task-2', status: 'scheduled' },
          { id: 'task-3', status: 'scheduled' },
        ]

        const prisma = require('@/lib/prisma')
        prisma.maintenanceTask.findMany.mockResolvedValue(mockExistingTasks)
        prisma.maintenanceTask.updateMany.mockResolvedValue({ count: 3 })
        prisma.maintenanceWorkLog.createMany.mockResolvedValue({ count: 3 })

        const { PUT: bulkUpdate } = require('@/app/api/maintenance/tasks/bulk/route')
        await bulkUpdate(req)

        expect(res._getStatusCode()).toBe(200)
        const responseData = JSON.parse(res._getData())
        expect(responseData.success).toBe(true)
        expect(responseData.data.updatedCount).toBe(3)
      })

      it('should handle partial failures in bulk operations', async () => {
        const bulkUpdateData = {
          taskIds: ['task-1', 'non-existent-task'],
          updates: { status: 'completed' },
        }

        const { req, res } = createMocks({
          method: 'PUT',
          url: '/api/maintenance/tasks/bulk',
          body: bulkUpdateData,
        })

        const mockExistingTasks = [{ id: 'task-1', status: 'scheduled' }]

        const prisma = require('@/lib/prisma')
        prisma.maintenanceTask.findMany.mockResolvedValue(mockExistingTasks)

        const { PUT: bulkUpdate } = require('@/app/api/maintenance/tasks/bulk/route')
        await bulkUpdate(req)

        expect(res._getStatusCode()).toBe(404)
        const responseData = JSON.parse(res._getData())
        expect(responseData.success).toBe(false)
        expect(responseData.error).toBe('Some tasks not found')
        expect(responseData.details.missingIds).toEqual(['non-existent-task'])
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const { req, res } = createMocks({
        method: 'GET',
        url: '/api/maintenance/tasks',
      })

      const { maintenanceService } = require('@/lib/services/maintenance-service')
      maintenanceService.getMaintenanceTasks.mockRejectedValue(
        new Error('Database connection failed')
      )

      await GET(req)

      expect(res._getStatusCode()).toBe(500)
      const responseData = JSON.parse(res._getData())
      expect(responseData.success).toBe(false)
      expect(responseData.error).toBe('Internal server error')
    })

    it('should validate request data and return appropriate errors', async () => {
      const invalidData = {
        title: '', // Invalid
        assetId: 'invalid-id',
        type: 'invalid-type',
      }

      const { req, res } = createMocks({
        method: 'POST',
        url: '/api/maintenance/tasks',
        body: invalidData,
      })

      await POST(req)

      expect(res._getStatusCode()).toBe(400)
      const responseData = JSON.parse(res._getData())
      expect(responseData.success).toBe(false)
      expect(responseData.error).toContain('Invalid request data')
      expect(responseData.details).toBeDefined()
    })
  })

  describe('Performance Tests', () => {
    it('should handle concurrent requests efficiently', async () => {
      const requests = Array.from({ length: 10 }, () => {
        const { req } = createMocks({
          method: 'GET',
          url: '/api/maintenance/tasks',
        })
        return GET(req)
      })

      const { maintenanceService } = require('@/lib/services/maintenance-service')
      maintenanceService.getMaintenanceTasks.mockResolvedValue({
        tasks: [],
        total: 0,
        totalPages: 0,
      })

      const startTime = Date.now()
      await Promise.all(requests)
      const endTime = Date.now()

      expect(endTime - startTime).toBeLessThan(2000) // Should complete within 2 seconds
    })
  })
})
