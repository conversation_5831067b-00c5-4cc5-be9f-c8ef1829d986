import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals'
import { testA<PERSON><PERSON>and<PERSON> } from 'next-test-api-route-handler'
import { createMocks } from 'node-mocks-http'
import prisma from '@/lib/prisma'
import { maintenanceService } from '@/lib/services/maintenance-service'

// Test database setup
const testDb = {
  async setup() {
    // Create test data
    await prisma.assetType.create({
      data: {
        id: 'test-asset-type-1',
        name: 'Test HVAC System',
        code: 'HVAC-TEST',
        description: 'Test HVAC system for maintenance testing',
        icon: 'wind',
        color: '#3b82f6',
        category: 'HVAC',
        isActive: true,
      },
    })

    await prisma.asset.create({
      data: {
        id: 'test-asset-1',
        name: 'Test HVAC Unit #1',
        description: 'Test HVAC unit for maintenance testing',
        assetTypeId: 'test-asset-type-1',
        serialNumber: 'HVAC-001-TEST',
        location: 'Test Building A - Floor 2',
        status: 'operational',
        purchaseDate: new Date('2020-01-01'),
        warrantyExpiry: new Date('2025-01-01'),
        isActive: true,
      },
    })

    await prisma.user.create({
      data: {
        id: 'test-user-1',
        name: 'Test Technician',
        email: '<EMAIL>',
        role: 'technician',
      },
    })
  },

  async cleanup() {
    // Clean up test data
    await prisma.maintenanceWorkLog.deleteMany({
      where: { taskId: { startsWith: 'test-' } },
    })
    await prisma.maintenanceNotification.deleteMany({
      where: { taskId: { startsWith: 'test-' } },
    })
    await prisma.maintenanceCalendarEvent.deleteMany({
      where: { taskId: { startsWith: 'test-' } },
    })
    await prisma.maintenanceTask.deleteMany({
      where: { id: { startsWith: 'test-' } },
    })
    await prisma.asset.deleteMany({
      where: { id: { startsWith: 'test-' } },
    })
    await prisma.assetType.deleteMany({
      where: { id: { startsWith: 'test-' } },
    })
    await prisma.user.deleteMany({
      where: { id: { startsWith: 'test-' } },
    })
  },
}

describe('Maintenance System End-to-End Integration Tests', () => {
  beforeAll(async () => {
    await testDb.setup()
  })

  afterAll(async () => {
    await testDb.cleanup()
    await prisma.$disconnect()
  })

  describe('Complete Maintenance Task Lifecycle', () => {
    let taskId: string

    it('should create a maintenance task', async () => {
      const taskData = {
        title: 'Test HVAC Maintenance',
        description: 'Comprehensive HVAC system maintenance and inspection',
        assetId: 'test-asset-1',
        type: 'preventive',
        priority: 'medium',
        status: 'scheduled',
        scheduledDate: new Date('2024-02-15T10:00:00Z'),
        dueDate: new Date('2024-02-15T14:00:00Z'),
        estimatedDuration: 240, // 4 hours
        estimatedCost: 800,
        assignedTo: 'test-user-1',
        instructions: 'Perform complete HVAC system inspection and maintenance',
        safetyNotes: 'Ensure power is disconnected before starting work',
      }

      const createdTask = await maintenanceService.createMaintenanceTask(taskData)
      taskId = createdTask.id

      expect(createdTask).toMatchObject({
        title: taskData.title,
        assetId: taskData.assetId,
        type: taskData.type,
        priority: taskData.priority,
        status: taskData.status,
      })
      expect(createdTask.id).toBeDefined()
      expect(createdTask.createdAt).toBeDefined()
    })

    it('should retrieve the created task', async () => {
      const result = await maintenanceService.getMaintenanceTasks(
        { assetId: 'test-asset-1' },
        { page: 1, limit: 10, offset: 0 }
      )

      expect(result.tasks).toHaveLength(1)
      expect(result.tasks[0].id).toBe(taskId)
      expect(result.tasks[0].title).toBe('Test HVAC Maintenance')
    })

    it('should update task status to in_progress', async () => {
      const updatedTask = await maintenanceService.updateMaintenanceTask(taskId, {
        status: 'in_progress',
        actualStartDate: new Date(),
      })

      expect(updatedTask.status).toBe('in_progress')
      expect(updatedTask.actualStartDate).toBeDefined()
    })

    it('should add work log entries', async () => {
      await prisma.maintenanceWorkLog.create({
        data: {
          taskId,
          userId: 'test-user-1',
          userName: 'Test Technician',
          action: 'started',
          description: 'Started HVAC maintenance work',
          timeSpent: 0,
          timestamp: new Date(),
        },
      })

      await prisma.maintenanceWorkLog.create({
        data: {
          taskId,
          userId: 'test-user-1',
          userName: 'Test Technician',
          action: 'progress_update',
          description: 'Completed filter replacement',
          timeSpent: 60,
          timestamp: new Date(),
        },
      })

      const workLogs = await prisma.maintenanceWorkLog.findMany({
        where: { taskId },
        orderBy: { timestamp: 'asc' },
      })

      expect(workLogs).toHaveLength(2)
      expect(workLogs[0].action).toBe('started')
      expect(workLogs[1].action).toBe('progress_update')
    })

    it('should complete the task', async () => {
      const completedTask = await maintenanceService.updateMaintenanceTask(taskId, {
        status: 'completed',
        completedDate: new Date(),
        actualDuration: 220, // 3 hours 40 minutes
        actualCost: 750,
        completionNotes: 'HVAC maintenance completed successfully. All systems operational.',
      })

      expect(completedTask.status).toBe('completed')
      expect(completedTask.completedDate).toBeDefined()
      expect(completedTask.actualDuration).toBe(220)
      expect(completedTask.actualCost).toBe(750)
    })

    it('should generate completion work log', async () => {
      await prisma.maintenanceWorkLog.create({
        data: {
          taskId,
          userId: 'test-user-1',
          userName: 'Test Technician',
          action: 'completed',
          description: 'Task completed successfully',
          timeSpent: 220,
          timestamp: new Date(),
        },
      })

      const completionLog = await prisma.maintenanceWorkLog.findFirst({
        where: {
          taskId,
          action: 'completed',
        },
      })

      expect(completionLog).toBeDefined()
      expect(completionLog?.timeSpent).toBe(220)
    })
  })

  describe('Scheduling and Conflict Detection', () => {
    it('should detect scheduling conflicts', async () => {
      // Create first task
      const task1Data = {
        title: 'First Task',
        assetId: 'test-asset-1',
        type: 'preventive',
        priority: 'medium',
        status: 'scheduled',
        scheduledDate: new Date('2024-03-01T10:00:00Z'),
        dueDate: new Date('2024-03-01T12:00:00Z'),
        assignedTo: 'test-user-1',
        estimatedDuration: 120,
      }

      const task1 = await maintenanceService.createMaintenanceTask(task1Data)

      // Create conflicting task
      const task2Data = {
        title: 'Conflicting Task',
        assetId: 'test-asset-1',
        type: 'corrective',
        priority: 'high',
        status: 'scheduled',
        scheduledDate: new Date('2024-03-01T11:00:00Z'), // Overlaps with task1
        dueDate: new Date('2024-03-01T13:00:00Z'),
        assignedTo: 'test-user-1', // Same technician
        estimatedDuration: 120,
      }

      const schedulingResult = await maintenanceService.scheduleMaintenanceTask(task2Data)

      expect(schedulingResult.conflicts).toHaveLength(1)
      expect(schedulingResult.conflicts[0].id).toBe(task1.id)
    })

    it('should optimize scheduling to avoid conflicts', async () => {
      const taskData = {
        title: 'Optimized Task',
        assetId: 'test-asset-1',
        type: 'preventive',
        priority: 'low',
        scheduledDate: new Date('2024-03-01T11:30:00Z'), // Would conflict
        assignedTo: 'test-user-1',
        estimatedDuration: 90,
      }

      const schedulingResult = await maintenanceService.scheduleMaintenanceTask(
        taskData,
        {
          considerWorkingHours: true,
          workingHours: { start: '09:00', end: '17:00' },
          excludeWeekends: true,
          bufferMinutes: 30,
        }
      )

      // Should be rescheduled to avoid conflict
      expect(schedulingResult.scheduledDate).not.toEqual(taskData.scheduledDate)
      expect(schedulingResult.conflicts).toHaveLength(0)
    })
  })

  describe('Predictive Maintenance Integration', () => {
    it('should generate maintenance recommendations', async () => {
      // Create maintenance history
      const historicalTasks = [
        {
          title: 'Previous Maintenance 1',
          assetId: 'test-asset-1',
          type: 'corrective',
          status: 'completed',
          scheduledDate: new Date('2024-01-01T10:00:00Z'),
          dueDate: new Date('2024-01-01T12:00:00Z'),
          completedDate: new Date('2024-01-01T11:30:00Z'),
          actualCost: 500,
        },
        {
          title: 'Previous Maintenance 2',
          assetId: 'test-asset-1',
          type: 'corrective',
          status: 'completed',
          scheduledDate: new Date('2024-01-15T10:00:00Z'),
          dueDate: new Date('2024-01-15T12:00:00Z'),
          completedDate: new Date('2024-01-15T12:30:00Z'),
          actualCost: 750,
        },
      ]

      for (const taskData of historicalTasks) {
        await maintenanceService.createMaintenanceTask(taskData)
      }

      const recommendations = await maintenanceService.generateMaintenanceRecommendations('test-asset-1')

      expect(recommendations).toHaveLength(1)
      expect(recommendations[0].recommendationType).toBe('inspect_asset')
      expect(recommendations[0].priority).toBe('high')
      expect(recommendations[0].reasoning).toContain('failure risk')
    })

    it('should create predictive maintenance prediction', async () => {
      const predictionInput = {
        assetId: 'test-asset-1',
        features: {
          age_months: 48, // 4 years old
          usage_hours: 8760, // 1 year of continuous operation
          maintenance_count: 8,
          failure_count: 2,
          temperature_variance: 15.5,
          vibration_level: 2.3,
        },
      }

      // Mock a predictive maintenance model
      await prisma.predictiveMaintenanceModel.create({
        data: {
          id: 'test-model-1',
          name: 'Test Failure Prediction Model',
          description: 'Test model for failure prediction',
          modelType: 'time_series',
          algorithm: 'linear_regression',
          version: 1,
          accuracy: 0.85,
          isActive: true,
          trainingData: JSON.stringify({ features: ['age_months', 'usage_hours'] }),
          hyperparameters: JSON.stringify({ learning_rate: 0.01 }),
        },
      })

      const prediction = await maintenanceService.createPredictiveMaintenance(predictionInput)

      expect(prediction.assetId).toBe('test-asset-1')
      expect(prediction.predictedValue).toBeGreaterThan(0)
      expect(prediction.predictedValue).toBeLessThanOrEqual(1)
      expect(prediction.confidence).toBeGreaterThan(0)
      expect(prediction.confidence).toBeLessThanOrEqual(1)
    })
  })

  describe('Notification System Integration', () => {
    it('should create notifications for due tasks', async () => {
      const dueSoonTask = await maintenanceService.createMaintenanceTask({
        title: 'Due Soon Task',
        assetId: 'test-asset-1',
        type: 'preventive',
        priority: 'medium',
        status: 'scheduled',
        scheduledDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        assignedTo: 'test-user-1',
      })

      // Create notification
      await prisma.maintenanceNotification.create({
        data: {
          taskId: dueSoonTask.id,
          recipientId: 'test-user-1',
          type: 'due_soon',
          title: 'Task Due Soon',
          message: `${dueSoonTask.title} is due in 2 days`,
          status: 'sent',
          scheduledFor: new Date(),
        },
      })

      const notifications = await prisma.maintenanceNotification.findMany({
        where: { recipientId: 'test-user-1' },
      })

      expect(notifications).toHaveLength(1)
      expect(notifications[0].type).toBe('due_soon')
      expect(notifications[0].taskId).toBe(dueSoonTask.id)
    })
  })

  describe('Analytics and Reporting', () => {
    it('should calculate maintenance statistics', async () => {
      // Get all tasks for the test asset
      const result = await maintenanceService.getMaintenanceTasks(
        { assetId: 'test-asset-1' },
        { page: 1, limit: 100, offset: 0 }
      )

      const tasks = result.tasks
      const completedTasks = tasks.filter(t => t.status === 'completed')
      const scheduledTasks = tasks.filter(t => t.status === 'scheduled')
      const inProgressTasks = tasks.filter(t => t.status === 'in_progress')

      expect(tasks.length).toBeGreaterThan(0)
      expect(completedTasks.length).toBeGreaterThan(0)

      // Calculate completion rate
      const completionRate = (completedTasks.length / tasks.length) * 100
      expect(completionRate).toBeGreaterThan(0)
      expect(completionRate).toBeLessThanOrEqual(100)

      // Calculate average cost
      const totalCost = completedTasks.reduce((sum, task) => sum + (task.actualCost || 0), 0)
      const averageCost = completedTasks.length > 0 ? totalCost / completedTasks.length : 0
      expect(averageCost).toBeGreaterThanOrEqual(0)
    })

    it('should track maintenance trends over time', async () => {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      const now = new Date()

      const recentTasks = await prisma.maintenanceTask.findMany({
        where: {
          assetId: 'test-asset-1',
          createdAt: {
            gte: thirtyDaysAgo,
            lte: now,
          },
        },
        orderBy: { createdAt: 'asc' },
      })

      // Group tasks by week
      const weeklyTasks = recentTasks.reduce((acc, task) => {
        const week = Math.floor((task.createdAt.getTime() - thirtyDaysAgo.getTime()) / (7 * 24 * 60 * 60 * 1000))
        acc[week] = (acc[week] || 0) + 1
        return acc
      }, {} as Record<number, number>)

      expect(Object.keys(weeklyTasks).length).toBeGreaterThan(0)
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle bulk operations efficiently', async () => {
      const bulkTasks = Array.from({ length: 50 }, (_, i) => ({
        title: `Bulk Task ${i + 1}`,
        assetId: 'test-asset-1',
        type: 'preventive',
        priority: 'low',
        status: 'scheduled',
        scheduledDate: new Date(Date.now() + i * 24 * 60 * 60 * 1000),
        dueDate: new Date(Date.now() + (i + 1) * 24 * 60 * 60 * 1000),
      }))

      const startTime = Date.now()
      
      const createdTasks = await Promise.all(
        bulkTasks.map(taskData => maintenanceService.createMaintenanceTask(taskData))
      )

      const endTime = Date.now()
      const duration = endTime - startTime

      expect(createdTasks).toHaveLength(50)
      expect(duration).toBeLessThan(5000) // Should complete within 5 seconds
    })

    it('should handle large dataset queries efficiently', async () => {
      const startTime = Date.now()
      
      const result = await maintenanceService.getMaintenanceTasks(
        {},
        { page: 1, limit: 100, offset: 0 }
      )

      const endTime = Date.now()
      const duration = endTime - startTime

      expect(result.tasks.length).toBeGreaterThan(0)
      expect(duration).toBeLessThan(1000) // Should complete within 1 second
    })
  })

  describe('Error Handling and Recovery', () => {
    it('should handle invalid data gracefully', async () => {
      const invalidTaskData = {
        title: '', // Invalid: empty title
        assetId: 'non-existent-asset',
        type: 'invalid-type',
      }

      await expect(
        maintenanceService.createMaintenanceTask(invalidTaskData)
      ).rejects.toThrow('Validation failed')
    })

    it('should handle database constraint violations', async () => {
      const taskData = {
        title: 'Test Task',
        assetId: 'non-existent-asset', // This should cause a foreign key constraint error
        type: 'preventive',
        priority: 'medium',
        status: 'scheduled',
        scheduledDate: new Date(),
        dueDate: new Date(),
      }

      await expect(
        maintenanceService.createMaintenanceTask(taskData)
      ).rejects.toThrow()
    })
  })
})
