"use client";

import { useToast } from "@/hooks/use-toast";
import { Heart, Star } from "lucide-react";
import { 
  AuthContext, 
  AlertType, 
  getRandomAlertMessage, 
  getContextualErrorMessage,
  CreativeAlertMessage 
} from "@/lib/auth/creative-alerts-config";

export interface UseCreativeAlertsOptions {
  context: AuthContext;
  onSuccess?: () => void;
  onError?: (error: any) => void;
  successDelay?: number;
}

export interface CreativeAlertHook {
  showSuccessAlert: (customMessage?: Partial<CreativeAlertMessage>) => void;
  showErrorAlert: (error?: any, customMessage?: Partial<CreativeAlertMessage>) => void;
  showInfoAlert: (message: string, description?: string) => void;
  showWarningAlert: (message: string, description?: string) => void;
}

export const useCreativeAlerts = (options: UseCreativeAlertsOptions): CreativeAlertHook => {
  const { toast } = useToast();
  const { context, onSuccess, onError, successDelay = 1500 } = options;

  const showSuccessAlert = (customMessage?: Partial<CreativeAlertMessage>) => {
    const alertMessage = customMessage 
      ? { ...getRandomAlertMessage(context, 'success'), ...customMessage }
      : getRandomAlertMessage(context, 'success');

    if (!alertMessage) return;

    const SuccessIcon = alertMessage.icon;
    
    toast({
      title: (
        <div className="flex items-center gap-2">
          <SuccessIcon className="h-5 w-5 text-green-500 animate-bounce" />
          <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent font-bold">
            {alertMessage.title}
          </span>
        </div>
      ),
      description: (
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">{alertMessage.description}</p>
          <div className="flex items-center gap-1 text-xs text-green-600">
            <Heart className="h-3 w-3 animate-pulse" />
            <span>Processing your request...</span>
          </div>
        </div>
      ),
      className: "border-green-200 bg-green-50 dark:bg-green-950 dark:border-green-800 animate-slideInBounce",
      duration: alertMessage.duration || 4000,
    });

    // Add success glow effect to the page
    const bodyElement = document.body;
    bodyElement.classList.add('success-glow');
    setTimeout(() => {
      bodyElement.classList.remove('success-glow');
    }, 2000);

    // Call success callback with delay
    if (onSuccess) {
      setTimeout(() => {
        onSuccess();
      }, successDelay);
    }
  };

  const showErrorAlert = (error?: any, customMessage?: Partial<CreativeAlertMessage>) => {
    const alertMessage = customMessage 
      ? { ...getContextualErrorMessage(context, error), ...customMessage }
      : getContextualErrorMessage(context, error);

    const ErrorIcon = alertMessage.icon;
    
    toast({
      title: (
        <div className="flex items-center gap-2">
          <ErrorIcon className="h-5 w-5 text-red-500 animate-pulse" />
          <span className="bg-gradient-to-r from-red-600 to-rose-600 bg-clip-text text-transparent font-bold">
            {alertMessage.title}
          </span>
        </div>
      ),
      description: (
        <div className="space-y-3">
          <p className="text-sm text-muted-foreground">{alertMessage.description}</p>
          {alertMessage.hint && (
            <div className="p-2 bg-blue-50 dark:bg-blue-950 rounded-md border border-blue-200 dark:border-blue-800">
              <p className="text-xs text-blue-700 dark:text-blue-300">{alertMessage.hint}</p>
            </div>
          )}
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Star className="h-3 w-3" />
            <span>Don't give up! You're one step away from success!</span>
          </div>
        </div>
      ),
      variant: "destructive",
      className: "border-red-200 bg-red-50 dark:bg-red-950 dark:border-red-800 animate-shakeError",
      duration: alertMessage.duration || 6000,
    });

    // Add error shake effect to the form
    const formElement = document.querySelector('form');
    if (formElement) {
      formElement.classList.add('animate-pulse');
      setTimeout(() => {
        formElement.classList.remove('animate-pulse');
      }, 600);
    }

    // Add error glow effect to the page
    const bodyElement = document.body;
    bodyElement.classList.add('error-glow');
    setTimeout(() => {
      bodyElement.classList.remove('error-glow');
    }, 2000);

    // Call error callback
    if (onError) {
      onError(error);
    }
  };

  const showInfoAlert = (message: string, description?: string) => {
    const alertMessage = getRandomAlertMessage(context, 'info');
    const InfoIcon = alertMessage?.icon || Star;
    
    toast({
      title: (
        <div className="flex items-center gap-2">
          <InfoIcon className="h-5 w-5 text-blue-500 animate-pulse" />
          <span className="bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent font-bold">
            {message}
          </span>
        </div>
      ),
      description: description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      ),
      className: "border-blue-200 bg-blue-50 dark:bg-blue-950 dark:border-blue-800",
      duration: 4000,
    });
  };

  const showWarningAlert = (message: string, description?: string) => {
    const alertMessage = getRandomAlertMessage(context, 'warning');
    const WarningIcon = alertMessage?.icon || Star;
    
    toast({
      title: (
        <div className="flex items-center gap-2">
          <WarningIcon className="h-5 w-5 text-yellow-500 animate-pulse" />
          <span className="bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent font-bold">
            {message}
          </span>
        </div>
      ),
      description: description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      ),
      className: "border-yellow-200 bg-yellow-50 dark:bg-yellow-950 dark:border-yellow-800",
      duration: 5000,
    });
  };

  return {
    showSuccessAlert,
    showErrorAlert,
    showInfoAlert,
    showWarningAlert
  };
};

// Utility function for quick alerts without hook setup
export const createQuickAlert = (context: AuthContext) => {
  return {
    success: (customMessage?: Partial<CreativeAlertMessage>) => {
      const hook = useCreativeAlerts({ context });
      hook.showSuccessAlert(customMessage);
    },
    error: (error?: any, customMessage?: Partial<CreativeAlertMessage>) => {
      const hook = useCreativeAlerts({ context });
      hook.showErrorAlert(error, customMessage);
    },
    info: (message: string, description?: string) => {
      const hook = useCreativeAlerts({ context });
      hook.showInfoAlert(message, description);
    },
    warning: (message: string, description?: string) => {
      const hook = useCreativeAlerts({ context });
      hook.showWarningAlert(message, description);
    }
  };
};

// Type exports for external use
export type { CreativeAlertMessage, AuthContext, AlertType };
