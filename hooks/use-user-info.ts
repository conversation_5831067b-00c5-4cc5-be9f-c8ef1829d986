"use client";

import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";
import { useState, useCallback, useMemo } from "react";
import { 
  User, 
  Settings, 
  LogOut, 
  Shield, 
  Bell, 
  HelpCircle, 
  Palette,
  Code,
  Layers,
  CreditCard,
  UserCog,
  Activity,
  Mail,
  Phone,
  Building,
  Calendar
} from "lucide-react";

export interface UserAction {
  id: string;
  label: string;
  icon: any;
  href?: string;
  onClick?: () => void;
  variant?: 'default' | 'destructive';
  separator?: boolean;
  badge?: string | number;
  disabled?: boolean;
  description?: string;
}

export interface UserInfoConfig {
  showAvatar?: boolean;
  showStatus?: boolean;
  showRole?: boolean;
  showLastActive?: boolean;
  avatarSize?: 'sm' | 'md' | 'lg';
  layout?: 'horizontal' | 'vertical';
  actions?: UserAction[];
  customActions?: UserAction[];
}

export interface UseUserInfoOptions {
  config?: UserInfoConfig;
  onLogout?: () => void;
  onProfileClick?: () => void;
  onSettingsClick?: () => void;
}

export interface UserInfoHook {
  user: any;
  isLoading: boolean;
  error: string | null;
  userActions: UserAction[];
  userDisplayInfo: {
    name: string;
    email: string;
    role: string;
    status: string;
    avatarUrl?: string;
    initials: string;
    statusColor: string;
    roleColor: string;
    lastActive?: string;
    department?: string;
    company?: string;
    phone?: string;
  };
  handleAction: (actionId: string) => void;
  refreshUserInfo: () => void;
}

const DEFAULT_CONFIG: UserInfoConfig = {
  showAvatar: true,
  showStatus: true,
  showRole: true,
  showLastActive: false,
  avatarSize: 'md',
  layout: 'horizontal',
  actions: []
};

export const useUserInfo = (options: UseUserInfoOptions = {}): UserInfoHook => {
  const { user, isLoading, error, logout } = useAuth();
  const router = useRouter();
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const config = { ...DEFAULT_CONFIG, ...options.config };

  // Generate user initials
  const getUserInitials = useCallback((name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }, []);

  // Get status color
  const getStatusColor = useCallback((status: string): string => {
    const statusColors: Record<string, string> = {
      'active': 'text-green-600 bg-green-100',
      'inactive': 'text-gray-600 bg-gray-100',
      'suspended': 'text-red-600 bg-red-100',
      'pending_verification': 'text-yellow-600 bg-yellow-100'
    };
    return statusColors[status] || 'text-gray-600 bg-gray-100';
  }, []);

  // Get role color
  const getRoleColor = useCallback((role: string): string => {
    const roleColors: Record<string, string> = {
      'admin': 'text-purple-600 bg-purple-100',
      'manager': 'text-blue-600 bg-blue-100',
      'user': 'text-green-600 bg-green-100',
      'client': 'text-orange-600 bg-orange-100',
      'technician': 'text-cyan-600 bg-cyan-100'
    };
    return roleColors[role] || 'text-gray-600 bg-gray-100';
  }, []);

  // Format last active time
  const formatLastActive = useCallback((lastActive: string): string => {
    const date = new Date(lastActive);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  }, []);

  // User display information
  const userDisplayInfo = useMemo(() => {
    if (!user) {
      return {
        name: 'Guest User',
        email: '',
        role: 'guest',
        status: 'inactive',
        initials: 'GU',
        statusColor: 'text-gray-600 bg-gray-100',
        roleColor: 'text-gray-600 bg-gray-100'
      };
    }

    return {
      name: user.name || 'Unknown User',
      email: user.email || '',
      role: user.role || 'user',
      status: user.status || 'active',
      avatarUrl: user.avatarUrl,
      initials: getUserInitials(user.name || 'Unknown User'),
      statusColor: getStatusColor(user.status || 'active'),
      roleColor: getRoleColor(user.role || 'user'),
      lastActive: user.lastActive ? formatLastActive(user.lastActive) : undefined,
      department: user.department,
      company: user.company,
      phone: user.phone
    };
  }, [user, getUserInitials, getStatusColor, getRoleColor, formatLastActive]);

  // Default user actions
  const defaultActions: UserAction[] = useMemo(() => [
    {
      id: 'profile',
      label: 'Profile',
      icon: User,
      href: '/admin/profile',
      description: 'View and edit your profile'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      href: '/admin/settings',
      description: 'Account and application settings'
    },
    {
      id: 'asset-module-editor',
      label: 'Asset Module Editor',
      icon: Code,
      href: '/module-editor/new',
      description: 'Create and edit asset modules',
      badge: 'New'
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: Bell,
      href: '/admin/notifications',
      badge: user?.unreadNotifications || 0,
      description: 'View your notifications'
    },
    {
      id: 'separator-1',
      label: '',
      icon: null,
      separator: true
    },
    {
      id: 'account-security',
      label: 'Security',
      icon: Shield,
      href: '/admin/security',
      description: 'Manage your account security'
    },
    {
      id: 'billing',
      label: 'Billing',
      icon: CreditCard,
      href: '/admin/billing',
      description: 'Manage billing and subscriptions'
    },
    {
      id: 'activity',
      label: 'Activity Log',
      icon: Activity,
      href: '/admin/activity',
      description: 'View your recent activity'
    },
    {
      id: 'separator-2',
      label: '',
      icon: null,
      separator: true
    },
    {
      id: 'help',
      label: 'Help & Support',
      icon: HelpCircle,
      href: '/help',
      description: 'Get help and support'
    },
    {
      id: 'logout',
      label: 'Sign out',
      icon: LogOut,
      variant: 'destructive' as const,
      description: 'Sign out of your account'
    }
  ], [user]);

  // Combine default and custom actions
  const userActions = useMemo(() => {
    const actions = [...defaultActions];
    
    if (config.customActions) {
      // Insert custom actions before the logout action
      const logoutIndex = actions.findIndex(action => action.id === 'logout');
      if (logoutIndex > 0) {
        actions.splice(logoutIndex, 0, ...config.customActions);
      } else {
        actions.push(...config.customActions);
      }
    }

    return actions.filter(action => {
      // Filter out disabled actions
      if (action.disabled) return false;
      
      // Filter based on user role if needed
      if (action.id === 'asset-module-editor' && user?.role !== 'admin') {
        return false;
      }
      
      return true;
    });
  }, [defaultActions, config.customActions, user]);

  // Handle action clicks
  const handleAction = useCallback(async (actionId: string) => {
    const action = userActions.find(a => a.id === actionId);
    if (!action) return;

    try {
      if (action.onClick) {
        action.onClick();
      } else if (action.href) {
        router.push(action.href);
      } else if (actionId === 'logout') {
        if (options.onLogout) {
          options.onLogout();
        } else {
          await logout();
        }
      } else if (actionId === 'profile' && options.onProfileClick) {
        options.onProfileClick();
      } else if (actionId === 'settings' && options.onSettingsClick) {
        options.onSettingsClick();
      }
    } catch (error) {
      console.error(`Error handling action ${actionId}:`, error);
    }
  }, [userActions, router, logout, options]);

  // Refresh user info
  const refreshUserInfo = useCallback(async () => {
    setIsRefreshing(true);
    try {
      // This would typically call a refresh method from the auth context
      // For now, we'll just simulate a refresh
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Error refreshing user info:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  return {
    user,
    isLoading: isLoading || isRefreshing,
    error,
    userActions,
    userDisplayInfo,
    handleAction,
    refreshUserInfo
  };
};
