import prisma from "@/lib/prisma";
import { MaintenanceTask, Asset, MaintenanceSchedule } from "@prisma/client";
import {
  MaintenanceTaskCreateSchema,
  MaintenanceTaskUpdateSchema,
  MaintenanceTaskFilter,
  MaintenanceStatistics,
  MaintenanceFrequency,
  MaintenancePredictionCreate,
  MaintenanceNotificationCreate
} from "@/lib/schemas/maintenance";
import { z } from "zod";

// Enhanced type definitions
export type MaintenanceTaskWithAsset = MaintenanceTask & {
  asset?: {
    id: string;
    name: string;
    category: string;
    location: string;
    assetType?: {
      id: string;
      name: string;
      code: string;
      icon: string;
      color: string;
    };
  };
  schedule?: {
    id: string;
    name: string;
    type: string;
    frequency: string;
    priority: string;
  };
  workLogs?: Array<{
    id: string;
    action: string;
    description: string;
    timeSpent: number;
    timestamp: Date;
    userName: string;
  }>;
  notifications?: Array<{
    id: string;
    type: string;
    status: string;
    scheduledFor: Date;
  }>;
};

export interface Pagination {
  page: number;
  limit: number;
  offset: number;
}

export interface SchedulingOptions {
  considerWorkingHours?: boolean;
  workingHours?: {
    start: string; // "09:00"
    end: string;   // "17:00"
  };
  excludeWeekends?: boolean;
  excludeHolidays?: boolean;
  holidays?: Date[];
  bufferMinutes?: number; // Buffer between tasks
}

export interface PredictiveMaintenanceInput {
  assetId: string;
  features: Record<string, number>;
  modelId?: string;
}

export interface MaintenanceRecommendation {
  assetId: string;
  recommendationType: "schedule_maintenance" | "replace_part" | "inspect_asset" | "defer_maintenance";
  priority: "low" | "medium" | "high" | "critical";
  reasoning: string;
  suggestedDate?: Date;
  estimatedCost?: number;
  confidence: number; // 0-1
  supportingData: Record<string, any>;
}

export class MaintenanceService {
  /**
   * Get all maintenance tasks with advanced filtering and pagination
   */
  async getMaintenanceTasks(
    filter: MaintenanceTaskFilter,
    pagination: Pagination
  ): Promise<{ tasks: MaintenanceTaskWithAsset[]; total: number; totalPages: number }> {
    try {
      // Build where clause from filter
      const where: any = {};

      // Status filtering (array support)
      if (filter.status && filter.status.length > 0) {
        where.status = { in: filter.status };
      }

      // Priority filtering (array support)
      if (filter.priority && filter.priority.length > 0) {
        where.priority = { in: filter.priority };
      }

      // Type filtering (array support)
      if (filter.type && filter.type.length > 0) {
        where.type = { in: filter.type };
      }

      // Asset filtering
      if (filter.assetId) {
        where.assetId = filter.assetId;
      }

      // Asset type filtering
      if (filter.assetTypeId) {
        where.asset = {
          assetTypeId: filter.assetTypeId,
        };
      }

      // Assignment filtering
      if (filter.assignedTo) {
        where.assignedTo = filter.assignedTo;
      }

      if (filter.assignedTeam) {
        where.assignedTeam = filter.assignedTeam;
      }

      // Date range filtering
      if (filter.scheduledDateFrom || filter.scheduledDateTo) {
        where.scheduledDate = {};
        if (filter.scheduledDateFrom) {
          where.scheduledDate.gte = filter.scheduledDateFrom;
        }
        if (filter.scheduledDateTo) {
          where.scheduledDate.lte = filter.scheduledDateTo;
        }
      }

      if (filter.dueDateFrom || filter.dueDateTo) {
        where.dueDate = {};
        if (filter.dueDateFrom) {
          where.dueDate.gte = filter.dueDateFrom;
        }
        if (filter.dueDateTo) {
          where.dueDate.lte = filter.dueDateTo;
        }
      }

      // Tags filtering
      if (filter.tags && filter.tags.length > 0) {
        where.tags = {
          hasSome: filter.tags,
        };
      }

      // Search filtering
      if (filter.search) {
        where.OR = [
          { title: { contains: filter.search, mode: "insensitive" } },
          { description: { contains: filter.search, mode: "insensitive" } },
          { instructions: { contains: filter.search, mode: "insensitive" } },
          { completionNotes: { contains: filter.search, mode: "insensitive" } },
        ];
      }

      // Calculate pagination
      const skip = pagination.offset;
      const take = pagination.limit;

      // Execute queries with enhanced includes
      const [tasks, total] = await Promise.all([
        prisma.maintenanceTask.findMany({
          where,
          skip,
          take,
          orderBy: { scheduledDate: "asc" },
          include: {
            asset: {
              select: {
                id: true,
                name: true,
                category: true,
                location: true,
                assetType: {
                  select: {
                    id: true,
                    name: true,
                    code: true,
                    icon: true,
                    color: true,
                  },
                },
              },
            },
            schedule: {
              select: {
                id: true,
                name: true,
                type: true,
                frequency: true,
                priority: true,
              },
            },
            workLogs: {
              select: {
                id: true,
                action: true,
                description: true,
                timeSpent: true,
                timestamp: true,
                userName: true,
              },
              orderBy: { timestamp: "desc" },
              take: 5, // Latest 5 work logs
            },
            notifications: {
              select: {
                id: true,
                type: true,
                status: true,
                scheduledFor: true,
              },
              where: { status: { not: "dismissed" } },
            },
          },
        }),
        prisma.maintenanceTask.count({ where }),
      ]);

      const totalPages = Math.ceil(total / pagination.limit);

      return {
        tasks: tasks as MaintenanceTaskWithAsset[],
        total,
        totalPages,
      };
    } catch (error) {
      console.error("Error in getMaintenanceTasks:", error);
      throw new Error("Failed to fetch maintenance tasks");
    }
  }

  /**
   * Advanced scheduling algorithm that considers working hours, conflicts, and resource availability
   */
  async scheduleMaintenanceTask(
    taskData: any,
    options: SchedulingOptions = {}
  ): Promise<{ scheduledDate: Date; dueDate: Date; conflicts: any[] }> {
    try {
      const {
        considerWorkingHours = true,
        workingHours = { start: "09:00", end: "17:00" },
        excludeWeekends = true,
        excludeHolidays = true,
        holidays = [],
        bufferMinutes = 30,
      } = options;

      let proposedDate = new Date(taskData.scheduledDate);

      // Adjust for working hours
      if (considerWorkingHours) {
        proposedDate = this.adjustToWorkingHours(proposedDate, workingHours);
      }

      // Adjust for weekends
      if (excludeWeekends) {
        proposedDate = this.adjustForWeekends(proposedDate);
      }

      // Adjust for holidays
      if (excludeHolidays && holidays.length > 0) {
        proposedDate = this.adjustForHolidays(proposedDate, holidays);
      }

      // Check for conflicts
      const conflicts = await this.checkSchedulingConflicts(
        proposedDate,
        taskData.estimatedDuration || 60,
        taskData.assignedTo,
        bufferMinutes
      );

      // If conflicts exist, find next available slot
      if (conflicts.length > 0) {
        proposedDate = await this.findNextAvailableSlot(
          proposedDate,
          taskData.estimatedDuration || 60,
          taskData.assignedTo,
          options
        );
      }

      // Calculate due date based on priority and type
      const dueDate = this.calculateDueDate(proposedDate, taskData.priority, taskData.type);

      return {
        scheduledDate: proposedDate,
        dueDate,
        conflicts,
      };
    } catch (error) {
      console.error("Error in scheduleMaintenanceTask:", error);
      throw new Error("Failed to schedule maintenance task");
    }
  }

  /**
   * Create a new maintenance task with enhanced validation and scheduling
   */
  async createMaintenanceTask(data: any): Promise<MaintenanceTask> {
    try {
      // Validate input data
      const validatedData = MaintenanceTaskCreateSchema.parse(data);

      // Convert complex objects to JSON strings for database storage
      const dbData: any = {
        ...validatedData,
        checklistItems: validatedData.checklistItems ? JSON.stringify(validatedData.checklistItems) : null,
        partsUsed: validatedData.partsUsed ? JSON.stringify(validatedData.partsUsed) : null,
      };

      return await prisma.maintenanceTask.create({
        data: dbData,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(", ")}`);
      }
      console.error("Error in createMaintenanceTask:", error);
      throw new Error("Failed to create maintenance task");
    }
  }

  /**
   * Update a maintenance task with enhanced validation
   */
  async updateMaintenanceTask(id: string, data: any): Promise<MaintenanceTask> {
    try {
      // Validate input data
      const validatedData = MaintenanceTaskUpdateSchema.parse(data);

      // Convert complex objects to JSON strings for database storage
      const dbData: any = {
        ...validatedData,
        updatedAt: new Date(),
      };

      if (validatedData.checklistItems) {
        dbData.checklistItems = JSON.stringify(validatedData.checklistItems);
      }
      if (validatedData.partsUsed) {
        dbData.partsUsed = JSON.stringify(validatedData.partsUsed);
      }

      // Remove fields that shouldn't be updated directly
      delete dbData.assetId; // Asset ID shouldn't change
      delete dbData.createdAt;

      return await prisma.maintenanceTask.update({
        where: { id },
        data: dbData,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(", ")}`);
      }
      console.error("Error in updateMaintenanceTask:", error);
      throw new Error("Failed to update maintenance task");
    }
  }

  /**
   * Helper methods for advanced scheduling
   */
  private adjustToWorkingHours(date: Date, workingHours: { start: string; end: string }): Date {
    const [startHour, startMinute] = workingHours.start.split(":").map(Number);
    const [endHour, endMinute] = workingHours.end.split(":").map(Number);

    const adjustedDate = new Date(date);
    const currentHour = adjustedDate.getHours();
    const currentMinute = adjustedDate.getMinutes();

    // If before working hours, move to start of working hours
    if (currentHour < startHour || (currentHour === startHour && currentMinute < startMinute)) {
      adjustedDate.setHours(startHour, startMinute, 0, 0);
    }

    // If after working hours, move to next day's start
    if (currentHour > endHour || (currentHour === endHour && currentMinute > endMinute)) {
      adjustedDate.setDate(adjustedDate.getDate() + 1);
      adjustedDate.setHours(startHour, startMinute, 0, 0);
    }

    return adjustedDate;
  }

  private adjustForWeekends(date: Date): Date {
    const adjustedDate = new Date(date);
    const dayOfWeek = adjustedDate.getDay();

    // If Saturday (6), move to Monday
    if (dayOfWeek === 6) {
      adjustedDate.setDate(adjustedDate.getDate() + 2);
    }
    // If Sunday (0), move to Monday
    else if (dayOfWeek === 0) {
      adjustedDate.setDate(adjustedDate.getDate() + 1);
    }

    return adjustedDate;
  }

  private adjustForHolidays(date: Date, holidays: Date[]): Date {
    let adjustedDate = new Date(date);

    while (holidays.some(holiday =>
      holiday.toDateString() === adjustedDate.toDateString()
    )) {
      adjustedDate.setDate(adjustedDate.getDate() + 1);
      // Also check for weekends after moving
      adjustedDate = this.adjustForWeekends(adjustedDate);
    }

    return adjustedDate;
  }

  private async checkSchedulingConflicts(
    proposedDate: Date,
    durationMinutes: number,
    assignedTo?: string,
    bufferMinutes: number = 30
  ): Promise<any[]> {
    const startTime = new Date(proposedDate.getTime() - bufferMinutes * 60 * 1000);
    const endTime = new Date(proposedDate.getTime() + (durationMinutes + bufferMinutes) * 60 * 1000);

    const conflicts = await prisma.maintenanceTask.findMany({
      where: {
        AND: [
          {
            OR: [
              {
                scheduledDate: { gte: startTime, lte: endTime },
              },
              {
                dueDate: { gte: startTime, lte: endTime },
              },
            ],
          },
          {
            status: { in: ["scheduled", "in_progress"] },
          },
          ...(assignedTo ? [{ assignedTo }] : []),
        ],
      },
      select: {
        id: true,
        title: true,
        scheduledDate: true,
        dueDate: true,
        assignedTo: true,
      },
    });

    return conflicts;
  }

  private async findNextAvailableSlot(
    startDate: Date,
    durationMinutes: number,
    assignedTo?: string,
    options: SchedulingOptions = {}
  ): Promise<Date> {
    let proposedDate = new Date(startDate);
    const maxAttempts = 30; // Don't search more than 30 days ahead
    let attempts = 0;

    while (attempts < maxAttempts) {
      const conflicts = await this.checkSchedulingConflicts(
        proposedDate,
        durationMinutes,
        assignedTo,
        options.bufferMinutes
      );

      if (conflicts.length === 0) {
        return proposedDate;
      }

      // Move to next available time slot
      proposedDate.setHours(proposedDate.getHours() + 1);

      // Adjust for working hours and weekends
      if (options.considerWorkingHours) {
        proposedDate = this.adjustToWorkingHours(proposedDate, options.workingHours!);
      }
      if (options.excludeWeekends) {
        proposedDate = this.adjustForWeekends(proposedDate);
      }
      if (options.excludeHolidays && options.holidays) {
        proposedDate = this.adjustForHolidays(proposedDate, options.holidays);
      }

      attempts++;
    }

    // If no slot found, return original date with warning
    console.warn("Could not find available slot within 30 days, using original date");
    return startDate;
  }

  private calculateDueDate(scheduledDate: Date, priority: string, type: string): Date {
    const dueDate = new Date(scheduledDate);

    // Base duration in hours based on priority
    let baseDurationHours = 24; // Default 24 hours

    switch (priority) {
      case "critical":
        baseDurationHours = 4;
        break;
      case "high":
        baseDurationHours = 8;
        break;
      case "medium":
        baseDurationHours = 24;
        break;
      case "low":
        baseDurationHours = 72;
        break;
    }

    // Adjust based on maintenance type
    switch (type) {
      case "emergency":
        baseDurationHours = Math.min(baseDurationHours, 2);
        break;
      case "corrective":
        baseDurationHours = Math.min(baseDurationHours, 8);
        break;
      case "predictive":
        baseDurationHours = Math.max(baseDurationHours, 48);
        break;
    }

    dueDate.setHours(dueDate.getHours() + baseDurationHours);
    return dueDate;
  }

  /**
   * Get a single maintenance task by ID
   */
  async getMaintenanceTaskById(id: string): Promise<MaintenanceTaskWithAsset | null> {
    try {
      return await prisma.maintenanceTask.findUnique({
        where: { id },
        include: {
          asset: {
            select: {
              id: true,
              name: true,
              category: true,
              location: true,
            },
          },
        },
      });
    } catch (error) {
      console.error("Error in getMaintenanceTaskById:", error);
      throw new Error("Failed to fetch maintenance task");
    }
  }

  /**
   * Generate maintenance recommendations using AI/ML algorithms
   */
  async generateMaintenanceRecommendations(assetId: string): Promise<MaintenanceRecommendation[]> {
    try {
      // Get asset with maintenance history
      const asset = await prisma.asset.findUnique({
        where: { id: assetId },
        include: {
          assetType: {
            include: {
              maintenanceSchedules: true,
            },
          },
          maintenanceTasks: {
            where: { status: "completed" },
            orderBy: { completedDate: "desc" },
            take: 10,
          },
        },
      });

      // Get predictions separately due to Prisma limitations
      const predictions = await prisma.maintenancePrediction.findMany({
        where: {
          assetId,
          isActive: true,
        },
        orderBy: { predictionDate: "desc" },
        take: 5,
      });

      if (!asset) {
        throw new Error("Asset not found");
      }

      const recommendations: MaintenanceRecommendation[] = [];

      // Analyze maintenance patterns
      const maintenanceHistory = asset.maintenanceTasks;
      const now = new Date();

      // Check for overdue preventive maintenance
      for (const schedule of asset.assetType?.maintenanceSchedules || []) {
        const lastMaintenance = maintenanceHistory.find(task =>
          task.scheduleId === schedule.id
        );

        if (lastMaintenance) {
          const daysSinceLastMaintenance = Math.floor(
            (now.getTime() - lastMaintenance.completedDate!.getTime()) / (1000 * 60 * 60 * 24)
          );

          // Parse frequency from JSON string
          let frequency: MaintenanceFrequency;
          try {
            frequency = JSON.parse(schedule.frequency);
          } catch {
            continue;
          }

          const intervalDays = this.calculateIntervalDays(frequency);

          if (daysSinceLastMaintenance >= intervalDays * 0.8) { // 80% of interval
            const priority = daysSinceLastMaintenance >= intervalDays ? "high" : "medium";

            recommendations.push({
              assetId,
              recommendationType: "schedule_maintenance",
              priority: priority as "low" | "medium" | "high" | "critical",
              reasoning: `${schedule.name} is due based on ${frequency.type} schedule`,
              suggestedDate: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
              estimatedCost: schedule.estimatedCost,
              confidence: daysSinceLastMaintenance >= intervalDays ? 0.9 : 0.7,
              supportingData: {
                scheduleId: schedule.id,
                scheduleName: schedule.name,
                daysSinceLastMaintenance,
                intervalDays,
              },
            });
          }
        }
      }

      // Analyze failure patterns
      const failurePattern = this.analyzeFailurePatterns(maintenanceHistory);
      if (failurePattern.riskScore > 0.7) {
        recommendations.push({
          assetId,
          recommendationType: "inspect_asset",
          priority: "high",
          reasoning: `High failure risk detected based on maintenance history pattern`,
          suggestedDate: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
          confidence: failurePattern.riskScore,
          supportingData: failurePattern,
        });
      }

      // Use predictive maintenance predictions
      for (const prediction of predictions) {
        if (prediction.predictionType === "failure_probability" && prediction.predictedValue > 0.6) {
          recommendations.push({
            assetId,
            recommendationType: "schedule_maintenance",
            priority: prediction.predictedValue > 0.8 ? "critical" : "high",
            reasoning: `Predictive model indicates high failure probability (${Math.round(prediction.predictedValue * 100)}%)`,
            suggestedDate: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
            confidence: prediction.confidence,
            supportingData: {
              modelId: prediction.modelId,
              predictionValue: prediction.predictedValue,
              features: JSON.parse(prediction.features),
            },
          });
        }
      }

      // Sort by priority and confidence
      return recommendations.sort((a, b) => {
        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return b.confidence - a.confidence;
      });

    } catch (error) {
      console.error("Error generating maintenance recommendations:", error);
      throw new Error("Failed to generate maintenance recommendations");
    }
  }

  /**
   * Create predictive maintenance prediction
   */
  async createPredictiveMaintenance(input: PredictiveMaintenanceInput): Promise<any> {
    try {
      // Get or create default model if none specified
      let modelId = input.modelId;
      if (!modelId) {
        const defaultModel = await prisma.predictiveMaintenanceModel.findFirst({
          where: {
            isActive: true,
            modelType: "time_series", // Default to time series
          },
        });

        if (!defaultModel) {
          throw new Error("No predictive maintenance model available");
        }

        modelId = defaultModel.id;
      }

      // Calculate prediction based on features
      const prediction = await this.calculatePrediction(input.features, modelId);

      // Store prediction
      const predictionData: MaintenancePredictionCreate = {
        assetId: input.assetId,
        modelId,
        predictionType: "failure_probability",
        predictedValue: prediction.value,
        confidence: prediction.confidence,
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Valid for 30 days
        features: input.features,
        metadata: prediction.metadata,
      };

      return await prisma.maintenancePrediction.create({
        data: predictionData,
      });

    } catch (error) {
      console.error("Error creating predictive maintenance:", error);
      throw new Error("Failed to create predictive maintenance");
    }
  }

  /**
   * Helper methods for analysis
   */
  private calculateIntervalDays(frequency: MaintenanceFrequency): number {
    switch (frequency.type) {
      case "daily":
        return frequency.interval;
      case "weekly":
        return frequency.interval * 7;
      case "monthly":
        return frequency.interval * 30;
      case "yearly":
        return frequency.interval * 365;
      case "custom":
        return frequency.interval; // Assume days for custom
      default:
        return 30; // Default to 30 days
    }
  }

  private analyzeFailurePatterns(maintenanceHistory: any[]): { riskScore: number; patterns: any[] } {
    if (maintenanceHistory.length < 3) {
      return { riskScore: 0, patterns: [] };
    }

    const patterns = [];
    let riskScore = 0;

    // Check for increasing frequency of corrective maintenance
    const correctiveTasks = maintenanceHistory.filter(task => task.type === "corrective");
    if (correctiveTasks.length > maintenanceHistory.length * 0.5) {
      riskScore += 0.3;
      patterns.push("High corrective maintenance frequency");
    }

    // Check for increasing costs
    const recentTasks = maintenanceHistory.slice(0, 3);
    const olderTasks = maintenanceHistory.slice(3, 6);

    if (recentTasks.length > 0 && olderTasks.length > 0) {
      const recentAvgCost = recentTasks.reduce((sum, task) => sum + (task.actualCost || 0), 0) / recentTasks.length;
      const olderAvgCost = olderTasks.reduce((sum, task) => sum + (task.actualCost || 0), 0) / olderTasks.length;

      if (recentAvgCost > olderAvgCost * 1.5) {
        riskScore += 0.2;
        patterns.push("Increasing maintenance costs");
      }
    }

    // Check for decreasing intervals between maintenance
    const intervals = [];
    for (let i = 1; i < maintenanceHistory.length; i++) {
      const interval = maintenanceHistory[i-1].completedDate!.getTime() - maintenanceHistory[i].completedDate!.getTime();
      intervals.push(interval / (1000 * 60 * 60 * 24)); // Convert to days
    }

    if (intervals.length >= 2) {
      const recentInterval = intervals[0];
      const olderInterval = intervals[intervals.length - 1];

      if (recentInterval < olderInterval * 0.7) {
        riskScore += 0.3;
        patterns.push("Decreasing intervals between maintenance");
      }
    }

    return { riskScore: Math.min(riskScore, 1), patterns };
  }

  private async calculatePrediction(features: Record<string, number>, modelId: string): Promise<{
    value: number;
    confidence: number;
    metadata: any;
  }> {
    // This is a simplified prediction algorithm
    // In a real implementation, this would use actual ML models

    const model = await prisma.predictiveMaintenanceModel.findUnique({
      where: { id: modelId },
    });

    if (!model) {
      throw new Error("Model not found");
    }

    // Simple weighted scoring based on common maintenance indicators
    let score = 0;
    let confidence = 0.5;

    // Age factor
    if (features.age_months) {
      score += Math.min(features.age_months / 120, 1) * 0.3; // Max contribution at 10 years
    }

    // Usage factor
    if (features.usage_hours) {
      score += Math.min(features.usage_hours / 8760, 1) * 0.2; // Max at 1 year of continuous use
    }

    // Maintenance frequency factor
    if (features.maintenance_count) {
      score += Math.min(features.maintenance_count / 12, 1) * 0.2; // Max at 12 maintenances
    }

    // Failure history factor
    if (features.failure_count) {
      score += Math.min(features.failure_count / 5, 1) * 0.3; // Max at 5 failures
    }

    // Adjust confidence based on data quality
    const featureCount = Object.keys(features).length;
    confidence = Math.min(0.5 + (featureCount / 10) * 0.4, 0.9);

    return {
      value: Math.min(score, 1),
      confidence,
      metadata: {
        algorithm: model.algorithm,
        features_used: Object.keys(features),
        model_version: model.version,
      },
    };
  }

  /**
   * Delete a maintenance task
   */
  async deleteMaintenanceTask(id: string): Promise<MaintenanceTask> {
    try {
      // Check if task exists
      const existingTask = await this.getMaintenanceTaskById(id);
      if (!existingTask) {
        throw new Error("Maintenance task not found");
      }

      return await prisma.maintenanceTask.delete({
        where: { id },
      });
    } catch (error) {
      console.error("Error in deleteMaintenanceTask:", error);
      throw new Error("Failed to delete maintenance task");
    }
  }

  /**
   * Complete a maintenance task
   */
  async completeMaintenanceTask(id: string, completionData: {
    completionNotes?: string;
    actualDuration?: number;
    actualCost?: number;
  }): Promise<MaintenanceTask> {
    try {
      return await prisma.maintenanceTask.update({
        where: { id },
        data: {
          status: "completed",
          completedDate: new Date(),
          completionNotes: completionData.completionNotes,
          actualDuration: completionData.actualDuration,
          actualCost: completionData.actualCost,
        },
      });
    } catch (error) {
      console.error("Error in completeMaintenanceTask:", error);
      throw new Error("Failed to complete maintenance task");
    }
  }

  /**
   * Get maintenance statistics
   */
  async getMaintenanceStatistics(): Promise<MaintenanceStatistics> {
    try {
      const [
        totalTasks,
        scheduledTasks,
        inProgressTasks,
        completedTasks,
        overdueTasks,
      ] = await Promise.all([
        prisma.maintenanceTask.count(),
        prisma.maintenanceTask.count({ where: { status: "scheduled" } }),
        prisma.maintenanceTask.count({ where: { status: "in_progress" } }),
        prisma.maintenanceTask.count({ where: { status: "completed" } }),
        prisma.maintenanceTask.count({
          where: {
            dueDate: { lt: new Date() },
            status: { not: "completed" },
          },
        }),
      ]);

      // Get upcoming tasks (next 30 days)
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);
      
      const upcomingTasks = await prisma.maintenanceTask.count({
        where: {
          scheduledDate: {
            gte: new Date(),
            lte: futureDate,
          },
          status: { in: ["scheduled", "in_progress"] },
        },
      });

      // Calculate average completion time
      const completedTasksWithDuration = await prisma.maintenanceTask.findMany({
        where: {
          status: "completed",
          actualDuration: { not: null },
        },
        select: {
          actualDuration: true,
        },
      });

      const averageCompletionTime = completedTasksWithDuration.length > 0
        ? completedTasksWithDuration.reduce((sum, task) => sum + (task.actualDuration || 0), 0) / completedTasksWithDuration.length
        : 0;

      // Calculate total and average cost
      const completedTasksWithCost = await prisma.maintenanceTask.findMany({
        where: {
          status: "completed",
          actualCost: { not: null },
        },
        select: {
          actualCost: true,
        },
      });

      const totalCost = completedTasksWithCost.reduce((sum, task) => sum + (task.actualCost || 0), 0);
      const averageCost = completedTasksWithCost.length > 0 ? totalCost / completedTasksWithCost.length : 0;

      // Get tasks by priority
      const tasksByPriority = await prisma.maintenanceTask.groupBy({
        by: ['priority'],
        _count: {
          priority: true,
        },
      });

      // Get tasks by type
      const tasksByType = await prisma.maintenanceTask.groupBy({
        by: ['type'],
        _count: {
          type: true,
        },
      });

      return {
        totalTasks,
        scheduledTasks,
        inProgressTasks,
        completedTasks,
        overdueTasks,
        upcomingTasks,
        averageCompletionTime,
        totalCost,
        averageCost,
        tasksByPriority: tasksByPriority.map(item => ({
          priority: item.priority,
          count: item._count.priority,
        })),
        tasksByType: tasksByType.map(item => ({
          type: item.type,
          count: item._count.type,
        })),
      };
    } catch (error) {
      console.error("Error in getMaintenanceStatistics:", error);
      throw new Error("Failed to fetch maintenance statistics");
    }
  }

  /**
   * Get overdue maintenance tasks
   */
  async getOverdueTasks(): Promise<MaintenanceTaskWithAsset[]> {
    try {
      const now = new Date();
      
      return await prisma.maintenanceTask.findMany({
        where: {
          dueDate: { lt: now },
          status: { not: "completed" },
        },
        include: {
          asset: {
            select: {
              id: true,
              name: true,
              category: true,
              location: true,
            },
          },
        },
        orderBy: {
          dueDate: "asc",
        },
      });
    } catch (error) {
      console.error("Error in getOverdueTasks:", error);
      throw new Error("Failed to fetch overdue tasks");
    }
  }

  /**
   * Get upcoming maintenance tasks
   */
  async getUpcomingTasks(days: number = 30): Promise<MaintenanceTaskWithAsset[]> {
    try {
      const now = new Date();
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + days);
      
      return await prisma.maintenanceTask.findMany({
        where: {
          scheduledDate: {
            gte: now,
            lte: futureDate,
          },
          status: { in: ["scheduled", "in_progress"] },
        },
        include: {
          asset: {
            select: {
              id: true,
              name: true,
              category: true,
              location: true,
            },
          },
        },
        orderBy: {
          scheduledDate: "asc",
        },
      });
    } catch (error) {
      console.error("Error in getUpcomingTasks:", error);
      throw new Error("Failed to fetch upcoming tasks");
    }
  }
}

// Export a singleton instance
export const maintenanceService = new MaintenanceService();
