import { z } from "zod";

// Base enums for maintenance system
export const MaintenanceTypeEnum = z.enum([
  "preventive",
  "predictive", 
  "corrective",
  "emergency",
  "condition_based"
]);

export const MaintenancePriorityEnum = z.enum([
  "low",
  "medium", 
  "high",
  "critical"
]);

export const MaintenanceStatusEnum = z.enum([
  "scheduled",
  "in_progress",
  "completed", 
  "cancelled",
  "overdue",
  "on_hold"
]);

export const NotificationTypeEnum = z.enum([
  "due_soon",
  "overdue",
  "assigned",
  "completed",
  "cancelled",
  "status_changed"
]);

export const NotificationStatusEnum = z.enum([
  "pending",
  "sent",
  "read",
  "dismissed"
]);

export const NotificationChannelEnum = z.enum([
  "email",
  "sms", 
  "push",
  "in_app"
]);

// Maintenance Schedule Schemas
export const MaintenanceFrequencySchema = z.object({
  type: z.enum(["daily", "weekly", "monthly", "yearly", "custom", "usage_based"]),
  interval: z.number().int().positive(),
  unit: z.enum(["days", "weeks", "months", "years", "hours", "cycles"]).optional(),
  daysOfWeek: z.array(z.number().int().min(0).max(6)).optional(), // 0 = Sunday
  dayOfMonth: z.number().int().min(1).max(31).optional(),
  monthOfYear: z.number().int().min(1).max(12).optional(),
  customRule: z.string().optional(), // RRULE format
});

export const ChecklistItemSchema = z.object({
  id: z.string(),
  title: z.string().min(1, "Checklist item title is required"),
  description: z.string().optional(),
  required: z.boolean().default(false),
  type: z.enum(["checkbox", "text", "number", "select", "file"]).default("checkbox"),
  options: z.array(z.string()).optional(), // For select type
  validation: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
    pattern: z.string().optional(),
  }).optional(),
});

export const RequiredPartSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Part name is required"),
  partNumber: z.string().optional(),
  quantity: z.number().positive(),
  unit: z.string().default("each"),
  estimatedCost: z.number().positive().optional(),
  supplier: z.string().optional(),
  notes: z.string().optional(),
});

export const MaintenanceScheduleSchema = z.object({
  id: z.string().cuid(),
  assetTypeId: z.string().cuid("Invalid asset type ID"),
  name: z.string().min(1, "Schedule name is required").max(200, "Name too long"),
  description: z.string().min(1, "Description is required"),
  type: MaintenanceTypeEnum,
  frequency: MaintenanceFrequencySchema,
  priority: MaintenancePriorityEnum,
  estimatedDuration: z.number().int().positive(), // in minutes
  estimatedCost: z.number().positive(),
  requiredSkills: z.array(z.string()).default([]),
  requiredParts: z.array(RequiredPartSchema).optional(),
  instructions: z.string().min(1, "Instructions are required"),
  checklistItems: z.array(ChecklistItemSchema).optional(),
  triggers: z.array(z.object({
    type: z.enum(["time", "usage", "condition", "event"]),
    condition: z.string(),
    threshold: z.number().optional(),
  })).optional(),
  isActive: z.boolean().default(true),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const MaintenanceScheduleCreateSchema = MaintenanceScheduleSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const MaintenanceScheduleUpdateSchema = MaintenanceScheduleCreateSchema.partial();

// Enhanced Maintenance Task Schemas
export const MaintenanceTaskSchema = z.object({
  id: z.string().cuid(),
  assetId: z.string().cuid("Invalid asset ID"),
  scheduleId: z.string().cuid().optional(),
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  description: z.string().optional(),
  type: MaintenanceTypeEnum,
  priority: MaintenancePriorityEnum,
  status: MaintenanceStatusEnum.default("scheduled"),
  scheduledDate: z.date(),
  dueDate: z.date(),
  completedDate: z.date().optional(),
  assignedTo: z.string().optional(),
  assignedTeam: z.string().optional(),
  estimatedDuration: z.number().int().positive().optional(),
  actualDuration: z.number().int().positive().optional(),
  estimatedCost: z.number().positive().optional(),
  actualCost: z.number().positive().optional(),
  instructions: z.string().optional(),
  checklistItems: z.array(ChecklistItemSchema).optional(),
  completionNotes: z.string().optional(),
  failureReason: z.string().optional(),
  partsUsed: z.array(RequiredPartSchema).optional(),
  skillsRequired: z.array(z.string()).default([]),
  safetyNotes: z.string().optional(),
  workOrderNumber: z.string().optional(),
  recurringTaskId: z.string().cuid().optional(),
  parentTaskId: z.string().cuid().optional(),
  isRecurring: z.boolean().default(false),
  nextScheduledDate: z.date().optional(),
  completedBy: z.string().optional(),
  verifiedBy: z.string().optional(),
  verificationNotes: z.string().optional(),
  attachments: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const MaintenanceTaskCreateSchema = MaintenanceTaskSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const MaintenanceTaskUpdateSchema = MaintenanceTaskCreateSchema.partial();

// Maintenance Work Log Schema
export const MaintenanceWorkLogSchema = z.object({
  id: z.string().cuid(),
  taskId: z.string().cuid("Invalid task ID"),
  userId: z.string().cuid("Invalid user ID"),
  userName: z.string().min(1, "User name is required"),
  action: z.enum(["started", "paused", "resumed", "completed", "updated", "commented"]),
  description: z.string().optional(),
  timeSpent: z.number().int().positive().optional(), // in minutes
  timestamp: z.date().default(() => new Date()),
  metadata: z.record(z.any()).optional(),
});

export const MaintenanceWorkLogCreateSchema = MaintenanceWorkLogSchema.omit({
  id: true,
  timestamp: true,
});

// Maintenance Notification Schema
export const MaintenanceNotificationSchema = z.object({
  id: z.string().cuid(),
  taskId: z.string().cuid("Invalid task ID"),
  type: NotificationTypeEnum,
  title: z.string().min(1, "Title is required"),
  message: z.string().min(1, "Message is required"),
  recipientId: z.string().min(1, "Recipient ID is required"),
  recipientType: z.enum(["user", "team", "role"]),
  status: NotificationStatusEnum.default("pending"),
  scheduledFor: z.date(),
  sentAt: z.date().optional(),
  readAt: z.date().optional(),
  dismissedAt: z.date().optional(),
  channels: z.array(NotificationChannelEnum).default([]),
  metadata: z.record(z.any()).optional(),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const MaintenanceNotificationCreateSchema = MaintenanceNotificationSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const MaintenanceNotificationUpdateSchema = MaintenanceNotificationCreateSchema.partial();

// Maintenance Calendar Event Schema
export const MaintenanceCalendarEventSchema = z.object({
  id: z.string().cuid(),
  taskId: z.string().cuid().optional(),
  assetId: z.string().cuid().optional(),
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  startDate: z.date(),
  endDate: z.date(),
  allDay: z.boolean().default(false),
  recurrenceRule: z.string().optional(), // RRULE format
  color: z.string().optional(),
  category: z.string().optional(),
  location: z.string().optional(),
  assignedTo: z.string().optional(),
  status: MaintenanceStatusEnum.default("scheduled"),
  isBlocked: z.boolean().default(false),
  priority: MaintenancePriorityEnum.default("medium"),
  tags: z.array(z.string()).default([]),
  createdBy: z.string().min(1, "Creator is required"),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const MaintenanceCalendarEventCreateSchema = MaintenanceCalendarEventSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const MaintenanceCalendarEventUpdateSchema = MaintenanceCalendarEventCreateSchema.partial();

// Export TypeScript types
export type MaintenanceSchedule = z.infer<typeof MaintenanceScheduleSchema>;
export type MaintenanceScheduleCreate = z.infer<typeof MaintenanceScheduleCreateSchema>;
export type MaintenanceScheduleUpdate = z.infer<typeof MaintenanceScheduleUpdateSchema>;

export type MaintenanceTask = z.infer<typeof MaintenanceTaskSchema>;
export type MaintenanceTaskCreate = z.infer<typeof MaintenanceTaskCreateSchema>;
export type MaintenanceTaskUpdate = z.infer<typeof MaintenanceTaskUpdateSchema>;

export type MaintenanceWorkLog = z.infer<typeof MaintenanceWorkLogSchema>;
export type MaintenanceWorkLogCreate = z.infer<typeof MaintenanceWorkLogCreateSchema>;

export type MaintenanceNotification = z.infer<typeof MaintenanceNotificationSchema>;
export type MaintenanceNotificationCreate = z.infer<typeof MaintenanceNotificationCreateSchema>;
export type MaintenanceNotificationUpdate = z.infer<typeof MaintenanceNotificationUpdateSchema>;

export type MaintenanceCalendarEvent = z.infer<typeof MaintenanceCalendarEventSchema>;
export type MaintenanceCalendarEventCreate = z.infer<typeof MaintenanceCalendarEventCreateSchema>;
export type MaintenanceCalendarEventUpdate = z.infer<typeof MaintenanceCalendarEventUpdateSchema>;

export type ChecklistItem = z.infer<typeof ChecklistItemSchema>;
export type RequiredPart = z.infer<typeof RequiredPartSchema>;
export type MaintenanceFrequency = z.infer<typeof MaintenanceFrequencySchema>;

// Maintenance Alert Rule Schema
export const MaintenanceAlertRuleSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1, "Alert rule name is required"),
  description: z.string().optional(),
  assetTypeId: z.string().cuid().optional(),
  assetId: z.string().cuid().optional(),
  triggerType: z.enum(["time_based", "condition_based", "usage_based"]),
  triggerConfig: z.record(z.any()), // JSON configuration
  alertType: z.enum(["due_soon", "overdue", "failure_prediction", "condition_alert"]),
  severity: z.enum(["info", "warning", "critical"]),
  channels: z.array(NotificationChannelEnum).default([]),
  recipients: z.array(z.string()).default([]),
  isActive: z.boolean().default(true),
  conditions: z.record(z.any()).optional(),
  throttleMinutes: z.number().int().positive().optional(),
  lastTriggered: z.date().optional(),
  createdBy: z.string().min(1, "Creator is required"),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const MaintenanceAlertRuleCreateSchema = MaintenanceAlertRuleSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const MaintenanceAlertRuleUpdateSchema = MaintenanceAlertRuleCreateSchema.partial();

// Predictive Maintenance Model Schema
export const PredictiveMaintenanceModelSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1, "Model name is required"),
  description: z.string().optional(),
  assetTypeId: z.string().cuid("Invalid asset type ID"),
  modelType: z.enum(["time_series", "regression", "classification", "anomaly_detection"]),
  algorithm: z.enum([
    "linear_regression",
    "random_forest",
    "lstm",
    "isolation_forest",
    "svm",
    "neural_network"
  ]),
  features: z.array(z.string()).default([]),
  targetVariable: z.string().min(1, "Target variable is required"),
  accuracy: z.number().min(0).max(1).optional(),
  lastTrained: z.date().optional(),
  trainingData: z.record(z.any()).optional(),
  modelConfig: z.record(z.any()),
  isActive: z.boolean().default(true),
  version: z.number().int().positive().default(1),
  createdBy: z.string().min(1, "Creator is required"),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
});

export const PredictiveMaintenanceModelCreateSchema = PredictiveMaintenanceModelSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const PredictiveMaintenanceModelUpdateSchema = PredictiveMaintenanceModelCreateSchema.partial();

// Maintenance Prediction Schema
export const MaintenancePredictionSchema = z.object({
  id: z.string().cuid(),
  assetId: z.string().cuid("Invalid asset ID"),
  modelId: z.string().cuid("Invalid model ID"),
  predictionType: z.enum([
    "failure_probability",
    "remaining_useful_life",
    "next_maintenance_date",
    "condition_score",
    "anomaly_score"
  ]),
  predictedValue: z.number(),
  confidence: z.number().min(0).max(1),
  predictionDate: z.date().default(() => new Date()),
  validUntil: z.date(),
  actualOutcome: z.string().optional(),
  features: z.record(z.any()),
  metadata: z.record(z.any()).optional(),
  isActive: z.boolean().default(true),
});

export const MaintenancePredictionCreateSchema = MaintenancePredictionSchema.omit({
  id: true,
  predictionDate: true,
});

export const MaintenancePredictionUpdateSchema = MaintenancePredictionCreateSchema.partial();

// Bulk Operations Schemas
export const BulkMaintenanceTaskUpdateSchema = z.object({
  taskIds: z.array(z.string().cuid()).min(1, "At least one task ID is required"),
  updates: z.object({
    status: MaintenanceStatusEnum.optional(),
    assignedTo: z.string().optional(),
    assignedTeam: z.string().optional(),
    priority: MaintenancePriorityEnum.optional(),
    scheduledDate: z.date().optional(),
    dueDate: z.date().optional(),
    tags: z.array(z.string()).optional(),
  }).refine(data => Object.keys(data).length > 0, "At least one update field is required"),
});

export const MaintenanceTaskFilterSchema = z.object({
  status: z.array(MaintenanceStatusEnum).optional(),
  priority: z.array(MaintenancePriorityEnum).optional(),
  type: z.array(MaintenanceTypeEnum).optional(),
  assetId: z.string().cuid().optional(),
  assetTypeId: z.string().cuid().optional(),
  assignedTo: z.string().optional(),
  assignedTeam: z.string().optional(),
  scheduledDateFrom: z.date().optional(),
  scheduledDateTo: z.date().optional(),
  dueDateFrom: z.date().optional(),
  dueDateTo: z.date().optional(),
  tags: z.array(z.string()).optional(),
  search: z.string().optional(),
});

export const MaintenanceStatisticsSchema = z.object({
  totalTasks: z.number().int().nonnegative(),
  scheduledTasks: z.number().int().nonnegative(),
  inProgressTasks: z.number().int().nonnegative(),
  completedTasks: z.number().int().nonnegative(),
  overdueTasks: z.number().int().nonnegative(),
  upcomingTasks: z.number().int().nonnegative(),
  averageCompletionTime: z.number().nonnegative(),
  totalCost: z.number().nonnegative(),
  averageCost: z.number().nonnegative(),
  tasksByPriority: z.array(z.object({
    priority: z.string(),
    count: z.number().int().nonnegative(),
  })),
  tasksByType: z.array(z.object({
    type: z.string(),
    count: z.number().int().nonnegative(),
  })),
  tasksByStatus: z.array(z.object({
    status: z.string(),
    count: z.number().int().nonnegative(),
  })),
  completionRate: z.number().min(0).max(100),
  averageResponseTime: z.number().nonnegative(),
  costTrends: z.array(z.object({
    period: z.string(),
    cost: z.number().nonnegative(),
  })).optional(),
});

// Export additional types
export type MaintenanceAlertRule = z.infer<typeof MaintenanceAlertRuleSchema>;
export type MaintenanceAlertRuleCreate = z.infer<typeof MaintenanceAlertRuleCreateSchema>;
export type MaintenanceAlertRuleUpdate = z.infer<typeof MaintenanceAlertRuleUpdateSchema>;

export type PredictiveMaintenanceModel = z.infer<typeof PredictiveMaintenanceModelSchema>;
export type PredictiveMaintenanceModelCreate = z.infer<typeof PredictiveMaintenanceModelCreateSchema>;
export type PredictiveMaintenanceModelUpdate = z.infer<typeof PredictiveMaintenanceModelUpdateSchema>;

export type MaintenancePrediction = z.infer<typeof MaintenancePredictionSchema>;
export type MaintenancePredictionCreate = z.infer<typeof MaintenancePredictionCreateSchema>;
export type MaintenancePredictionUpdate = z.infer<typeof MaintenancePredictionUpdateSchema>;

export type BulkMaintenanceTaskUpdate = z.infer<typeof BulkMaintenanceTaskUpdateSchema>;
export type MaintenanceTaskFilter = z.infer<typeof MaintenanceTaskFilterSchema>;
export type MaintenanceStatistics = z.infer<typeof MaintenanceStatisticsSchema>;
