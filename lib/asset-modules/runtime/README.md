# Asset Module Runtime System

A comprehensive, production-ready runtime system for executing dynamic asset modules with enterprise-grade security, performance, and monitoring capabilities.

## 🚀 Features

### Core Runtime Engine
- **Dynamic Module Loading** - Hot-load modules without system restart
- **Field Rendering Engine** - Render complex form fields with validation
- **Logic Execution Engine** - Execute visual logic flows with node-based processing
- **Validation Engine** - Comprehensive data validation with custom rules
- **Data Transformation Pipeline** - Transform and process data through configurable pipelines

### Security & Sandboxing
- **Isolated Execution** - Secure sandboxed environment for module execution
- **Permission System** - Fine-grained access control and permissions
- **Audit Logging** - Complete audit trail of all operations
- **Rate Limiting** - Prevent abuse with configurable rate limits
- **Input Sanitization** - Automatic sanitization of user inputs

### Performance & Optimization
- **Intelligent Caching** - Multi-level caching for optimal performance
- **Lazy Loading** - Load modules and resources on demand
- **Bundle Optimization** - Automatic CSS/JS bundling and minification
- **Performance Monitoring** - Real-time performance metrics and analysis
- **Memory Management** - Efficient memory usage with automatic cleanup

### Integration & Deployment
- **Editor Integration** - Seamless integration with Asset Module Editor
- **Preview System** - Safe testing environment for module development
- **Hot Deployment** - Deploy modules without downtime
- **API Integration** - RESTful APIs for all runtime operations
- **Event System** - Comprehensive event handling and notifications

## 📁 Architecture

```
lib/asset-modules/runtime/
├── runtime-system.ts           # Main orchestrator
├── module-registry.ts          # Module registration and management
├── module-loader.ts            # Dynamic module loading
├── dependency-resolver.ts      # Dependency resolution
├── field-rendering-engine.ts   # Field rendering system
├── logic-execution-engine.ts   # Logic flow execution
├── validation-engine.ts        # Data validation system
├── data-transformation-pipeline.ts # Data processing pipeline
├── integration-manager.ts      # System integrations
├── security-sandbox.ts         # Security and sandboxing
├── performance-optimizer.ts    # Performance optimization
└── editor-integration.ts       # Editor bridge
```

## 🛠 Quick Start

### 1. Basic Module Execution

```typescript
import { runtimeSystem } from '@/lib/asset-modules/runtime/runtime-system';

// Execute a module
const result = await runtimeSystem.executeModule(
  'module-id',
  'asset-id', 
  'asset-type-id',
  { field1: 'value1', field2: 'value2' },
  {
    userId: 'user-id',
    userRole: 'user',
    permissions: ['module.execute'],
    environment: 'production'
  }
);

console.log('Execution result:', result.outputs);
```

### 2. Field Validation

```typescript
// Validate data against module rules
const validation = await runtimeSystem.validateModuleData(
  'module-id',
  { field1: 'value1', field2: 'value2' },
  context
);

if (!validation.valid) {
  console.log('Validation errors:', validation.errors);
}
```

### 3. Field Rendering

```typescript
// Render module fields for display
const rendering = await runtimeSystem.renderModuleFields(
  'module-id',
  { field1: 'value1', field2: 'value2' },
  context
);

console.log('Rendered HTML:', rendering.layout);
```

## 🔧 Configuration

### Environment Variables

```bash
# Runtime Configuration
ASSET_MODULE_CACHE_TTL=300000          # Cache TTL in milliseconds
ASSET_MODULE_MAX_EXECUTION_TIME=30000  # Max execution time in ms
ASSET_MODULE_MAX_MEMORY_USAGE=50MB     # Memory limit per module
ASSET_MODULE_DEBUG=false               # Enable debug logging

# Security Configuration
ASSET_MODULE_SANDBOX_ENABLED=true      # Enable sandboxing
ASSET_MODULE_RATE_LIMIT=100            # Requests per minute
ASSET_MODULE_AUDIT_ENABLED=true        # Enable audit logging

# Performance Configuration
ASSET_MODULE_CACHE_ENABLED=true        # Enable caching
ASSET_MODULE_LAZY_LOADING=true         # Enable lazy loading
ASSET_MODULE_BUNDLE_OPTIMIZATION=true  # Enable bundling
```

### Module Configuration

```typescript
// Module configuration example
const moduleConfig = {
  id: 'my-module',
  name: 'My Custom Module',
  version: '1.0.0',
  fields: [
    {
      id: 'field-1',
      name: 'userName',
      type: 'text',
      label: 'User Name',
      isRequired: true,
      validation: {
        minLength: 2,
        maxLength: 50,
        pattern: '^[a-zA-Z\\s]+$'
      }
    }
  ],
  logic: {
    nodes: [
      {
        id: 'input-1',
        type: 'input',
        data: { fieldName: 'userName' }
      },
      {
        id: 'transform-1',
        type: 'transform',
        data: { 
          operation: 'uppercase',
          inputField: 'userName',
          outputField: 'displayName'
        }
      }
    ],
    edges: [
      {
        source: 'input-1',
        target: 'transform-1'
      }
    ]
  }
};
```

## 🔒 Security

### Permission System

```typescript
// Define module permissions
const permissions = {
  'module.execute': 'Execute modules',
  'module.validate': 'Validate data',
  'module.render': 'Render fields',
  'module.deploy': 'Deploy modules',
  'module.admin': 'Administrative access'
};

// Check permissions
const hasPermission = await securityManager.checkPermission(
  userId,
  'module.execute',
  moduleId
);
```

### Sandboxing

```typescript
// Execute in secure sandbox
const result = await sandbox.executeInSandbox(
  moduleId,
  code,
  inputs,
  {
    memoryLimit: 50 * 1024 * 1024, // 50MB
    executionTimeout: 30000,        // 30 seconds
    networkAccess: false,
    fileSystemAccess: false
  }
);
```

## 📊 Monitoring

### System Metrics

```typescript
// Get system metrics
const metrics = runtimeSystem.getSystemMetrics();
console.log({
  activeModules: metrics.activeModules,
  totalExecutions: metrics.totalExecutions,
  successRate: metrics.successfulExecutions / metrics.totalExecutions,
  averageExecutionTime: metrics.averageExecutionTime,
  memoryUsage: metrics.memoryUsage,
  cacheHitRate: metrics.cacheHitRate
});
```

### Performance Analysis

```typescript
// Analyze module performance
const optimizer = ModulePerformanceOptimizer.getInstance();
const analysis = await optimizer.analyzePerformance('module-id');

console.log('Bottlenecks:', analysis.bottlenecks);
console.log('Recommendations:', analysis.recommendations);
```

### Security Auditing

```typescript
// Get security audit logs
const securitySandbox = ModuleSecuritySandbox.getInstance();
const auditLogs = securitySandbox.getAuditLogs('module-id');

auditLogs.forEach(log => {
  console.log(`${log.timestamp}: ${log.eventType} - ${log.description}`);
});
```

## 🧪 Testing

### Running Tests

```bash
# Run all runtime system tests
npm test __tests__/asset-modules/

# Run specific test suites
npm test __tests__/asset-modules/runtime-system.test.ts
npm test __tests__/asset-modules/field-rendering-engine.test.ts
npm test __tests__/asset-modules/validation-engine.test.ts

# Run with coverage
npm test -- --coverage
```

### Test Structure

```typescript
// Example test
describe('Runtime System', () => {
  it('should execute module successfully', async () => {
    const result = await runtimeSystem.executeModule(
      'test-module',
      'test-asset',
      'test-type',
      { field: 'value' },
      testContext
    );
    
    expect(result.success).toBe(true);
    expect(result.outputs).toBeDefined();
  });
});
```

## 🚀 Deployment

### Production Deployment

```typescript
// Initialize runtime system
import { runtimeSystem } from '@/lib/asset-modules/runtime/runtime-system';

// Start the system
await runtimeSystem.initialize();

// Deploy a module
const deployment = await editorIntegration.deployModule(
  moduleData,
  userId,
  { autoActivate: true, environment: 'production' }
);

console.log('Deployment result:', deployment);
```

### Health Checks

```typescript
// System health check
const health = await runtimeSystem.performHealthCheck();
console.log('System health:', health.status);
console.log('Active modules:', health.activeModules);
console.log('Performance score:', health.performanceScore);
```

## 📚 API Reference

### REST Endpoints

- `POST /api/asset-modules/runtime/execute` - Execute module
- `POST /api/asset-modules/runtime/validate` - Validate data
- `POST /api/asset-modules/runtime/render` - Render fields
- `GET /api/asset-modules/runtime/status` - System status
- `POST /api/asset-modules/runtime/deploy` - Deploy module
- `POST /api/asset-modules/runtime/preview` - Create preview session

### TypeScript Interfaces

```typescript
interface ModuleExecutionResult {
  success: boolean;
  executionId: string;
  moduleId: string;
  executionTime: number;
  outputs: Record<string, any>;
  errors: ExecutionError[];
  warnings: ExecutionWarning[];
}

interface ModuleValidationResult {
  valid: boolean;
  moduleId: string;
  fieldResults: Record<string, FieldValidationResult>;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}
```

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/new-feature`
3. **Write tests** for your changes
4. **Ensure all tests pass**: `npm test`
5. **Submit a pull request**

### Development Guidelines

- Follow TypeScript best practices
- Write comprehensive tests
- Document new features
- Follow existing code patterns
- Ensure security considerations

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: [Runtime System Docs](./docs/runtime-system.md)
- **API Reference**: [API Documentation](./docs/api-reference.md)
- **Examples**: [Example Modules](./examples/)
- **Issues**: [GitHub Issues](https://github.com/your-repo/issues)
- **Community**: [Discord Server](https://discord.gg/your-server)

## 🗺 Roadmap

- [ ] **v2.0**: Enhanced performance optimization
- [ ] **v2.1**: Advanced security features
- [ ] **v2.2**: Machine learning integration
- [ ] **v2.3**: Multi-tenant support
- [ ] **v3.0**: Distributed execution
