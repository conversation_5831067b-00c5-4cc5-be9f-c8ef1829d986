import {
  ModuleField,
  ModuleExecutionContext,
  FieldRenderingEngine,
  FieldRenderResult,
  FieldGroupRenderResult,
  RenderingOptimization,
  RenderError,
  ModuleRendering,
  FieldUIConfig
} from '@/lib/types/asset-modules';
import { CacheManager } from '@/lib/utils/cache';
import { SecurityManager } from '@/lib/utils/security';

/**
 * Field Rendering Engine handles dynamic rendering of module fields
 * with optimization, caching, and security features
 */
export class ModuleFieldRenderingEngine implements FieldRenderingEngine {
  private static instance: ModuleFieldRenderingEngine;
  private cache: CacheManager;
  private renderingTemplates: Map<string, FieldRenderingTemplate> = new Map();
  private optimizationCache: Map<string, RenderingOptimization> = new Map();

  private constructor() {
    this.cache = CacheManager.getInstance();
    this.initializeRenderingTemplates();
  }

  static getInstance(): ModuleFieldRenderingEngine {
    if (!ModuleFieldRenderingEngine.instance) {
      ModuleFieldRenderingEngine.instance = new ModuleFieldRenderingEngine();
    }
    return ModuleFieldRenderingEngine.instance;
  }

  /**
   * Initialize built-in rendering templates for different field types
   */
  private initializeRenderingTemplates(): void {
    // Text field template
    this.renderingTemplates.set('text', {
      html: `<div class="field-wrapper {{wrapperClass}}">
        <label class="field-label {{labelClass}}" for="{{fieldId}}">{{label}}</label>
        <input
          type="text"
          id="{{fieldId}}"
          name="{{fieldName}}"
          value="{{value}}"
          placeholder="{{placeholder}}"
          class="field-input {{inputClass}}"
          {{required}}
          {{disabled}}
          {{attributes}}
        />
        {{helpText}}
        {{errorMessage}}
      </div>`,
      css: `.field-wrapper { margin-bottom: 1rem; }
        .field-label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
        .field-input { width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; }
        .field-input:focus { outline: none; border-color: #3b82f6; box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }`,
      javascript: `
        function initializeTextField(fieldId) {
          const input = document.getElementById(fieldId);
          if (input) {
            input.addEventListener('input', function(e) {
              validateField(fieldId, e.target.value);
            });
          }
        }
      `,
      dependencies: ['validation-utils']
    });

    // Number field template
    this.renderingTemplates.set('number', {
      html: `<div class="field-wrapper {{wrapperClass}}">
        <label class="field-label {{labelClass}}" for="{{fieldId}}">{{label}}</label>
        <input
          type="number"
          id="{{fieldId}}"
          name="{{fieldName}}"
          value="{{value}}"
          placeholder="{{placeholder}}"
          class="field-input {{inputClass}}"
          min="{{min}}"
          max="{{max}}"
          step="{{step}}"
          {{required}}
          {{disabled}}
          {{attributes}}
        />
        {{helpText}}
        {{errorMessage}}
      </div>`,
      css: `.field-wrapper { margin-bottom: 1rem; }
        .field-label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
        .field-input { width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; }`,
      javascript: `
        function initializeNumberField(fieldId) {
          const input = document.getElementById(fieldId);
          if (input) {
            input.addEventListener('input', function(e) {
              validateNumberField(fieldId, e.target.value);
            });
          }
        }
      `,
      dependencies: ['validation-utils', 'number-utils']
    });

    // Select field template
    this.renderingTemplates.set('select', {
      html: `<div class="field-wrapper {{wrapperClass}}">
        <label class="field-label {{labelClass}}" for="{{fieldId}}">{{label}}</label>
        <select
          id="{{fieldId}}"
          name="{{fieldName}}"
          class="field-select {{selectClass}}"
          {{required}}
          {{disabled}}
          {{attributes}}
        >
          {{options}}
        </select>
        {{helpText}}
        {{errorMessage}}
      </div>`,
      css: `.field-wrapper { margin-bottom: 1rem; }
        .field-label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
        .field-select { width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; }`,
      javascript: `
        function initializeSelectField(fieldId) {
          const select = document.getElementById(fieldId);
          if (select) {
            select.addEventListener('change', function(e) {
              validateField(fieldId, e.target.value);
            });
          }
        }
      `,
      dependencies: ['validation-utils']
    });

    // Add more field type templates as needed...
  }

  /**
   * Render a single field
   */
  async renderField(field: ModuleField, value: any, context: ModuleExecutionContext): Promise<FieldRenderResult> {
    const startTime = Date.now();
    const cacheKey = `field-render-${field.id}-${JSON.stringify(value)}-${context.executionId}`;

    try {
      // Check cache first
      const cached = this.cache.get<FieldRenderResult>(cacheKey);
      if (cached) {
        return cached;
      }

      // Validate security permissions
      await this.validateRenderingPermissions(field, context);

      // Get rendering template
      const template = this.getRenderingTemplate(field);
      if (!template) {
        throw new Error(`No rendering template found for field type: ${field.type}`);
      }

      // Prepare rendering context
      const renderingContext = await this.prepareRenderingContext(field, value, context);

      // Render HTML
      const html = this.renderHTML(template.html, renderingContext);

      // Process CSS
      const css = this.processCSS(template.css, field, renderingContext);

      // Process JavaScript
      const javascript = this.processJavaScript(template.javascript, field, renderingContext);

      // Validate rendered output
      const errors = await this.validateRenderedOutput(html, css, javascript, field);

      const renderTime = Date.now() - startTime;

      const result: FieldRenderResult = {
        fieldId: field.id,
        html,
        css,
        javascript,
        dependencies: template.dependencies,
        metadata: {
          renderTime,
          cacheKey,
          version: '1.0.0'
        },
        errors
      };

      // Cache the result
      this.cache.set(cacheKey, result, 5 * 60 * 1000); // Cache for 5 minutes

      return result;

    } catch (error) {
      const renderTime = Date.now() - startTime;

      return {
        fieldId: field.id,
        html: `<div class="field-error">Error rendering field: ${error instanceof Error ? error.message : 'Unknown error'}</div>`,
        css: '.field-error { color: #ef4444; padding: 1rem; border: 1px solid #fecaca; border-radius: 0.375rem; }',
        javascript: '',
        dependencies: [],
        metadata: {
          renderTime,
          cacheKey,
          version: '1.0.0'
        },
        errors: [{
          fieldId: field.id,
          type: 'syntax',
          message: error instanceof Error ? error.message : 'Unknown rendering error',
          severity: 'error'
        }]
      };
    }
  }

  /**
   * Render a group of fields
   */
  async renderFieldGroup(fields: ModuleField[], values: Record<string, any>, context: ModuleExecutionContext): Promise<FieldGroupRenderResult> {
    const startTime = Date.now();
    const groupId = `group-${fields.map(f => f.id).join('-')}`;
    let cacheHits = 0;
    let cacheMisses = 0;

    try {
      // Render each field
      const fieldResults: FieldRenderResult[] = [];

      for (const field of fields) {
        const fieldValue = values[field.name] || field.defaultValue;
        const cacheKey = `field-render-${field.id}-${JSON.stringify(fieldValue)}-${context.executionId}`;

        if (this.cache.has(cacheKey)) {
          cacheHits++;
        } else {
          cacheMisses++;
        }

        const result = await this.renderField(field, fieldValue, context);
        fieldResults.push(result);
      }

      // Generate group layout
      const layout = await this.generateGroupLayout(fields, fieldResults, context);

      const totalRenderTime = Date.now() - startTime;

      return {
        groupId,
        fields: fieldResults,
        layout,
        totalRenderTime,
        cacheHits,
        cacheMisses
      };

    } catch (error) {
      return {
        groupId,
        fields: [],
        layout: `<div class="group-error">Error rendering field group: ${error instanceof Error ? error.message : 'Unknown error'}</div>`,
        totalRenderTime: Date.now() - startTime,
        cacheHits,
        cacheMisses
      };
    }
  }

  /**
   * Validate rendering permissions
   */
  private async validateRenderingPermissions(field: ModuleField, context: ModuleExecutionContext): Promise<void> {
    // Check if user has permission to render this field
    if (!context.permissions.includes('field.render')) {
      throw new Error(`Insufficient permissions to render field ${field.id}`);
    }

    // Check field-specific permissions
    if (field.name.startsWith('sensitive_') && !context.permissions.includes('field.render.sensitive')) {
      throw new Error(`Insufficient permissions to render sensitive field ${field.id}`);
    }
  }

  /**
   * Get rendering template for field type
   */
  private getRenderingTemplate(field: ModuleField): FieldRenderingTemplate | null {
    return this.renderingTemplates.get(field.type) || null;
  }

  /**
   * Prepare rendering context with field data and values
   */
  private async prepareRenderingContext(field: ModuleField, value: any, context: ModuleExecutionContext): Promise<RenderingContext> {
    const renderingContext: RenderingContext = {
      fieldId: field.id,
      fieldName: field.name,
      label: field.label,
      value: this.sanitizeValue(value),
      placeholder: field.placeholder || '',
      required: field.isRequired ? 'required' : '',
      disabled: field.uiConfig?.disabled ? 'disabled' : '',
      attributes: this.buildAttributes(field.uiConfig),
      wrapperClass: this.buildWrapperClass(field.uiConfig),
      labelClass: this.buildLabelClass(field.uiConfig),
      inputClass: this.buildInputClass(field.uiConfig),
      selectClass: this.buildSelectClass(field.uiConfig),
      helpText: field.helpText ? `<div class="field-help">${field.helpText}</div>` : '',
      errorMessage: '', // Will be populated during validation
      options: await this.buildOptions(field),
      min: field.validation?.min?.toString() || '',
      max: field.validation?.max?.toString() || '',
      step: field.validation?.step?.toString() || '1'
    };

    return renderingContext;
  }

  /**
   * Render HTML template with context
   */
  private renderHTML(template: string, context: RenderingContext): string {
    let html = template;

    // Replace all template variables
    Object.entries(context).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      html = html.replace(regex, String(value));
    });

    return html;
  }

  /**
   * Process CSS with field-specific customizations
   */
  private processCSS(css: string, field: ModuleField, context: RenderingContext): string {
    let processedCSS = css;

    // Add field-specific CSS customizations
    if (field.uiConfig?.customCSS) {
      processedCSS += '\n' + field.uiConfig.customCSS;
    }

    // Add responsive CSS if needed
    if (field.uiConfig?.responsive) {
      processedCSS += this.generateResponsiveCSS(field);
    }

    return processedCSS;
  }

  /**
   * Process JavaScript with field-specific functionality
   */
  private processJavaScript(javascript: string, field: ModuleField, context: RenderingContext): string {
    let processedJS = javascript;

    // Replace field ID in JavaScript
    processedJS = processedJS.replace(/{{fieldId}}/g, field.id);

    // Add field-specific JavaScript
    if (field.uiConfig?.customJS) {
      processedJS += '\n' + field.uiConfig.customJS;
    }

    // Add initialization call
    processedJS += `\n// Initialize field\ndocument.addEventListener('DOMContentLoaded', function() {\n  initialize${field.type.charAt(0).toUpperCase() + field.type.slice(1)}Field('${field.id}');\n});`;

    return processedJS;
  }

  /**
   * Validate rendered output for security and correctness
   */
  private async validateRenderedOutput(html: string, css: string, javascript: string, field: ModuleField): Promise<RenderError[]> {
    const errors: RenderError[] = [];

    // Validate HTML
    if (html.includes('<script>')) {
      errors.push({
        fieldId: field.id,
        type: 'security',
        message: 'Script tags are not allowed in field HTML',
        severity: 'critical'
      });
    }

    // Validate CSS
    if (css.includes('javascript:') || css.includes('expression(')) {
      errors.push({
        fieldId: field.id,
        type: 'security',
        message: 'JavaScript expressions are not allowed in CSS',
        severity: 'critical'
      });
    }

    // Validate JavaScript
    if (javascript.includes('eval(') || javascript.includes('Function(')) {
      errors.push({
        fieldId: field.id,
        type: 'security',
        message: 'Dynamic code execution is not allowed',
        severity: 'critical'
      });
    }

    return errors;
  }

  /**
   * Generate group layout HTML
   */
  private async generateGroupLayout(fields: ModuleField[], fieldResults: FieldRenderResult[], context: ModuleExecutionContext): Promise<string> {
    // Group fields by their group property
    const fieldGroups = new Map<string, { fields: ModuleField[], results: FieldRenderResult[] }>();

    fields.forEach((field, index) => {
      const groupName = field.group || 'default';
      if (!fieldGroups.has(groupName)) {
        fieldGroups.set(groupName, { fields: [], results: [] });
      }
      fieldGroups.get(groupName)!.fields.push(field);
      fieldGroups.get(groupName)!.results.push(fieldResults[index]);
    });

    // Generate layout HTML
    let layoutHTML = '<div class="field-group-container">';

    for (const [groupName, group] of fieldGroups) {
      layoutHTML += `<div class="field-group" data-group="${groupName}">`;

      if (groupName !== 'default') {
        layoutHTML += `<h3 class="field-group-title">${groupName}</h3>`;
      }

      layoutHTML += '<div class="field-group-fields">';
      group.results.forEach(result => {
        layoutHTML += result.html;
      });
      layoutHTML += '</div></div>';
    }

    layoutHTML += '</div>';

    return layoutHTML;
  }

  // Helper methods
  private sanitizeValue(value: any): string {
    if (value === null || value === undefined) return '';
    return String(value).replace(/[<>&"']/g, (char) => {
      const entities: Record<string, string> = {
        '<': '&lt;',
        '>': '&gt;',
        '&': '&amp;',
        '"': '&quot;',
        "'": '&#39;'
      };
      return entities[char] || char;
    });
  }

  private buildAttributes(uiConfig?: FieldUIConfig): string {
    if (!uiConfig?.attributes) return '';
    return Object.entries(uiConfig.attributes)
      .map(([key, value]) => `${key}="${this.sanitizeValue(value)}"`)
      .join(' ');
  }

  private buildWrapperClass(uiConfig?: FieldUIConfig): string {
    const classes = ['field-wrapper'];
    if (uiConfig?.width) classes.push(`field-width-${uiConfig.width}`);
    if (uiConfig?.customClass) classes.push(uiConfig.customClass);
    return classes.join(' ');
  }

  private buildLabelClass(uiConfig?: FieldUIConfig): string {
    const classes = ['field-label'];
    if (uiConfig?.labelClass) classes.push(uiConfig.labelClass);
    return classes.join(' ');
  }

  private buildInputClass(uiConfig?: FieldUIConfig): string {
    const classes = ['field-input'];
    if (uiConfig?.inputClass) classes.push(uiConfig.inputClass);
    return classes.join(' ');
  }

  private buildSelectClass(uiConfig?: FieldUIConfig): string {
    const classes = ['field-select'];
    if (uiConfig?.selectClass) classes.push(uiConfig.selectClass);
    return classes.join(' ');
  }

  private async buildOptions(field: ModuleField): Promise<string> {
    if (field.type !== 'select' && field.type !== 'multiselect') return '';

    const options = field.validation?.options || [];
    return options.map(option =>
      `<option value="${this.sanitizeValue(option.value)}">${this.sanitizeValue(option.label)}</option>`
    ).join('');
  }

  private generateResponsiveCSS(field: ModuleField): string {
    return `
      @media (max-width: 768px) {
        #${field.id} { width: 100%; }
      }
      @media (max-width: 480px) {
        #${field.id} { font-size: 16px; }
      }
    `;
  }

  /**
   * Validate rendering output
   */
  async validateRendering(field: ModuleField, renderResult: FieldRenderResult): Promise<boolean> {
    return renderResult.errors.length === 0;
  }

  /**
   * Optimize rendering for multiple fields
   */
  async optimizeRendering(fields: ModuleField[]): Promise<RenderingOptimization> {
    // Implementation placeholder for rendering optimization
    return {
      bundledCSS: '',
      bundledJS: '',
      optimizedFields: [],
      removedDuplicates: [],
      compressionRatio: 1.0,
      estimatedPerformanceGain: 0
    };
  }
}

// Helper interfaces
interface FieldRenderingTemplate {
  html: string;
  css: string;
  javascript: string;
  dependencies: string[];
}

interface RenderingContext {
  fieldId: string;
  fieldName: string;
  label: string;
  value: string;
  placeholder: string;
  required: string;
  disabled: string;
  attributes: string;
  wrapperClass: string;
  labelClass: string;
  inputClass: string;
  selectClass: string;
  helpText: string;
  errorMessage: string;
  options: string;
  min: string;
  max: string;
  step: string;
}