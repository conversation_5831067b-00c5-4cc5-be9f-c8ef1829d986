import {
  DataTransformationPipeline,
  DataTransformation,
  DataTransformationResult,
  TransformationValidationResult,
  TransformationOptimization,
  TransformationChain,
  TransformationExecutionStep,
  TransformationError,
  TransformationWarning,
  TransformationValidationError,
  TransformationValidationWarning,
  TransformationSuggestion,
  TransformationSchema,
  ModuleExecutionContext
} from '@/lib/types/asset-modules';
import { CacheManager } from '@/lib/utils/cache';
import { SecurityManager } from '@/lib/utils/security';

/**
 * Data Transformation Pipeline handles data processing and transformation
 * for asset modules with support for various transformation types
 */
export class ModuleDataTransformationPipeline implements DataTransformationPipeline {
  private static instance: ModuleDataTransformationPipeline;
  private cache: CacheManager;
  private transformers: Map<string, DataTransformer> = new Map();
  private executionQueue: Map<string, Promise<DataTransformationResult>> = new Map();

  private constructor() {
    this.cache = CacheManager.getInstance();
    this.initializeTransformers();
  }

  static getInstance(): ModuleDataTransformationPipeline {
    if (!ModuleDataTransformationPipeline.instance) {
      ModuleDataTransformationPipeline.instance = new ModuleDataTransformationPipeline();
    }
    return ModuleDataTransformationPipeline.instance;
  }

  /**
   * Initialize built-in data transformers
   */
  private initializeTransformers(): void {
    // Map transformer
    this.transformers.set('map', {
      transform: async (data: any, config: Record<string, any>, context: ModuleExecutionContext) => {
        const mapping = config.mapping || {};
        if (Array.isArray(data)) {
          return data.map(item => this.applyMapping(item, mapping));
        } else {
          return this.applyMapping(data, mapping);
        }
      },
      validate: async (transformation: DataTransformation) => {
        const errors: TransformationValidationError[] = [];
        if (!transformation.configuration.mapping) {
          errors.push({
            type: 'schema',
            message: 'Mapping configuration is required for map transformation',
            severity: 'error'
          });
        }
        return { valid: errors.length === 0, errors, warnings: [], suggestions: [] };
      }
    });

    // Filter transformer
    this.transformers.set('filter', {
      transform: async (data: any, config: Record<string, any>, context: ModuleExecutionContext) => {
        const condition = config.condition;
        if (!Array.isArray(data)) {
          throw new Error('Filter transformation requires array input');
        }
        return data.filter(item => this.evaluateCondition(condition, item));
      },
      validate: async (transformation: DataTransformation) => {
        const errors: TransformationValidationError[] = [];
        if (!transformation.configuration.condition) {
          errors.push({
            type: 'schema',
            message: 'Condition is required for filter transformation',
            severity: 'error'
          });
        }
        return { valid: errors.length === 0, errors, warnings: [], suggestions: [] };
      }
    });

    // Reduce transformer
    this.transformers.set('reduce', {
      transform: async (data: any, config: Record<string, any>, context: ModuleExecutionContext) => {
        if (!Array.isArray(data)) {
          throw new Error('Reduce transformation requires array input');
        }
        
        const operation = config.operation || 'sum';
        const field = config.field;
        
        switch (operation) {
          case 'sum':
            return data.reduce((sum, item) => sum + (Number(item[field]) || 0), 0);
          case 'count':
            return data.length;
          case 'average':
            const sum = data.reduce((sum, item) => sum + (Number(item[field]) || 0), 0);
            return data.length > 0 ? sum / data.length : 0;
          case 'min':
            return Math.min(...data.map(item => Number(item[field]) || 0));
          case 'max':
            return Math.max(...data.map(item => Number(item[field]) || 0));
          case 'concat':
            return data.map(item => item[field]).join(config.separator || '');
          default:
            throw new Error(`Unknown reduce operation: ${operation}`);
        }
      },
      validate: async (transformation: DataTransformation) => {
        const errors: TransformationValidationError[] = [];
        const validOperations = ['sum', 'count', 'average', 'min', 'max', 'concat'];
        
        if (!transformation.configuration.operation) {
          errors.push({
            type: 'schema',
            message: 'Operation is required for reduce transformation',
            severity: 'error'
          });
        } else if (!validOperations.includes(transformation.configuration.operation)) {
          errors.push({
            type: 'schema',
            message: `Invalid operation. Must be one of: ${validOperations.join(', ')}`,
            severity: 'error'
          });
        }
        
        return { valid: errors.length === 0, errors, warnings: [], suggestions: [] };
      }
    });

    // Validate transformer
    this.transformers.set('validate', {
      transform: async (data: any, config: Record<string, any>, context: ModuleExecutionContext) => {
        const schema = config.schema;
        const validationResult = await this.validateAgainstSchema(data, schema);
        
        if (config.throwOnError && !validationResult.valid) {
          throw new Error(`Validation failed: ${validationResult.errors.join(', ')}`);
        }
        
        return {
          data,
          valid: validationResult.valid,
          errors: validationResult.errors
        };
      },
      validate: async (transformation: DataTransformation) => {
        const errors: TransformationValidationError[] = [];
        if (!transformation.configuration.schema) {
          errors.push({
            type: 'schema',
            message: 'Schema is required for validate transformation',
            severity: 'error'
          });
        }
        return { valid: errors.length === 0, errors, warnings: [], suggestions: [] };
      }
    });

    // Normalize transformer
    this.transformers.set('normalize', {
      transform: async (data: any, config: Record<string, any>, context: ModuleExecutionContext) => {
        const normalizationType = config.type || 'trim';
        
        if (typeof data === 'string') {
          switch (normalizationType) {
            case 'trim':
              return data.trim();
            case 'lowercase':
              return data.toLowerCase();
            case 'uppercase':
              return data.toUpperCase();
            case 'capitalize':
              return data.charAt(0).toUpperCase() + data.slice(1).toLowerCase();
            case 'slug':
              return data.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
            default:
              return data;
          }
        } else if (Array.isArray(data)) {
          return data.map(item => this.transformers.get('normalize')!.transform(item, config, context));
        } else if (typeof data === 'object' && data !== null) {
          const normalized: Record<string, any> = {};
          for (const [key, value] of Object.entries(data)) {
            normalized[key] = await this.transformers.get('normalize')!.transform(value, config, context);
          }
          return normalized;
        }
        
        return data;
      },
      validate: async (transformation: DataTransformation) => {
        const validTypes = ['trim', 'lowercase', 'uppercase', 'capitalize', 'slug'];
        const errors: TransformationValidationError[] = [];
        
        if (transformation.configuration.type && !validTypes.includes(transformation.configuration.type)) {
          errors.push({
            type: 'schema',
            message: `Invalid normalization type. Must be one of: ${validTypes.join(', ')}`,
            severity: 'error'
          });
        }
        
        return { valid: errors.length === 0, errors, warnings: [], suggestions: [] };
      }
    });

    // Aggregate transformer
    this.transformers.set('aggregate', {
      transform: async (data: any, config: Record<string, any>, context: ModuleExecutionContext) => {
        if (!Array.isArray(data)) {
          throw new Error('Aggregate transformation requires array input');
        }
        
        const groupBy = config.groupBy;
        const aggregations = config.aggregations || [];
        
        // Group data
        const groups = this.groupBy(data, groupBy);
        
        // Apply aggregations to each group
        const result: Record<string, any> = {};
        for (const [groupKey, groupData] of Object.entries(groups)) {
          result[groupKey] = {};
          
          for (const aggregation of aggregations) {
            const field = aggregation.field;
            const operation = aggregation.operation;
            
            switch (operation) {
              case 'sum':
                result[groupKey][`${field}_sum`] = groupData.reduce((sum, item) => sum + (Number(item[field]) || 0), 0);
                break;
              case 'count':
                result[groupKey][`${field}_count`] = groupData.length;
                break;
              case 'average':
                const sum = groupData.reduce((sum, item) => sum + (Number(item[field]) || 0), 0);
                result[groupKey][`${field}_avg`] = groupData.length > 0 ? sum / groupData.length : 0;
                break;
              case 'min':
                result[groupKey][`${field}_min`] = Math.min(...groupData.map(item => Number(item[field]) || 0));
                break;
              case 'max':
                result[groupKey][`${field}_max`] = Math.max(...groupData.map(item => Number(item[field]) || 0));
                break;
            }
          }
        }
        
        return result;
      },
      validate: async (transformation: DataTransformation) => {
        const errors: TransformationValidationError[] = [];
        
        if (!transformation.configuration.groupBy) {
          errors.push({
            type: 'schema',
            message: 'groupBy field is required for aggregate transformation',
            severity: 'error'
          });
        }
        
        if (!transformation.configuration.aggregations || !Array.isArray(transformation.configuration.aggregations)) {
          errors.push({
            type: 'schema',
            message: 'aggregations array is required for aggregate transformation',
            severity: 'error'
          });
        }
        
        return { valid: errors.length === 0, errors, warnings: [], suggestions: [] };
      }
    });

    // Join transformer
    this.transformers.set('join', {
      transform: async (data: any, config: Record<string, any>, context: ModuleExecutionContext) => {
        const leftData = data;
        const rightData = config.rightData;
        const leftKey = config.leftKey;
        const rightKey = config.rightKey;
        const joinType = config.joinType || 'inner';
        
        if (!Array.isArray(leftData) || !Array.isArray(rightData)) {
          throw new Error('Join transformation requires array inputs');
        }
        
        const result: any[] = [];
        
        for (const leftItem of leftData) {
          const matches = rightData.filter(rightItem => leftItem[leftKey] === rightItem[rightKey]);
          
          if (matches.length > 0) {
            for (const match of matches) {
              result.push({ ...leftItem, ...match });
            }
          } else if (joinType === 'left') {
            result.push(leftItem);
          }
        }
        
        if (joinType === 'right') {
          for (const rightItem of rightData) {
            const hasMatch = leftData.some(leftItem => leftItem[leftKey] === rightItem[rightKey]);
            if (!hasMatch) {
              result.push(rightItem);
            }
          }
        }
        
        return result;
      },
      validate: async (transformation: DataTransformation) => {
        const errors: TransformationValidationError[] = [];
        const requiredFields = ['rightData', 'leftKey', 'rightKey'];
        
        for (const field of requiredFields) {
          if (!transformation.configuration[field]) {
            errors.push({
              type: 'schema',
              message: `${field} is required for join transformation`,
              severity: 'error'
            });
          }
        }
        
        const validJoinTypes = ['inner', 'left', 'right', 'outer'];
        if (transformation.configuration.joinType && !validJoinTypes.includes(transformation.configuration.joinType)) {
          errors.push({
            type: 'schema',
            message: `Invalid join type. Must be one of: ${validJoinTypes.join(', ')}`,
            severity: 'error'
          });
        }
        
        return { valid: errors.length === 0, errors, warnings: [], suggestions: [] };
      }
    });

    // Split transformer
    this.transformers.set('split', {
      transform: async (data: any, config: Record<string, any>, context: ModuleExecutionContext) => {
        const field = config.field;
        const separator = config.separator || ',';
        const limit = config.limit;
        
        if (Array.isArray(data)) {
          return data.map(item => {
            if (typeof item[field] === 'string') {
              const parts = item[field].split(separator, limit);
              return { ...item, [`${field}_split`]: parts };
            }
            return item;
          });
        } else if (typeof data === 'object' && data !== null) {
          if (typeof data[field] === 'string') {
            const parts = data[field].split(separator, limit);
            return { ...data, [`${field}_split`]: parts };
          }
        }
        
        return data;
      },
      validate: async (transformation: DataTransformation) => {
        const errors: TransformationValidationError[] = [];
        
        if (!transformation.configuration.field) {
          errors.push({
            type: 'schema',
            message: 'field is required for split transformation',
            severity: 'error'
          });
        }
        
        return { valid: errors.length === 0, errors, warnings: [], suggestions: [] };
      }
    });

    // Custom transformer
    this.transformers.set('custom', {
      transform: async (data: any, config: Record<string, any>, context: ModuleExecutionContext) => {
        const script = config.script;
        if (!script) {
          throw new Error('Custom script is required for custom transformation');
        }
        
        // In a real implementation, this would execute the custom script in a secure sandbox
        // For now, return the data unchanged
        console.warn('Custom transformation script execution not implemented');
        return data;
      },
      validate: async (transformation: DataTransformation) => {
        const errors: TransformationValidationError[] = [];
        
        if (!transformation.configuration.script) {
          errors.push({
            type: 'schema',
            message: 'script is required for custom transformation',
            severity: 'error'
          });
        }
        
        return { valid: errors.length === 0, errors, warnings: [], suggestions: [] };
      }
    });
  }

  /**
   * Transform data using a series of transformations
   */
  async transformData(
    data: Record<string, any>,
    transformations: DataTransformation[],
    context: ModuleExecutionContext
  ): Promise<DataTransformationResult> {
    const startTime = Date.now();
    const transformationId = `transform-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Check if transformation is already in progress
    if (this.executionQueue.has(transformationId)) {
      return this.executionQueue.get(transformationId)!;
    }

    const transformationPromise = this.performDataTransformation(data, transformations, context, startTime);
    this.executionQueue.set(transformationId, transformationPromise);

    try {
      const result = await transformationPromise;
      this.executionQueue.delete(transformationId);
      return result;
    } catch (error) {
      this.executionQueue.delete(transformationId);
      throw error;
    }
  }

  /**
   * Perform the actual data transformation
   */
  private async performDataTransformation(
    data: Record<string, any>,
    transformations: DataTransformation[],
    context: ModuleExecutionContext,
    startTime: number
  ): Promise<DataTransformationResult> {
    const errors: TransformationError[] = [];
    const warnings: TransformationWarning[] = [];
    const transformationsApplied: string[] = [];
    let currentData = data;
    let cacheHits = 0;

    try {
      // Sort transformations by order
      const sortedTransformations = [...transformations].sort((a, b) => a.order - b.order);

      // Apply each transformation
      for (const transformation of sortedTransformations) {
        try {
          // Check cache first
          const cacheKey = `transformation-${transformation.id}-${JSON.stringify(currentData)}`;
          const cachedResult = this.cache.get<any>(cacheKey);
          
          if (cachedResult) {
            currentData = cachedResult;
            cacheHits++;
            transformationsApplied.push(transformation.id);
            continue;
          }

          // Validate transformation
          const validationResult = await this.validateTransformation(transformation);
          if (!validationResult.valid) {
            errors.push({
              transformationId: transformation.id,
              type: 'validation',
              message: `Transformation validation failed: ${validationResult.errors.map(e => e.message).join(', ')}`,
              severity: 'error'
            });
            continue;
          }

          // Check condition if present
          if (transformation.condition && !this.evaluateCondition(transformation.condition, currentData)) {
            continue;
          }

          // Get transformer
          const transformer = this.transformers.get(transformation.type);
          if (!transformer) {
            errors.push({
              transformationId: transformation.id,
              type: 'dependency',
              message: `Unknown transformation type: ${transformation.type}`,
              severity: 'error'
            });
            continue;
          }

          // Apply transformation
          const transformedData = await transformer.transform(currentData, transformation.configuration, context);
          
          // Cache the result
          this.cache.set(cacheKey, transformedData, 5 * 60 * 1000); // Cache for 5 minutes
          
          currentData = transformedData;
          transformationsApplied.push(transformation.id);

        } catch (error) {
          errors.push({
            transformationId: transformation.id,
            type: 'runtime',
            message: error instanceof Error ? error.message : 'Transformation failed',
            data: currentData,
            severity: 'error'
          });
        }
      }

      const executionTime = Date.now() - startTime;
      const dataSize = JSON.stringify(currentData).length;

      return {
        success: errors.filter(e => e.severity === 'critical').length === 0,
        inputData: data,
        outputData: currentData,
        transformationsApplied,
        executionTime,
        errors,
        warnings,
        metadata: {
          dataSize,
          transformationCount: transformationsApplied.length,
          cacheHits,
          memoryUsage: dataSize
        }
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        success: false,
        inputData: data,
        outputData: currentData,
        transformationsApplied,
        executionTime,
        errors: [{
          transformationId: 'pipeline',
          type: 'runtime',
          message: error instanceof Error ? error.message : 'Pipeline execution failed',
          severity: 'critical'
        }],
        warnings,
        metadata: {
          dataSize: JSON.stringify(data).length,
          transformationCount: 0,
          cacheHits,
          memoryUsage: 0
        }
      };
    }
  }

  /**
   * Validate a transformation
   */
  async validateTransformation(transformation: DataTransformation): Promise<TransformationValidationResult> {
    try {
      const transformer = this.transformers.get(transformation.type);
      if (!transformer) {
        return {
          valid: false,
          errors: [{
            type: 'dependency',
            message: `Unknown transformation type: ${transformation.type}`,
            severity: 'error'
          }],
          warnings: [],
          suggestions: []
        };
      }

      return await transformer.validate(transformation);
    } catch (error) {
      return {
        valid: false,
        errors: [{
          type: 'syntax',
          message: error instanceof Error ? error.message : 'Validation failed',
          severity: 'error'
        }],
        warnings: [],
        suggestions: []
      };
    }
  }

  /**
   * Optimize transformations for better performance
   */
  async optimizeTransformations(transformations: DataTransformation[]): Promise<TransformationOptimization> {
    // Implementation placeholder for transformation optimization
    return {
      optimizedTransformations: transformations,
      optimizations: [],
      performanceGain: 0,
      memoryReduction: 0
    };
  }

  /**
   * Create transformation chain with execution plan
   */
  async createTransformationChain(transformations: DataTransformation[]): Promise<TransformationChain> {
    const sortedTransformations = [...transformations].sort((a, b) => a.order - b.order);
    const executionPlan: TransformationExecutionStep[] = [];
    
    // Create execution steps
    for (let i = 0; i < sortedTransformations.length; i++) {
      const transformation = sortedTransformations[i];
      const dependencies = transformation.dependencies || [];
      
      executionPlan.push({
        stepId: `step-${i}`,
        transformationIds: [transformation.id],
        dependencies,
        canRunInParallel: dependencies.length === 0,
        estimatedTime: 10 // ms
      });
    }

    return {
      id: `chain-${Date.now()}`,
      transformations: sortedTransformations,
      executionPlan,
      parallelizable: executionPlan.some(step => step.canRunInParallel),
      estimatedExecutionTime: executionPlan.reduce((sum, step) => sum + step.estimatedTime, 0),
      memoryRequirement: sortedTransformations.length * 1024 // Rough estimate
    };
  }

  // Helper methods
  private applyMapping(data: any, mapping: Record<string, string>): any {
    const result: Record<string, any> = {};
    for (const [sourceKey, targetKey] of Object.entries(mapping)) {
      if (data.hasOwnProperty(sourceKey)) {
        result[targetKey] = data[sourceKey];
      }
    }
    return result;
  }

  private evaluateCondition(condition: string, data: any): boolean {
    try {
      // Simple condition evaluation - in a real implementation, this would be more sophisticated
      let evaluableCondition = condition;
      
      // Replace field references with actual values
      if (typeof data === 'object' && data !== null) {
        for (const [key, value] of Object.entries(data)) {
          evaluableCondition = evaluableCondition.replace(new RegExp(`\\b${key}\\b`, 'g'), JSON.stringify(value));
        }
      }
      
      // For security, only allow basic comparisons
      const safeCondition = evaluableCondition.replace(/[^a-zA-Z0-9\s"'<>=!&|().,]/g, '');
      return eval(safeCondition);
    } catch {
      return false;
    }
  }

  private async validateAgainstSchema(data: any, schema: TransformationSchema): Promise<{ valid: boolean; errors: string[] }> {
    // Simple schema validation - in a real implementation, this would use a proper schema validator
    const errors: string[] = [];
    
    if (schema.type === 'object' && typeof data !== 'object') {
      errors.push('Expected object type');
    } else if (schema.type === 'array' && !Array.isArray(data)) {
      errors.push('Expected array type');
    } else if (schema.type === 'string' && typeof data !== 'string') {
      errors.push('Expected string type');
    } else if (schema.type === 'number' && typeof data !== 'number') {
      errors.push('Expected number type');
    } else if (schema.type === 'boolean' && typeof data !== 'boolean') {
      errors.push('Expected boolean type');
    }
    
    return { valid: errors.length === 0, errors };
  }

  private groupBy(data: any[], field: string): Record<string, any[]> {
    const groups: Record<string, any[]> = {};
    
    for (const item of data) {
      const key = String(item[field] || 'undefined');
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(item);
    }
    
    return groups;
  }
}

// Helper interfaces
interface DataTransformer {
  transform(data: any, config: Record<string, any>, context: ModuleExecutionContext): Promise<any>;
  validate(transformation: DataTransformation): Promise<TransformationValidationResult>;
}
