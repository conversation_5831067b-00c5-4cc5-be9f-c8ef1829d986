import {
  ModuleLogic,
  LogicNode,
  LogicEdge,
  ModuleExecutionContext,
  LogicExecutionEngine,
  LogicExecutionResult,
  NodeExecutionResult,
  LogicValidationResult,
  LogicOptimization,
  LogicExecutionError,
  LogicExecutionWarning,
  LogicValidationError,
  LogicValidationWarning,
  LogicSuggestion
} from '@/lib/types/asset-modules';
import { CacheManager } from '@/lib/utils/cache';
import { SecurityManager } from '@/lib/utils/security';

/**
 * Logic Execution Engine handles the execution of module logic flows
 * with support for various node types, validation, and optimization
 */
export class ModuleLogicExecutionEngine implements LogicExecutionEngine {
  private static instance: ModuleLogicExecutionEngine;
  private cache: CacheManager;
  private nodeExecutors: Map<string, NodeExecutor> = new Map();
  private executionQueue: Map<string, Promise<LogicExecutionResult>> = new Map();

  private constructor() {
    this.cache = CacheManager.getInstance();
    this.initializeNodeExecutors();
  }

  static getInstance(): ModuleLogicExecutionEngine {
    if (!ModuleLogicExecutionEngine.instance) {
      ModuleLogicExecutionEngine.instance = new ModuleLogicExecutionEngine();
    }
    return ModuleLogicExecutionEngine.instance;
  }

  /**
   * Initialize built-in node executors
   */
  private initializeNodeExecutors(): void {
    // Input node executor
    this.nodeExecutors.set('input', {
      execute: async (node: LogicNode, inputs: Record<string, any>, context: ModuleExecutionContext) => {
        const fieldName = node.data.fieldName;
        const value = inputs[fieldName] || node.data.defaultValue;
        return {
          success: true,
          output: value,
          executionTime: 1,
          memoryUsage: 0,
          metadata: { fieldName, value }
        };
      },
      validate: async (node: LogicNode) => ({ valid: true, errors: [], warnings: [] })
    });

    // Output node executor
    this.nodeExecutors.set('output', {
      execute: async (node: LogicNode, inputs: Record<string, any>, context: ModuleExecutionContext) => {
        const inputValue = inputs[node.data.inputField] || node.data.defaultValue;
        return {
          success: true,
          output: inputValue,
          executionTime: 1,
          memoryUsage: 0,
          metadata: { outputField: node.data.outputField, value: inputValue }
        };
      },
      validate: async (node: LogicNode) => ({ valid: true, errors: [], warnings: [] })
    });

    // Transform node executor
    this.nodeExecutors.set('transform', {
      execute: async (node: LogicNode, inputs: Record<string, any>, context: ModuleExecutionContext) => {
        const startTime = Date.now();
        try {
          const inputValue = inputs[node.data.inputField];
          const transformType = node.data.transformType;
          let output;

          switch (transformType) {
            case 'uppercase':
              output = String(inputValue).toUpperCase();
              break;
            case 'lowercase':
              output = String(inputValue).toLowerCase();
              break;
            case 'trim':
              output = String(inputValue).trim();
              break;
            case 'number':
              output = Number(inputValue);
              break;
            case 'string':
              output = String(inputValue);
              break;
            case 'custom':
              output = await this.executeCustomTransform(node.data.customScript, inputValue, context);
              break;
            default:
              throw new Error(`Unknown transform type: ${transformType}`);
          }

          return {
            success: true,
            output,
            executionTime: Date.now() - startTime,
            memoryUsage: JSON.stringify(output).length,
            metadata: { transformType, inputValue, outputValue: output }
          };
        } catch (error) {
          return {
            success: false,
            output: null,
            executionTime: Date.now() - startTime,
            memoryUsage: 0,
            error: {
              nodeId: node.id,
              type: 'runtime',
              message: error instanceof Error ? error.message : 'Transform execution failed',
              severity: 'error',
              recoverable: true
            },
            metadata: { transformType: node.data.transformType }
          };
        }
      },
      validate: async (node: LogicNode) => {
        const errors: LogicValidationError[] = [];
        const warnings: LogicValidationWarning[] = [];

        if (!node.data.transformType) {
          errors.push({
            nodeId: node.id,
            type: 'syntax',
            message: 'Transform type is required',
            severity: 'error'
          });
        }

        if (node.data.transformType === 'custom' && !node.data.customScript) {
          errors.push({
            nodeId: node.id,
            type: 'syntax',
            message: 'Custom script is required for custom transform',
            severity: 'error'
          });
        }

        return { valid: errors.length === 0, errors, warnings };
      }
    });

    // Condition node executor
    this.nodeExecutors.set('condition', {
      execute: async (node: LogicNode, inputs: Record<string, any>, context: ModuleExecutionContext) => {
        const startTime = Date.now();
        try {
          const leftValue = inputs[node.data.leftField] || node.data.leftValue;
          const rightValue = inputs[node.data.rightField] || node.data.rightValue;
          const operator = node.data.operator;

          let result = false;
          switch (operator) {
            case 'equals':
              result = leftValue === rightValue;
              break;
            case 'not_equals':
              result = leftValue !== rightValue;
              break;
            case 'greater_than':
              result = Number(leftValue) > Number(rightValue);
              break;
            case 'less_than':
              result = Number(leftValue) < Number(rightValue);
              break;
            case 'contains':
              result = String(leftValue).includes(String(rightValue));
              break;
            case 'starts_with':
              result = String(leftValue).startsWith(String(rightValue));
              break;
            case 'ends_with':
              result = String(leftValue).endsWith(String(rightValue));
              break;
            default:
              throw new Error(`Unknown operator: ${operator}`);
          }

          return {
            success: true,
            output: result,
            executionTime: Date.now() - startTime,
            memoryUsage: 0,
            metadata: { leftValue, rightValue, operator, result }
          };
        } catch (error) {
          return {
            success: false,
            output: false,
            executionTime: Date.now() - startTime,
            memoryUsage: 0,
            error: {
              nodeId: node.id,
              type: 'runtime',
              message: error instanceof Error ? error.message : 'Condition evaluation failed',
              severity: 'error',
              recoverable: true
            },
            metadata: { operator: node.data.operator }
          };
        }
      },
      validate: async (node: LogicNode) => {
        const errors: LogicValidationError[] = [];
        const warnings: LogicValidationWarning[] = [];

        if (!node.data.operator) {
          errors.push({
            nodeId: node.id,
            type: 'syntax',
            message: 'Operator is required for condition node',
            severity: 'error'
          });
        }

        return { valid: errors.length === 0, errors, warnings };
      }
    });

    // Calculation node executor
    this.nodeExecutors.set('calculation', {
      execute: async (node: LogicNode, inputs: Record<string, any>, context: ModuleExecutionContext) => {
        const startTime = Date.now();
        try {
          const operands = node.data.operands.map((operand: any) => {
            if (operand.type === 'field') {
              return Number(inputs[operand.value]) || 0;
            } else {
              return Number(operand.value) || 0;
            }
          });

          const operation = node.data.operation;
          let result = 0;

          switch (operation) {
            case 'add':
              result = operands.reduce((sum, val) => sum + val, 0);
              break;
            case 'subtract':
              result = operands.reduce((diff, val, index) => index === 0 ? val : diff - val);
              break;
            case 'multiply':
              result = operands.reduce((product, val) => product * val, 1);
              break;
            case 'divide':
              result = operands.reduce((quotient, val, index) => {
                if (index === 0) return val;
                if (val === 0) throw new Error('Division by zero');
                return quotient / val;
              });
              break;
            case 'average':
              result = operands.reduce((sum, val) => sum + val, 0) / operands.length;
              break;
            case 'min':
              result = Math.min(...operands);
              break;
            case 'max':
              result = Math.max(...operands);
              break;
            default:
              throw new Error(`Unknown operation: ${operation}`);
          }

          return {
            success: true,
            output: result,
            executionTime: Date.now() - startTime,
            memoryUsage: 0,
            metadata: { operation, operands, result }
          };
        } catch (error) {
          return {
            success: false,
            output: 0,
            executionTime: Date.now() - startTime,
            memoryUsage: 0,
            error: {
              nodeId: node.id,
              type: 'runtime',
              message: error instanceof Error ? error.message : 'Calculation failed',
              severity: 'error',
              recoverable: true
            },
            metadata: { operation: node.data.operation }
          };
        }
      },
      validate: async (node: LogicNode) => {
        const errors: LogicValidationError[] = [];
        const warnings: LogicValidationWarning[] = [];

        if (!node.data.operation) {
          errors.push({
            nodeId: node.id,
            type: 'syntax',
            message: 'Operation is required for calculation node',
            severity: 'error'
          });
        }

        if (!node.data.operands || node.data.operands.length === 0) {
          errors.push({
            nodeId: node.id,
            type: 'syntax',
            message: 'At least one operand is required',
            severity: 'error'
          });
        }

        return { valid: errors.length === 0, errors, warnings };
      }
    });
  }

  /**
   * Execute module logic
   */
  async executeLogic(logic: ModuleLogic, context: ModuleExecutionContext, inputs: Record<string, any>): Promise<LogicExecutionResult> {
    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    // Check if execution is already in progress
    if (this.executionQueue.has(logic.id)) {
      return this.executionQueue.get(logic.id)!;
    }

    const executionPromise = this.performLogicExecution(logic, context, inputs, executionId, startTime);
    this.executionQueue.set(logic.id, executionPromise);

    try {
      const result = await executionPromise;
      this.executionQueue.delete(logic.id);
      return result;
    } catch (error) {
      this.executionQueue.delete(logic.id);
      throw error;
    }
  }

  /**
   * Perform the actual logic execution
   */
  private async performLogicExecution(
    logic: ModuleLogic,
    context: ModuleExecutionContext,
    inputs: Record<string, any>,
    executionId: string,
    startTime: number
  ): Promise<LogicExecutionResult> {
    const nodeResults: Record<string, NodeExecutionResult> = {};
    const errors: LogicExecutionError[] = [];
    const warnings: LogicExecutionWarning[] = [];
    let outputs: Record<string, any> = {};
    let nodesExecuted = 0;
    let cacheHits = 0;

    try {
      // Validate logic before execution
      const validationResult = await this.validateLogic(logic);
      if (!validationResult.valid) {
        errors.push(...validationResult.errors.map(err => ({
          nodeId: err.nodeId,
          type: 'validation' as const,
          message: err.message,
          severity: err.severity as 'warning' | 'error' | 'critical',
          recoverable: false
        })));
      }

      // Find start nodes (nodes with no incoming edges)
      const startNodes = this.findStartNodes(logic.nodes, logic.edges);
      if (startNodes.length === 0) {
        throw new Error('No start nodes found in logic flow');
      }

      // Execute nodes in topological order
      const executionOrder = this.getExecutionOrder(logic.nodes, logic.edges);
      const nodeInputs = { ...inputs };

      for (const nodeId of executionOrder) {
        const node = logic.nodes.find(n => n.id === nodeId);
        if (!node) continue;

        // Check cache first
        const cacheKey = `node-${nodeId}-${JSON.stringify(nodeInputs)}`;
        const cachedResult = this.cache.get<NodeExecutionResult>(cacheKey);
        
        if (cachedResult) {
          nodeResults[nodeId] = cachedResult;
          nodeInputs[nodeId] = cachedResult.output;
          cacheHits++;
          continue;
        }

        // Execute node
        const nodeResult = await this.executeNode(node, context, nodeInputs);
        nodeResults[nodeId] = nodeResult;
        nodesExecuted++;

        if (nodeResult.success) {
          nodeInputs[nodeId] = nodeResult.output;
          
          // Cache successful results
          this.cache.set(cacheKey, nodeResult, 5 * 60 * 1000); // 5 minutes
        } else if (nodeResult.error) {
          errors.push(nodeResult.error);
          
          // Stop execution on critical errors
          if (nodeResult.error.severity === 'critical') {
            break;
          }
        }
      }

      // Collect outputs from output nodes
      const outputNodes = logic.nodes.filter(n => n.type === 'output');
      for (const outputNode of outputNodes) {
        const result = nodeResults[outputNode.id];
        if (result && result.success) {
          outputs[outputNode.data.outputField || outputNode.id] = result.output;
        }
      }

      const executionTime = Date.now() - startTime;

      return {
        executionId,
        success: errors.filter(e => e.severity === 'critical' || e.severity === 'error').length === 0,
        outputs,
        executionTime,
        nodesExecuted,
        nodeResults,
        errors,
        warnings,
        metadata: {
          startTime: new Date(startTime).toISOString(),
          endTime: new Date().toISOString(),
          memoryUsage: Object.values(nodeResults).reduce((sum, result) => sum + result.memoryUsage, 0),
          cacheHits
        }
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        executionId,
        success: false,
        outputs: {},
        executionTime,
        nodesExecuted,
        nodeResults,
        errors: [{
          nodeId: 'execution',
          type: 'runtime',
          message: error instanceof Error ? error.message : 'Logic execution failed',
          severity: 'critical',
          recoverable: false
        }],
        warnings,
        metadata: {
          startTime: new Date(startTime).toISOString(),
          endTime: new Date().toISOString(),
          memoryUsage: 0,
          cacheHits
        }
      };
    }
  }

  /**
   * Execute a single node
   */
  async executeNode(node: LogicNode, context: ModuleExecutionContext, inputs: Record<string, any>): Promise<NodeExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Get node executor
      const executor = this.nodeExecutors.get(node.type);
      if (!executor) {
        throw new Error(`No executor found for node type: ${node.type}`);
      }

      // Validate node before execution
      const validation = await executor.validate(node);
      if (!validation.valid) {
        throw new Error(`Node validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
      }

      // Execute node
      const result = await executor.execute(node, inputs, context);
      
      return {
        nodeId: node.id,
        success: result.success,
        output: result.output,
        executionTime: Date.now() - startTime,
        memoryUsage: result.memoryUsage,
        error: result.error,
        metadata: result.metadata || {}
      };

    } catch (error) {
      return {
        nodeId: node.id,
        success: false,
        output: null,
        executionTime: Date.now() - startTime,
        memoryUsage: 0,
        error: {
          nodeId: node.id,
          type: 'runtime',
          message: error instanceof Error ? error.message : 'Node execution failed',
          severity: 'error',
          recoverable: true
        },
        metadata: {}
      };
    }
  }

  /**
   * Validate logic structure and nodes
   */
  async validateLogic(logic: ModuleLogic): Promise<LogicValidationResult> {
    const errors: LogicValidationError[] = [];
    const warnings: LogicValidationWarning[] = [];
    const suggestions: LogicSuggestion[] = [];

    // Validate nodes
    for (const node of logic.nodes) {
      const executor = this.nodeExecutors.get(node.type);
      if (!executor) {
        errors.push({
          nodeId: node.id,
          type: 'type',
          message: `Unknown node type: ${node.type}`,
          severity: 'error'
        });
        continue;
      }

      const nodeValidation = await executor.validate(node);
      errors.push(...nodeValidation.errors);
      warnings.push(...nodeValidation.warnings);
    }

    // Check for circular dependencies
    const cycles = this.detectCycles(logic.nodes, logic.edges);
    if (cycles.length > 0) {
      errors.push({
        nodeId: cycles[0][0],
        type: 'circular',
        message: `Circular dependency detected: ${cycles[0].join(' -> ')}`,
        severity: 'critical'
      });
    }

    // Check for unreachable nodes
    const unreachableNodes = this.findUnreachableNodes(logic.nodes, logic.edges);
    for (const nodeId of unreachableNodes) {
      warnings.push({
        nodeId,
        type: 'maintainability',
        message: 'Node is unreachable and will never execute',
        impact: 'medium'
      });
    }

    // Calculate complexity
    const complexity = this.calculateComplexity(logic.nodes, logic.edges);

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      complexity
    };
  }

  /**
   * Optimize logic for better performance
   */
  async optimizeLogic(logic: ModuleLogic): Promise<LogicOptimization> {
    // Implementation placeholder for logic optimization
    return {
      optimizedLogic: logic,
      optimizations: [],
      performanceGain: 0,
      maintainabilityScore: 0
    };
  }

  // Helper methods
  private findStartNodes(nodes: LogicNode[], edges: LogicEdge[]): LogicNode[] {
    const hasIncoming = new Set(edges.map(e => e.target));
    return nodes.filter(n => !hasIncoming.has(n.id));
  }

  private getExecutionOrder(nodes: LogicNode[], edges: LogicEdge[]): string[] {
    // Topological sort implementation
    const visited = new Set<string>();
    const temp = new Set<string>();
    const result: string[] = [];

    const visit = (nodeId: string) => {
      if (temp.has(nodeId)) throw new Error('Circular dependency detected');
      if (visited.has(nodeId)) return;

      temp.add(nodeId);
      const outgoingEdges = edges.filter(e => e.source === nodeId);
      for (const edge of outgoingEdges) {
        visit(edge.target);
      }
      temp.delete(nodeId);
      visited.add(nodeId);
      result.unshift(nodeId);
    };

    for (const node of nodes) {
      if (!visited.has(node.id)) {
        visit(node.id);
      }
    }

    return result;
  }

  private detectCycles(nodes: LogicNode[], edges: LogicEdge[]): string[][] {
    // Cycle detection implementation
    return [];
  }

  private findUnreachableNodes(nodes: LogicNode[], edges: LogicEdge[]): string[] {
    // Unreachable node detection implementation
    return [];
  }

  private calculateComplexity(nodes: LogicNode[], edges: LogicEdge[]) {
    return {
      cyclomaticComplexity: edges.length - nodes.length + 2,
      cognitiveComplexity: nodes.length,
      estimatedExecutionTime: nodes.length * 10 // ms
    };
  }

  private async executeCustomTransform(script: string, inputValue: any, context: ModuleExecutionContext): Promise<any> {
    // Secure custom script execution would be implemented here
    // For now, return the input value unchanged
    return inputValue;
  }
}

// Helper interfaces
interface NodeExecutor {
  execute(node: LogicNode, inputs: Record<string, any>, context: ModuleExecutionContext): Promise<NodeExecutionResult>;
  validate(node: LogicNode): Promise<{ valid: boolean; errors: LogicValidationError[]; warnings: LogicValidationWarning[] }>;
}
