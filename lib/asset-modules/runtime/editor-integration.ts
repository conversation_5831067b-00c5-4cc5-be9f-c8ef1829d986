import {
  AssetModule,
  ModuleExecutionContext,
  ModuleInstallationPackage,
  ModuleRegistrationResult,
  ModuleActivationResult,
  FieldRenderResult,
  LogicExecutionResult,
  ModuleValidationResult
} from '@/lib/types/asset-modules';
import { runtimeSystem } from './runtime-system';
import { ModuleRegistry } from './module-registry';
import { ModuleLoader } from './module-loader';
import { CacheManager } from '@/lib/utils/cache';
import { db } from '@/lib/db';

/**
 * Asset Module Editor Integration Bridge
 * Provides seamless integration between the Asset Module Editor and Runtime System
 */
export class AssetModuleEditorIntegration {
  private static instance: AssetModuleEditorIntegration;
  private registry: ModuleRegistry;
  private loader: ModuleLoader;
  private cache: CacheManager;
  private previewSessions: Map<string, PreviewSession> = new Map();

  private constructor() {
    this.registry = ModuleRegistry.getInstance();
    this.loader = ModuleLoader.getInstance();
    this.cache = CacheManager.getInstance();
    this.setupPreviewCleanup();
  }

  static getInstance(): AssetModuleEditorIntegration {
    if (!AssetModuleEditorIntegration.instance) {
      AssetModuleEditorIntegration.instance = new AssetModuleEditorIntegration();
    }
    return AssetModuleEditorIntegration.instance;
  }

  /**
   * Setup automatic cleanup of preview sessions
   */
  private setupPreviewCleanup(): void {
    setInterval(() => {
      this.cleanupExpiredPreviewSessions();
    }, 5 * 60 * 1000); // Cleanup every 5 minutes
  }

  /**
   * Deploy module from editor to runtime
   */
  async deployModule(
    moduleData: AssetModule,
    userId: string,
    deploymentOptions: DeploymentOptions = {}
  ): Promise<ModuleDeploymentResult> {
    const startTime = Date.now();
    const deploymentId = `deploy-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    try {
      // 1. Validate module before deployment
      const validationResult = await this.validateModuleForDeployment(moduleData);
      if (!validationResult.valid) {
        return {
          success: false,
          deploymentId,
          moduleId: moduleData.id,
          errors: validationResult.errors,
          warnings: validationResult.warnings,
          deploymentTime: Date.now() - startTime,
          version: moduleData.version,
          status: 'failed'
        };
      }

      // 2. Create installation package
      const installationPackage = await this.createInstallationPackage(moduleData, deploymentOptions);

      // 3. Register module in runtime
      const registrationResult = await this.registry.registerModule(installationPackage);
      if (!registrationResult.success) {
        return {
          success: false,
          deploymentId,
          moduleId: moduleData.id,
          errors: registrationResult.errors.map(e => e.message),
          warnings: registrationResult.warnings.map(w => w.message),
          deploymentTime: Date.now() - startTime,
          version: moduleData.version,
          status: 'failed'
        };
      }

      // 4. Activate module if requested
      let activationResult: ModuleActivationResult | null = null;
      if (deploymentOptions.autoActivate !== false) {
        const context = this.createDeploymentContext(userId, moduleData);
        activationResult = await this.loader.activateModule(moduleData.id, context);
      }

      // 5. Update database with deployment info
      await this.updateModuleDeploymentStatus(moduleData.id, {
        deploymentId,
        status: 'deployed',
        deployedAt: new Date().toISOString(),
        deployedBy: userId,
        version: moduleData.version,
        isActive: activationResult?.success || false
      });

      // 6. Clear editor caches
      this.cache.invalidatePattern(`editor-module-${moduleData.id}*`);

      const deploymentTime = Date.now() - startTime;

      return {
        success: true,
        deploymentId,
        moduleId: moduleData.id,
        errors: [],
        warnings: validationResult.warnings,
        deploymentTime,
        version: moduleData.version,
        status: 'deployed',
        activationResult,
        metadata: {
          registrationTime: registrationResult.metadata.registrationTime,
          resourcesCreated: registrationResult.metadata.resourcesCreated,
          autoActivated: deploymentOptions.autoActivate !== false
        }
      };

    } catch (error) {
      return {
        success: false,
        deploymentId,
        moduleId: moduleData.id,
        errors: [error instanceof Error ? error.message : 'Deployment failed'],
        warnings: [],
        deploymentTime: Date.now() - startTime,
        version: moduleData.version,
        status: 'failed'
      };
    }
  }

  /**
   * Create preview session for testing module in editor
   */
  async createPreviewSession(
    moduleData: AssetModule,
    userId: string,
    previewOptions: PreviewOptions = {}
  ): Promise<PreviewSessionResult> {
    const sessionId = `preview-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    try {
      // 1. Create temporary module instance
      const tempModuleId = `temp-${moduleData.id}-${sessionId}`;
      const tempModule = { ...moduleData, id: tempModuleId, isPreview: true };

      // 2. Create preview installation package
      const installationPackage = await this.createInstallationPackage(tempModule, {
        isPreview: true,
        skipValidation: previewOptions.skipValidation
      });

      // 3. Register temporary module
      const registrationResult = await this.registry.registerModule(installationPackage);
      if (!registrationResult.success) {
        throw new Error(`Preview registration failed: ${registrationResult.errors.map(e => e.message).join(', ')}`);
      }

      // 4. Activate temporary module
      const context = this.createPreviewContext(userId, tempModule, sessionId);
      const activationResult = await this.loader.activateModule(tempModuleId, context);
      if (!activationResult.success) {
        throw new Error(`Preview activation failed: ${activationResult.errors.map(e => e.message).join(', ')}`);
      }

      // 5. Create preview session
      const session: PreviewSession = {
        sessionId,
        moduleId: tempModuleId,
        originalModuleId: moduleData.id,
        userId,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + (previewOptions.duration || 30 * 60 * 1000)).toISOString(), // 30 minutes default
        isActive: true,
        executionCount: 0,
        lastUsed: new Date().toISOString()
      };

      this.previewSessions.set(sessionId, session);

      return {
        success: true,
        sessionId,
        moduleId: tempModuleId,
        originalModuleId: moduleData.id,
        expiresAt: session.expiresAt,
        previewTime: Date.now() - startTime,
        context
      };

    } catch (error) {
      return {
        success: false,
        sessionId,
        moduleId: '',
        originalModuleId: moduleData.id,
        expiresAt: '',
        previewTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Preview creation failed'
      };
    }
  }

  /**
   * Execute module in preview mode
   */
  async executePreview(
    sessionId: string,
    inputData: Record<string, any>,
    assetTypeId: string
  ): Promise<PreviewExecutionResult> {
    try {
      const session = this.previewSessions.get(sessionId);
      if (!session) {
        throw new Error('Preview session not found');
      }

      if (!session.isActive || new Date() > new Date(session.expiresAt)) {
        throw new Error('Preview session expired');
      }

      // Update session usage
      session.executionCount++;
      session.lastUsed = new Date().toISOString();

      // Create preview execution context
      const context = this.createPreviewContext(session.userId, { id: session.originalModuleId } as AssetModule, sessionId);

      // Execute module
      const result = await runtimeSystem.executeModule(
        session.moduleId,
        'preview',
        assetTypeId,
        inputData,
        context
      );

      return {
        success: result.success,
        sessionId,
        executionId: result.executionId,
        executionTime: result.executionTime,
        outputs: result.outputs,
        renderResults: result.renderResults,
        validationResult: result.validationResult,
        logicResult: result.logicResult,
        errors: result.errors,
        warnings: result.warnings,
        metadata: {
          ...result.metadata,
          previewSession: true,
          executionCount: session.executionCount
        }
      };

    } catch (error) {
      return {
        success: false,
        sessionId,
        executionId: '',
        executionTime: 0,
        outputs: {},
        renderResults: {},
        validationResult: null,
        logicResult: null,
        errors: [{ type: 'execution', message: error instanceof Error ? error.message : 'Preview execution failed', severity: 'error' }],
        warnings: [],
        metadata: { previewSession: true }
      };
    }
  }

  /**
   * Test module field rendering in preview
   */
  async previewFieldRendering(
    sessionId: string,
    data: Record<string, any>,
    assetTypeId: string,
    renderOptions: any = {}
  ): Promise<PreviewRenderResult> {
    try {
      const session = this.previewSessions.get(sessionId);
      if (!session) {
        throw new Error('Preview session not found');
      }

      if (!session.isActive || new Date() > new Date(session.expiresAt)) {
        throw new Error('Preview session expired');
      }

      // Create preview context
      const context = this.createPreviewContext(session.userId, { id: session.originalModuleId } as AssetModule, sessionId);
      context.assetTypeId = assetTypeId;

      // Render fields
      const result = await runtimeSystem.renderModuleFields(
        session.moduleId,
        data,
        context
      );

      return {
        success: true,
        sessionId,
        groupId: result.groupId,
        renderTime: result.totalRenderTime,
        fieldsRendered: result.fields.length,
        html: result.layout,
        css: result.fields.map(f => f.css).join('\n'),
        javascript: result.fields.map(f => f.javascript).join('\n'),
        fields: result.fields.map(field => ({
          fieldId: field.fieldId,
          renderTime: field.metadata.renderTime,
          hasErrors: field.errors.length > 0,
          errors: field.errors
        })),
        cacheHits: result.cacheHits,
        cacheMisses: result.cacheMisses
      };

    } catch (error) {
      return {
        success: false,
        sessionId,
        groupId: '',
        renderTime: 0,
        fieldsRendered: 0,
        html: '',
        css: '',
        javascript: '',
        fields: [],
        cacheHits: 0,
        cacheMisses: 0,
        error: error instanceof Error ? error.message : 'Preview rendering failed'
      };
    }
  }

  /**
   * Validate module in preview mode
   */
  async previewValidation(
    sessionId: string,
    data: Record<string, any>,
    assetTypeId: string
  ): Promise<PreviewValidationResult> {
    try {
      const session = this.previewSessions.get(sessionId);
      if (!session) {
        throw new Error('Preview session not found');
      }

      if (!session.isActive || new Date() > new Date(session.expiresAt)) {
        throw new Error('Preview session expired');
      }

      // Create preview context
      const context = this.createPreviewContext(session.userId, { id: session.originalModuleId } as AssetModule, sessionId);
      context.assetTypeId = assetTypeId;

      // Validate data
      const result = await runtimeSystem.validateModuleData(
        session.moduleId,
        data,
        context
      );

      return {
        success: true,
        sessionId,
        valid: result.valid,
        validationTime: result.metadata.totalValidationTime,
        fieldsValidated: result.metadata.fieldsValidated,
        fieldResults: result.fieldResults,
        errors: result.errors,
        warnings: result.warnings
      };

    } catch (error) {
      return {
        success: false,
        sessionId,
        valid: false,
        validationTime: 0,
        fieldsValidated: 0,
        fieldResults: {},
        errors: [{ type: 'structure', message: error instanceof Error ? error.message : 'Preview validation failed', affectedFields: [], severity: 'error' }],
        warnings: []
      };
    }
  }

  /**
   * End preview session and cleanup
   */
  async endPreviewSession(sessionId: string): Promise<boolean> {
    try {
      const session = this.previewSessions.get(sessionId);
      if (!session) {
        return false;
      }

      // Deactivate and unload temporary module
      const context = this.createPreviewContext(session.userId, { id: session.originalModuleId } as AssetModule, sessionId);
      await this.loader.deactivateModule(session.moduleId, context);
      await this.loader.unloadModule(session.moduleId, context);

      // Remove from registry
      await this.registry.unregisterModule(session.moduleId);

      // Remove session
      this.previewSessions.delete(sessionId);

      return true;
    } catch (error) {
      console.error(`Failed to end preview session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * Get active preview sessions for a user
   */
  getActivePreviewSessions(userId: string): PreviewSession[] {
    return Array.from(this.previewSessions.values())
      .filter(session => session.userId === userId && session.isActive)
      .filter(session => new Date() <= new Date(session.expiresAt));
  }

  /**
   * Sync module changes from editor to runtime
   */
  async syncModuleChanges(
    moduleId: string,
    changes: ModuleChanges,
    userId: string
  ): Promise<ModuleSyncResult> {
    try {
      // Get current module from runtime
      const currentModule = await this.registry.getModule(moduleId);
      if (!currentModule) {
        throw new Error('Module not found in runtime');
      }

      // Apply changes
      const updatedModule = this.applyModuleChanges(currentModule, changes);

      // Update module in runtime
      const updateResult = await this.registry.updateModule(moduleId, updatedModule);

      // Restart module if it's active
      const runtimeInstance = this.loader.getRuntimeInstance(moduleId);
      if (runtimeInstance && runtimeInstance.status === 'active') {
        const context = this.createDeploymentContext(userId, updatedModule);
        await this.loader.deactivateModule(moduleId, context);
        await this.loader.activateModule(moduleId, context);
      }

      return {
        success: updateResult.success,
        moduleId,
        changes: updateResult.changes,
        migrationRequired: updateResult.migrationRequired,
        restartRequired: runtimeInstance?.status === 'active'
      };

    } catch (error) {
      return {
        success: false,
        moduleId,
        changes: [],
        migrationRequired: false,
        restartRequired: false,
        error: error instanceof Error ? error.message : 'Sync failed'
      };
    }
  }

  // Helper methods
  private async validateModuleForDeployment(module: AssetModule): Promise<{ valid: boolean; errors: string[]; warnings: string[] }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    if (!module.name || module.name.trim().length === 0) {
      errors.push('Module name is required');
    }

    if (!module.version || module.version.trim().length === 0) {
      errors.push('Module version is required');
    }

    if (!module.fields || module.fields.length === 0) {
      warnings.push('Module has no fields defined');
    }

    // Validate fields
    for (const field of module.fields) {
      if (!field.name || field.name.trim().length === 0) {
        errors.push(`Field ${field.id} is missing a name`);
      }
      if (!field.type) {
        errors.push(`Field ${field.id} is missing a type`);
      }
    }

    // Validate logic
    if (module.logic && module.logic.nodes) {
      const nodeIds = new Set(module.logic.nodes.map(n => n.id));
      for (const edge of module.logic.edges) {
        if (!nodeIds.has(edge.source)) {
          errors.push(`Logic edge references unknown source node: ${edge.source}`);
        }
        if (!nodeIds.has(edge.target)) {
          errors.push(`Logic edge references unknown target node: ${edge.target}`);
        }
      }
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  private async createInstallationPackage(module: AssetModule, options: any = {}): Promise<ModuleInstallationPackage> {
    return {
      module,
      dependencies: module.dependencies.map(depId => ({
        moduleId: depId,
        version: '*',
        type: 'required' as const,
        resolved: false
      })),
      assets: {
        icons: [],
        templates: [],
        documentation: []
      },
      installation: {
        preInstallScript: options.preInstallScript,
        postInstallScript: options.postInstallScript
      },
      verification: {
        checksum: this.calculateModuleChecksum(module),
        signature: '',
        certificate: ''
      }
    };
  }

  private createDeploymentContext(userId: string, module: AssetModule): ModuleExecutionContext {
    return {
      moduleId: module.id,
      assetId: 'deployment',
      assetTypeId: 'system',
      userId,
      userRole: 'admin',
      permissions: ['module.deploy', 'module.activate'],
      sessionId: 'deployment-session',
      executionId: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      environment: 'production',
      metadata: { deployment: true }
    };
  }

  private createPreviewContext(userId: string, module: AssetModule, sessionId: string): ModuleExecutionContext {
    return {
      moduleId: module.id,
      assetId: 'preview',
      assetTypeId: 'preview',
      userId,
      userRole: 'user',
      permissions: ['module.preview', 'module.execute'],
      sessionId,
      executionId: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      environment: 'development',
      metadata: { preview: true, sessionId }
    };
  }

  private async updateModuleDeploymentStatus(moduleId: string, status: any): Promise<void> {
    await db.assetModule.update({
      where: { id: moduleId },
      data: {
        deploymentStatus: status.status,
        deployedAt: status.deployedAt,
        deployedBy: status.deployedBy,
        isActive: status.isActive,
        version: status.version
      }
    });
  }

  private calculateModuleChecksum(module: AssetModule): string {
    const content = JSON.stringify({
      id: module.id,
      version: module.version,
      fields: module.fields,
      logic: module.logic
    });
    
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    
    return hash.toString(16);
  }

  private applyModuleChanges(module: AssetModule, changes: ModuleChanges): AssetModule {
    return {
      ...module,
      ...changes,
      updatedAt: new Date().toISOString()
    };
  }

  private cleanupExpiredPreviewSessions(): void {
    const now = new Date();
    for (const [sessionId, session] of this.previewSessions) {
      if (new Date(session.expiresAt) <= now) {
        this.endPreviewSession(sessionId);
      }
    }
  }
}

// Helper interfaces
interface DeploymentOptions {
  autoActivate?: boolean;
  skipValidation?: boolean;
  preInstallScript?: string;
  postInstallScript?: string;
  isPreview?: boolean;
}

interface PreviewOptions {
  duration?: number; // in milliseconds
  skipValidation?: boolean;
}

interface PreviewSession {
  sessionId: string;
  moduleId: string;
  originalModuleId: string;
  userId: string;
  createdAt: string;
  expiresAt: string;
  isActive: boolean;
  executionCount: number;
  lastUsed: string;
}

interface ModuleDeploymentResult {
  success: boolean;
  deploymentId: string;
  moduleId: string;
  errors: string[];
  warnings: string[];
  deploymentTime: number;
  version: string;
  status: 'deployed' | 'failed';
  activationResult?: ModuleActivationResult;
  metadata?: any;
}

interface PreviewSessionResult {
  success: boolean;
  sessionId: string;
  moduleId: string;
  originalModuleId: string;
  expiresAt: string;
  previewTime: number;
  context?: ModuleExecutionContext;
  error?: string;
}

interface PreviewExecutionResult {
  success: boolean;
  sessionId: string;
  executionId: string;
  executionTime: number;
  outputs: Record<string, any>;
  renderResults: Record<string, FieldRenderResult>;
  validationResult: ModuleValidationResult | null;
  logicResult: LogicExecutionResult | null;
  errors: any[];
  warnings: any[];
  metadata: any;
}

interface PreviewRenderResult {
  success: boolean;
  sessionId: string;
  groupId: string;
  renderTime: number;
  fieldsRendered: number;
  html: string;
  css: string;
  javascript: string;
  fields: any[];
  cacheHits: number;
  cacheMisses: number;
  error?: string;
}

interface PreviewValidationResult {
  success: boolean;
  sessionId: string;
  valid: boolean;
  validationTime: number;
  fieldsValidated: number;
  fieldResults: any;
  errors: any[];
  warnings: any[];
}

interface ModuleChanges {
  name?: string;
  description?: string;
  fields?: any[];
  logic?: any;
  rendering?: any;
  validation?: any;
  [key: string]: any;
}

interface ModuleSyncResult {
  success: boolean;
  moduleId: string;
  changes: any[];
  migrationRequired: boolean;
  restartRequired: boolean;
  error?: string;
}

// Export singleton instance
export const editorIntegration = AssetModuleEditorIntegration.getInstance();
