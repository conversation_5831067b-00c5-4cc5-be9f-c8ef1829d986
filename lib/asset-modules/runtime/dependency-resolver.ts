import {
  ModuleDependency,
  DependencyResolutionResult,
  ResolvedDependency,
  ResolutionStep,
  DependencyConflict,
  DependencyValidationResult,
  DependencyWarning,
  AssetModule,
  CompatibilityResult,
  CompatibilityIssue,
  MigrationStep
} from '@/lib/types/asset-modules';
import { ModuleRegistry } from './module-registry';
import { CacheManager } from '@/lib/utils/cache';

/**
 * Dependency Resolver handles module dependency resolution, conflict detection,
 * and compatibility checking for the Asset Module Runtime System
 */
export class DependencyResolver {
  private static instance: DependencyResolver;
  private registry: ModuleRegistry;
  private cache: CacheManager;
  private resolutionCache: Map<string, DependencyResolutionResult> = new Map();

  private constructor() {
    this.registry = ModuleRegistry.getInstance();
    this.cache = CacheManager.getInstance();
  }

  static getInstance(): DependencyResolver {
    if (!DependencyResolver.instance) {
      DependencyResolver.instance = new DependencyResolver();
    }
    return DependencyResolver.instance;
  }

  /**
   * Resolve dependencies for a module
   */
  async resolveDependencies(moduleId: string): Promise<DependencyResolutionResult> {
    const cacheKey = `dependency-resolution-${moduleId}`;
    const cached = this.cache.get<DependencyResolutionResult>(cacheKey);
    if (cached) return cached;

    try {
      // 1. Get module and its dependencies
      const module = await this.registry.getModule(moduleId);
      if (!module) {
        throw new Error(`Module ${moduleId} not found`);
      }

      const dependencies = module.dependencies.map(depId => ({
        moduleId: depId,
        version: '*', // Default to any version
        type: 'required' as const,
        resolved: false
      }));

      // 2. Build dependency graph
      const dependencyGraph = await this.buildDependencyGraph(moduleId, dependencies);

      // 3. Detect circular dependencies
      const circularDependencies = this.detectCircularDependencies(dependencyGraph);
      if (circularDependencies.length > 0) {
        return {
          success: false,
          resolvedDependencies: [],
          unresolvedDependencies: dependencies,
          conflicts: circularDependencies.map(cycle => ({
            moduleId: cycle[0],
            conflictingModules: cycle,
            type: 'version',
            severity: 'critical' as const,
            resolution: 'Break circular dependency by removing one of the dependencies'
          })),
          resolutionPlan: []
        };
      }

      // 4. Resolve each dependency
      const resolvedDependencies: ResolvedDependency[] = [];
      const unresolvedDependencies: ModuleDependency[] = [];
      const conflicts: DependencyConflict[] = [];

      for (const dependency of dependencies) {
        try {
          const resolved = await this.resolveSingleDependency(dependency, dependencyGraph);
          if (resolved) {
            resolvedDependencies.push(resolved);
            dependency.resolved = true;
            dependency.resolvedVersion = resolved.version;
          } else {
            unresolvedDependencies.push(dependency);
          }
        } catch (error) {
          unresolvedDependencies.push(dependency);
          
          if (error instanceof DependencyConflictError) {
            conflicts.push(error.conflict);
          }
        }
      }

      // 5. Create resolution plan
      const resolutionPlan = await this.createResolutionPlan(resolvedDependencies, dependencyGraph);

      // 6. Validate resolution
      const validationResult = await this.validateResolution(resolvedDependencies, conflicts);

      const result: DependencyResolutionResult = {
        success: unresolvedDependencies.length === 0 && conflicts.length === 0,
        resolvedDependencies,
        unresolvedDependencies,
        conflicts,
        resolutionPlan
      };

      // Cache the result
      this.cache.set(cacheKey, result, 10 * 60 * 1000); // Cache for 10 minutes

      return result;

    } catch (error) {
      return {
        success: false,
        resolvedDependencies: [],
        unresolvedDependencies: [],
        conflicts: [{
          moduleId,
          conflictingModules: [],
          type: 'version',
          severity: 'critical',
          resolution: `Resolution failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        }],
        resolutionPlan: []
      };
    }
  }

  /**
   * Build dependency graph for a module
   */
  private async buildDependencyGraph(rootModuleId: string, dependencies: ModuleDependency[]): Promise<DependencyGraph> {
    const graph: DependencyGraph = {
      nodes: new Map(),
      edges: new Map()
    };

    const visited = new Set<string>();
    const queue = [rootModuleId];

    // Add root node
    graph.nodes.set(rootModuleId, {
      moduleId: rootModuleId,
      dependencies: dependencies.map(d => d.moduleId),
      dependents: []
    });

    while (queue.length > 0) {
      const currentModuleId = queue.shift()!;
      
      if (visited.has(currentModuleId)) {
        continue;
      }
      visited.add(currentModuleId);

      const currentModule = await this.registry.getModule(currentModuleId);
      if (!currentModule) {
        continue;
      }

      const currentDependencies = currentModule.dependencies;

      // Add edges for dependencies
      if (!graph.edges.has(currentModuleId)) {
        graph.edges.set(currentModuleId, []);
      }

      for (const depId of currentDependencies) {
        // Add edge
        graph.edges.get(currentModuleId)!.push(depId);

        // Add dependency node if not exists
        if (!graph.nodes.has(depId)) {
          const depModule = await this.registry.getModule(depId);
          if (depModule) {
            graph.nodes.set(depId, {
              moduleId: depId,
              dependencies: depModule.dependencies,
              dependents: []
            });
            queue.push(depId);
          }
        }

        // Update dependent relationship
        const depNode = graph.nodes.get(depId);
        if (depNode && !depNode.dependents.includes(currentModuleId)) {
          depNode.dependents.push(currentModuleId);
        }
      }
    }

    return graph;
  }

  /**
   * Detect circular dependencies in the graph
   */
  private detectCircularDependencies(graph: DependencyGraph): string[][] {
    const cycles: string[][] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const dfs = (nodeId: string, path: string[]): void => {
      if (recursionStack.has(nodeId)) {
        // Found a cycle
        const cycleStart = path.indexOf(nodeId);
        if (cycleStart !== -1) {
          cycles.push(path.slice(cycleStart).concat([nodeId]));
        }
        return;
      }

      if (visited.has(nodeId)) {
        return;
      }

      visited.add(nodeId);
      recursionStack.add(nodeId);
      path.push(nodeId);

      const edges = graph.edges.get(nodeId) || [];
      for (const neighbor of edges) {
        dfs(neighbor, [...path]);
      }

      recursionStack.delete(nodeId);
    };

    for (const nodeId of graph.nodes.keys()) {
      if (!visited.has(nodeId)) {
        dfs(nodeId, []);
      }
    }

    return cycles;
  }

  /**
   * Resolve a single dependency
   */
  private async resolveSingleDependency(dependency: ModuleDependency, graph: DependencyGraph): Promise<ResolvedDependency | null> {
    try {
      // 1. Find the module
      const module = await this.registry.getModule(dependency.moduleId);
      if (!module) {
        return null;
      }

      // 2. Check version compatibility
      const isCompatible = await this.checkVersionCompatibility(dependency, module);
      if (!isCompatible) {
        throw new DependencyConflictError({
          moduleId: dependency.moduleId,
          conflictingModules: [dependency.moduleId],
          type: 'version',
          severity: 'error',
          resolution: `Version ${dependency.version} is not compatible with available version ${module.version}`
        });
      }

      // 3. Check for conflicts with other resolved dependencies
      const conflicts = await this.checkDependencyConflicts(dependency, graph);
      if (conflicts.length > 0) {
        throw new DependencyConflictError(conflicts[0]);
      }

      // 4. Create resolved dependency
      return {
        moduleId: dependency.moduleId,
        version: module.version,
        source: 'registry',
        path: `modules/${dependency.moduleId}`,
        checksum: this.calculateChecksum(module)
      };

    } catch (error) {
      if (error instanceof DependencyConflictError) {
        throw error;
      }
      return null;
    }
  }

  /**
   * Check version compatibility
   */
  private async checkVersionCompatibility(dependency: ModuleDependency, module: AssetModule): Promise<boolean> {
    // Simple version matching for now
    // In a real implementation, this would use semantic versioning
    return dependency.version === '*' || dependency.version === module.version;
  }

  /**
   * Check for dependency conflicts
   */
  private async checkDependencyConflicts(dependency: ModuleDependency, graph: DependencyGraph): Promise<DependencyConflict[]> {
    const conflicts: DependencyConflict[] = [];

    // Check for API conflicts
    const module = await this.registry.getModule(dependency.moduleId);
    if (!module) return conflicts;

    // Check if this module conflicts with any already resolved dependencies
    for (const [nodeId, node] of graph.nodes) {
      if (nodeId === dependency.moduleId) continue;

      const otherModule = await this.registry.getModule(nodeId);
      if (!otherModule) continue;

      // Check for field name conflicts
      const fieldConflicts = this.checkFieldConflicts(module, otherModule);
      if (fieldConflicts.length > 0) {
        conflicts.push({
          moduleId: dependency.moduleId,
          conflictingModules: [nodeId],
          type: 'api',
          severity: 'warning',
          resolution: `Field name conflicts detected: ${fieldConflicts.join(', ')}`
        });
      }
    }

    return conflicts;
  }

  /**
   * Check for field conflicts between modules
   */
  private checkFieldConflicts(module1: AssetModule, module2: AssetModule): string[] {
    const conflicts: string[] = [];
    const module1Fields = new Set(module1.fields.map(f => f.name));
    
    for (const field of module2.fields) {
      if (module1Fields.has(field.name)) {
        conflicts.push(field.name);
      }
    }

    return conflicts;
  }

  /**
   * Create resolution plan
   */
  private async createResolutionPlan(resolvedDependencies: ResolvedDependency[], graph: DependencyGraph): Promise<ResolutionStep[]> {
    const plan: ResolutionStep[] = [];
    const processed = new Set<string>();

    // Topological sort to determine installation order
    const sortedModules = this.topologicalSort(graph);

    let order = 0;
    for (const moduleId of sortedModules) {
      const resolved = resolvedDependencies.find(d => d.moduleId === moduleId);
      if (resolved && !processed.has(moduleId)) {
        const dependencies = graph.nodes.get(moduleId)?.dependencies || [];
        
        plan.push({
          stepId: `step-${order}`,
          action: 'install',
          moduleId,
          version: resolved.version,
          dependencies: dependencies.filter(d => processed.has(d)),
          order
        });

        processed.add(moduleId);
        order++;
      }
    }

    return plan;
  }

  /**
   * Topological sort of dependency graph
   */
  private topologicalSort(graph: DependencyGraph): string[] {
    const result: string[] = [];
    const visited = new Set<string>();
    const temp = new Set<string>();

    const visit = (nodeId: string): void => {
      if (temp.has(nodeId)) {
        throw new Error('Circular dependency detected');
      }
      if (visited.has(nodeId)) {
        return;
      }

      temp.add(nodeId);
      const edges = graph.edges.get(nodeId) || [];
      for (const neighbor of edges) {
        visit(neighbor);
      }
      temp.delete(nodeId);
      visited.add(nodeId);
      result.unshift(nodeId); // Add to beginning for reverse topological order
    };

    for (const nodeId of graph.nodes.keys()) {
      if (!visited.has(nodeId)) {
        visit(nodeId);
      }
    }

    return result;
  }

  /**
   * Validate resolution result
   */
  private async validateResolution(resolvedDependencies: ResolvedDependency[], conflicts: DependencyConflict[]): Promise<boolean> {
    // Check if all critical conflicts are resolved
    const criticalConflicts = conflicts.filter(c => c.severity === 'critical');
    if (criticalConflicts.length > 0) {
      return false;
    }

    // Validate each resolved dependency
    for (const dependency of resolvedDependencies) {
      const module = await this.registry.getModule(dependency.moduleId);
      if (!module) {
        return false;
      }

      // Verify checksum
      const expectedChecksum = this.calculateChecksum(module);
      if (dependency.checksum !== expectedChecksum) {
        return false;
      }
    }

    return true;
  }

  /**
   * Calculate module checksum for integrity verification
   */
  private calculateChecksum(module: AssetModule): string {
    // Simple checksum calculation
    // In a real implementation, this would use a proper hashing algorithm
    const content = JSON.stringify({
      id: module.id,
      version: module.version,
      fields: module.fields,
      logic: module.logic
    });
    
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return hash.toString(16);
  }

  /**
   * Clear resolution cache
   */
  clearCache(): void {
    this.resolutionCache.clear();
    this.cache.invalidatePattern('dependency-resolution-*');
  }

  /**
   * Get cached resolution
   */
  getCachedResolution(moduleId: string): DependencyResolutionResult | null {
    return this.resolutionCache.get(moduleId) || null;
  }
}

// Helper interfaces and classes
interface DependencyGraph {
  nodes: Map<string, DependencyNode>;
  edges: Map<string, string[]>;
}

interface DependencyNode {
  moduleId: string;
  dependencies: string[];
  dependents: string[];
}

class DependencyConflictError extends Error {
  constructor(public conflict: DependencyConflict) {
    super(`Dependency conflict: ${conflict.resolution}`);
    this.name = 'DependencyConflictError';
  }
}
