import {
  ModuleField,
  AssetModule,
  ModuleExecutionContext,
  ValidationEngine,
  FieldValidationResult,
  ModuleValidationResult,
  DependencyValidationResult,
  ValidationSchema,
  FieldValidationError,
  FieldValidationWarning,
  CrossFieldValidationResult,
  ModuleValidationError,
  ModuleValidationWarning,
  DependencyConflict,
  DependencyWarning,
  FieldValidationSchema,
  ValidationRule,
  ValidationTransformation,
  CrossFieldValidationRule,
  ModuleDependency
} from '@/lib/types/asset-modules';
import { CacheManager } from '@/lib/utils/cache';
import { SecurityManager } from '@/lib/utils/security';

/**
 * Validation Engine handles field validation, module validation,
 * and dependency validation for the Asset Module Runtime System
 */
export class ModuleValidationEngine implements ValidationEngine {
  private static instance: ModuleValidationEngine;
  private cache: CacheManager;
  private validators: Map<string, FieldValidator> = new Map();
  private transformers: Map<string, ValueTransformer> = new Map();

  private constructor() {
    this.cache = CacheManager.getInstance();
    this.initializeValidators();
    this.initializeTransformers();
  }

  static getInstance(): ModuleValidationEngine {
    if (!ModuleValidationEngine.instance) {
      ModuleValidationEngine.instance = new ModuleValidationEngine();
    }
    return ModuleValidationEngine.instance;
  }

  /**
   * Initialize built-in field validators
   */
  private initializeValidators(): void {
    // Required validator
    this.validators.set('required', {
      validate: async (value: any, rule: ValidationRule, field: ModuleField) => {
        const isEmpty = value === null || value === undefined || value === '';
        return {
          valid: !isEmpty,
          message: isEmpty ? (rule.message || `${field.label} is required`) : '',
          normalizedValue: value
        };
      }
    });

    // String length validator
    this.validators.set('minLength', {
      validate: async (value: any, rule: ValidationRule, field: ModuleField) => {
        const strValue = String(value || '');
        const minLength = rule.parameters.length || 0;
        const valid = strValue.length >= minLength;
        return {
          valid,
          message: valid ? '' : (rule.message || `${field.label} must be at least ${minLength} characters`),
          normalizedValue: strValue
        };
      }
    });

    this.validators.set('maxLength', {
      validate: async (value: any, rule: ValidationRule, field: ModuleField) => {
        const strValue = String(value || '');
        const maxLength = rule.parameters.length || Infinity;
        const valid = strValue.length <= maxLength;
        return {
          valid,
          message: valid ? '' : (rule.message || `${field.label} must be no more than ${maxLength} characters`),
          normalizedValue: strValue
        };
      }
    });

    // Number range validators
    this.validators.set('min', {
      validate: async (value: any, rule: ValidationRule, field: ModuleField) => {
        const numValue = Number(value);
        const min = rule.parameters.value || 0;
        const valid = !isNaN(numValue) && numValue >= min;
        return {
          valid,
          message: valid ? '' : (rule.message || `${field.label} must be at least ${min}`),
          normalizedValue: numValue
        };
      }
    });

    this.validators.set('max', {
      validate: async (value: any, rule: ValidationRule, field: ModuleField) => {
        const numValue = Number(value);
        const max = rule.parameters.value || Infinity;
        const valid = !isNaN(numValue) && numValue <= max;
        return {
          valid,
          message: valid ? '' : (rule.message || `${field.label} must be no more than ${max}`),
          normalizedValue: numValue
        };
      }
    });

    // Pattern validator
    this.validators.set('pattern', {
      validate: async (value: any, rule: ValidationRule, field: ModuleField) => {
        const strValue = String(value || '');
        const pattern = new RegExp(rule.parameters.pattern || '.*');
        const valid = pattern.test(strValue);
        return {
          valid,
          message: valid ? '' : (rule.message || `${field.label} format is invalid`),
          normalizedValue: strValue
        };
      }
    });

    // Email validator
    this.validators.set('email', {
      validate: async (value: any, rule: ValidationRule, field: ModuleField) => {
        const strValue = String(value || '');
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const valid = emailPattern.test(strValue);
        return {
          valid,
          message: valid ? '' : (rule.message || `${field.label} must be a valid email address`),
          normalizedValue: strValue
        };
      }
    });

    // URL validator
    this.validators.set('url', {
      validate: async (value: any, rule: ValidationRule, field: ModuleField) => {
        const strValue = String(value || '');
        try {
          new URL(strValue);
          return {
            valid: true,
            message: '',
            normalizedValue: strValue
          };
        } catch {
          return {
            valid: false,
            message: rule.message || `${field.label} must be a valid URL`,
            normalizedValue: strValue
          };
        }
      }
    });

    // Date validator
    this.validators.set('date', {
      validate: async (value: any, rule: ValidationRule, field: ModuleField) => {
        const dateValue = new Date(value);
        const valid = !isNaN(dateValue.getTime());
        return {
          valid,
          message: valid ? '' : (rule.message || `${field.label} must be a valid date`),
          normalizedValue: valid ? dateValue.toISOString() : value
        };
      }
    });

    // Unique validator (would check against database)
    this.validators.set('unique', {
      validate: async (value: any, rule: ValidationRule, field: ModuleField) => {
        // In a real implementation, this would check database for uniqueness
        // For now, assume all values are unique
        return {
          valid: true,
          message: '',
          normalizedValue: value
        };
      }
    });
  }

  /**
   * Initialize value transformers
   */
  private initializeTransformers(): void {
    this.transformers.set('normalize', {
      transform: async (value: any, parameters: Record<string, any>) => {
        if (typeof value === 'string') {
          return value.trim().toLowerCase();
        }
        return value;
      }
    });

    this.transformers.set('format', {
      transform: async (value: any, parameters: Record<string, any>) => {
        const format = parameters.format;
        if (format === 'uppercase') return String(value).toUpperCase();
        if (format === 'lowercase') return String(value).toLowerCase();
        if (format === 'capitalize') return String(value).charAt(0).toUpperCase() + String(value).slice(1).toLowerCase();
        return value;
      }
    });

    this.transformers.set('convert', {
      transform: async (value: any, parameters: Record<string, any>) => {
        const type = parameters.type;
        if (type === 'number') return Number(value);
        if (type === 'string') return String(value);
        if (type === 'boolean') return Boolean(value);
        return value;
      }
    });

    this.transformers.set('sanitize', {
      transform: async (value: any, parameters: Record<string, any>) => {
        if (typeof value === 'string') {
          // Remove HTML tags and dangerous characters
          return value.replace(/<[^>]*>/g, '').replace(/[<>&"']/g, '');
        }
        return value;
      }
    });
  }

  /**
   * Validate a single field
   */
  async validateField(field: ModuleField, value: any, context: ModuleExecutionContext): Promise<FieldValidationResult> {
    const startTime = Date.now();
    const cacheKey = `field-validation-${field.id}-${JSON.stringify(value)}-${context.executionId}`;

    try {
      // Check cache first
      const cached = this.cache.get<FieldValidationResult>(cacheKey);
      if (cached) {
        return cached;
      }

      const errors: FieldValidationError[] = [];
      const warnings: FieldValidationWarning[] = [];
      const rulesApplied: string[] = [];
      const transformationsApplied: string[] = [];
      let normalizedValue = value;

      // Apply transformations first
      const transformations = this.getFieldTransformations(field);
      for (const transformation of transformations) {
        const transformer = this.transformers.get(transformation.type);
        if (transformer) {
          normalizedValue = await transformer.transform(normalizedValue, transformation.parameters);
          transformationsApplied.push(transformation.type);
        }
      }

      // Apply validation rules
      const validationRules = this.getFieldValidationRules(field);
      for (const rule of validationRules) {
        const validator = this.validators.get(rule.type);
        if (!validator) {
          warnings.push({
            fieldId: field.id,
            rule: rule.type,
            type: 'deprecated',
            message: `Unknown validation rule: ${rule.type}`,
            suggestion: 'Remove or replace with a supported validation rule'
          });
          continue;
        }

        // Check rule condition if present
        if (rule.condition && !this.evaluateCondition(rule.condition, { [field.name]: normalizedValue })) {
          continue;
        }

        const validationResult = await validator.validate(normalizedValue, rule, field);
        rulesApplied.push(rule.type);

        if (!validationResult.valid) {
          errors.push({
            fieldId: field.id,
            rule: rule.type,
            type: this.getErrorType(rule.type),
            message: validationResult.message,
            value: normalizedValue,
            expectedValue: rule.parameters.value,
            severity: rule.severity === 'warning' ? 'error' : 'error'
          });
        }

        // Update normalized value from validation
        if (validationResult.normalizedValue !== undefined) {
          normalizedValue = validationResult.normalizedValue;
        }
      }

      const validationTime = Date.now() - startTime;

      const result: FieldValidationResult = {
        fieldId: field.id,
        valid: errors.length === 0,
        value,
        normalizedValue,
        errors,
        warnings,
        metadata: {
          validationTime,
          rulesApplied,
          transformationsApplied
        }
      };

      // Cache the result
      this.cache.set(cacheKey, result, 5 * 60 * 1000); // Cache for 5 minutes

      return result;

    } catch (error) {
      const validationTime = Date.now() - startTime;
      
      return {
        fieldId: field.id,
        valid: false,
        value,
        normalizedValue: value,
        errors: [{
          fieldId: field.id,
          rule: 'system',
          type: 'custom',
          message: error instanceof Error ? error.message : 'Validation error',
          value,
          severity: 'error'
        }],
        warnings: [],
        metadata: {
          validationTime,
          rulesApplied: [],
          transformationsApplied: []
        }
      };
    }
  }

  /**
   * Validate an entire module
   */
  async validateModule(module: AssetModule, data: Record<string, any>, context: ModuleExecutionContext): Promise<ModuleValidationResult> {
    const startTime = Date.now();
    const fieldResults: Record<string, FieldValidationResult> = {};
    const crossFieldValidation: CrossFieldValidationResult[] = [];
    const errors: ModuleValidationError[] = [];
    const warnings: ModuleValidationWarning[] = [];
    let fieldsValidated = 0;
    let rulesExecuted = 0;

    try {
      // Validate each field
      for (const field of module.fields) {
        const fieldValue = data[field.name];
        const fieldResult = await this.validateField(field, fieldValue, context);
        fieldResults[field.id] = fieldResult;
        fieldsValidated++;
        rulesExecuted += fieldResult.metadata.rulesApplied.length;
      }

      // Perform cross-field validation
      const crossFieldRules = this.getCrossFieldValidationRules(module);
      for (const rule of crossFieldRules) {
        const crossFieldResult = await this.validateCrossFieldRule(rule, data, module, context);
        crossFieldValidation.push(crossFieldResult);
        rulesExecuted++;
      }

      // Check for structural issues
      const structuralValidation = await this.validateModuleStructure(module);
      errors.push(...structuralValidation.errors);
      warnings.push(...structuralValidation.warnings);

      const totalValidationTime = Date.now() - startTime;
      const valid = Object.values(fieldResults).every(r => r.valid) && 
                   crossFieldValidation.every(r => r.valid) && 
                   errors.length === 0;

      return {
        moduleId: module.id,
        valid,
        fieldResults,
        crossFieldValidation,
        errors,
        warnings,
        metadata: {
          totalValidationTime,
          fieldsValidated,
          rulesExecuted
        }
      };

    } catch (error) {
      return {
        moduleId: module.id,
        valid: false,
        fieldResults,
        crossFieldValidation,
        errors: [{
          type: 'structure',
          message: error instanceof Error ? error.message : 'Module validation failed',
          affectedFields: [],
          severity: 'critical'
        }],
        warnings,
        metadata: {
          totalValidationTime: Date.now() - startTime,
          fieldsValidated,
          rulesExecuted
        }
      };
    }
  }

  /**
   * Validate module dependencies
   */
  async validateDependencies(dependencies: ModuleDependency[]): Promise<DependencyValidationResult> {
    const resolvedDependencies: ModuleDependency[] = [];
    const unresolvedDependencies: ModuleDependency[] = [];
    const conflicts: DependencyConflict[] = [];
    const warnings: DependencyWarning[] = [];

    for (const dependency of dependencies) {
      try {
        // In a real implementation, this would check if the dependency exists and is compatible
        // For now, assume all dependencies are resolved
        resolvedDependencies.push({
          ...dependency,
          resolved: true,
          resolvedVersion: dependency.version
        });
      } catch (error) {
        unresolvedDependencies.push(dependency);
      }
    }

    return {
      valid: unresolvedDependencies.length === 0 && conflicts.length === 0,
      resolvedDependencies,
      unresolvedDependencies,
      conflicts,
      warnings
    };
  }

  /**
   * Create validation schema for a set of fields
   */
  async createValidationSchema(fields: ModuleField[]): Promise<ValidationSchema> {
    const fieldSchemas: Record<string, FieldValidationSchema> = {};
    const crossFieldRules: CrossFieldValidationRule[] = [];

    for (const field of fields) {
      fieldSchemas[field.id] = {
        fieldId: field.id,
        type: field.type,
        required: field.isRequired,
        rules: this.getFieldValidationRules(field),
        transformations: this.getFieldTransformations(field),
        dependencies: field.dependsOn
      };
    }

    return {
      id: `schema-${Date.now()}`,
      moduleId: fields[0]?.id.split('-')[0] || 'unknown',
      fields: fieldSchemas,
      crossFieldRules,
      metadata: {
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };
  }

  // Helper methods
  private getFieldValidationRules(field: ModuleField): ValidationRule[] {
    const rules: ValidationRule[] = [];

    if (field.isRequired) {
      rules.push({
        id: `${field.id}-required`,
        type: 'required',
        parameters: {},
        message: `${field.label} is required`,
        severity: 'error'
      });
    }

    if (field.validation?.minLength) {
      rules.push({
        id: `${field.id}-minLength`,
        type: 'minLength',
        parameters: { length: field.validation.minLength },
        message: `${field.label} must be at least ${field.validation.minLength} characters`,
        severity: 'error'
      });
    }

    if (field.validation?.maxLength) {
      rules.push({
        id: `${field.id}-maxLength`,
        type: 'maxLength',
        parameters: { length: field.validation.maxLength },
        message: `${field.label} must be no more than ${field.validation.maxLength} characters`,
        severity: 'error'
      });
    }

    if (field.validation?.min !== undefined) {
      rules.push({
        id: `${field.id}-min`,
        type: 'min',
        parameters: { value: field.validation.min },
        message: `${field.label} must be at least ${field.validation.min}`,
        severity: 'error'
      });
    }

    if (field.validation?.max !== undefined) {
      rules.push({
        id: `${field.id}-max`,
        type: 'max',
        parameters: { value: field.validation.max },
        message: `${field.label} must be no more than ${field.validation.max}`,
        severity: 'error'
      });
    }

    if (field.validation?.pattern) {
      rules.push({
        id: `${field.id}-pattern`,
        type: 'pattern',
        parameters: { pattern: field.validation.pattern },
        message: `${field.label} format is invalid`,
        severity: 'error'
      });
    }

    if (field.type === 'email') {
      rules.push({
        id: `${field.id}-email`,
        type: 'email',
        parameters: {},
        message: `${field.label} must be a valid email address`,
        severity: 'error'
      });
    }

    if (field.type === 'url') {
      rules.push({
        id: `${field.id}-url`,
        type: 'url',
        parameters: {},
        message: `${field.label} must be a valid URL`,
        severity: 'error'
      });
    }

    if (field.isUnique) {
      rules.push({
        id: `${field.id}-unique`,
        type: 'unique',
        parameters: {},
        message: `${field.label} must be unique`,
        severity: 'error'
      });
    }

    return rules;
  }

  private getFieldTransformations(field: ModuleField): ValidationTransformation[] {
    const transformations: ValidationTransformation[] = [];

    // Add default transformations based on field type
    if (field.type === 'text' || field.type === 'textarea') {
      transformations.push({
        id: `${field.id}-sanitize`,
        type: 'sanitize',
        parameters: {},
        order: 1
      });
    }

    if (field.type === 'email') {
      transformations.push({
        id: `${field.id}-normalize`,
        type: 'normalize',
        parameters: {},
        order: 2
      });
    }

    return transformations;
  }

  private getCrossFieldValidationRules(module: AssetModule): CrossFieldValidationRule[] {
    // In a real implementation, this would extract cross-field rules from module configuration
    return [];
  }

  private async validateCrossFieldRule(rule: CrossFieldValidationRule, data: Record<string, any>, module: AssetModule, context: ModuleExecutionContext): Promise<CrossFieldValidationResult> {
    try {
      const valid = this.evaluateCondition(rule.condition, data);
      return {
        ruleId: rule.id,
        fields: rule.fields,
        valid,
        message: valid ? undefined : rule.message,
        severity: rule.severity
      };
    } catch (error) {
      return {
        ruleId: rule.id,
        fields: rule.fields,
        valid: false,
        message: `Cross-field validation error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error'
      };
    }
  }

  private async validateModuleStructure(module: AssetModule): Promise<{ errors: ModuleValidationError[]; warnings: ModuleValidationWarning[] }> {
    const errors: ModuleValidationError[] = [];
    const warnings: ModuleValidationWarning[] = [];

    // Check for duplicate field names
    const fieldNames = new Set<string>();
    const duplicateFields: string[] = [];
    
    for (const field of module.fields) {
      if (fieldNames.has(field.name)) {
        duplicateFields.push(field.name);
      } else {
        fieldNames.add(field.name);
      }
    }

    if (duplicateFields.length > 0) {
      errors.push({
        type: 'structure',
        message: `Duplicate field names found: ${duplicateFields.join(', ')}`,
        affectedFields: duplicateFields,
        severity: 'error'
      });
    }

    return { errors, warnings };
  }

  private evaluateCondition(condition: string, data: Record<string, any>): boolean {
    // Simple condition evaluation - in a real implementation, this would be more sophisticated
    try {
      // Replace field references with actual values
      let evaluableCondition = condition;
      for (const [key, value] of Object.entries(data)) {
        evaluableCondition = evaluableCondition.replace(new RegExp(`\\b${key}\\b`, 'g'), JSON.stringify(value));
      }
      
      // For security, only allow basic comparisons
      const safeCondition = evaluableCondition.replace(/[^a-zA-Z0-9\s"'<>=!&|().,]/g, '');
      return eval(safeCondition);
    } catch {
      return false;
    }
  }

  private getErrorType(ruleType: string): FieldValidationError['type'] {
    const typeMap: Record<string, FieldValidationError['type']> = {
      'required': 'required',
      'minLength': 'range',
      'maxLength': 'range',
      'min': 'range',
      'max': 'range',
      'pattern': 'format',
      'email': 'format',
      'url': 'format',
      'unique': 'custom'
    };
    return typeMap[ruleType] || 'custom';
  }
}

// Helper interfaces
interface FieldValidator {
  validate(value: any, rule: ValidationRule, field: ModuleField): Promise<{
    valid: boolean;
    message: string;
    normalizedValue?: any;
  }>;
}

interface ValueTransformer {
  transform(value: any, parameters: Record<string, any>): Promise<any>;
}
