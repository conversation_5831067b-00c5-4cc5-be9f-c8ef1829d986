import {
  AssetModule,
  ModuleExecutionContext,
  ModuleRuntimeInstance,
  ModuleEvent,
  ModuleEventType,
  FieldRenderResult,
  FieldGroupRenderResult,
  LogicExecutionResult,
  FieldValidationResult,
  ModuleValidationResult,
  DataTransformationResult,
  IntegrationResult
} from '@/lib/types/asset-modules';

import { ModuleRegistry } from './module-registry';
import { ModuleLoader } from './module-loader';
import { DependencyResolver } from './dependency-resolver';
import { ModuleFieldRenderingEngine } from './field-rendering-engine';
import { ModuleLogicExecutionEngine } from './logic-execution-engine';
import { ModuleValidationEngine } from './validation-engine';
import { ModuleDataTransformationPipeline } from './data-transformation-pipeline';
import { AssetModuleIntegrationManager } from './integration-manager';
import { CacheManager } from '@/lib/utils/cache';
import { SecurityManager } from '@/lib/utils/security';

/**
 * Asset Module Runtime System - Main orchestrator for all runtime components
 * Provides a unified interface for executing asset modules in production
 */
export class AssetModuleRuntimeSystem {
  private static instance: AssetModuleRuntimeSystem;
  
  // Core components
  private registry: ModuleRegistry;
  private loader: ModuleLoader;
  private dependencyResolver: DependencyResolver;
  private renderingEngine: ModuleFieldRenderingEngine;
  private logicEngine: ModuleLogicExecutionEngine;
  private validationEngine: ModuleValidationEngine;
  private transformationPipeline: ModuleDataTransformationPipeline;
  private integrationManager: AssetModuleIntegrationManager;
  
  // System components
  private cache: CacheManager;
  private eventListeners: Map<ModuleEventType, ((event: ModuleEvent) => void)[]> = new Map();
  private systemMetrics: SystemMetrics = {
    totalExecutions: 0,
    successfulExecutions: 0,
    failedExecutions: 0,
    averageExecutionTime: 0,
    activeModules: 0,
    memoryUsage: 0,
    cacheHitRate: 0,
    uptime: Date.now()
  };

  private constructor() {
    this.initializeComponents();
    this.setupEventHandlers();
    this.startMetricsCollection();
  }

  static getInstance(): AssetModuleRuntimeSystem {
    if (!AssetModuleRuntimeSystem.instance) {
      AssetModuleRuntimeSystem.instance = new AssetModuleRuntimeSystem();
    }
    return AssetModuleRuntimeSystem.instance;
  }

  /**
   * Initialize all runtime components
   */
  private initializeComponents(): void {
    this.registry = ModuleRegistry.getInstance();
    this.loader = ModuleLoader.getInstance();
    this.dependencyResolver = DependencyResolver.getInstance();
    this.renderingEngine = ModuleFieldRenderingEngine.getInstance();
    this.logicEngine = ModuleLogicExecutionEngine.getInstance();
    this.validationEngine = ModuleValidationEngine.getInstance();
    this.transformationPipeline = ModuleDataTransformationPipeline.getInstance();
    this.integrationManager = AssetModuleIntegrationManager.getInstance();
    this.cache = CacheManager.getInstance();
  }

  /**
   * Setup event handlers for system monitoring
   */
  private setupEventHandlers(): void {
    // Listen to module loader events
    this.loader.addEventListener('module.loaded', (event) => {
      this.updateMetrics('module.loaded', event);
    });

    this.loader.addEventListener('module.activated', (event) => {
      this.systemMetrics.activeModules++;
      this.updateMetrics('module.activated', event);
    });

    this.loader.addEventListener('module.deactivated', (event) => {
      this.systemMetrics.activeModules--;
      this.updateMetrics('module.deactivated', event);
    });

    this.loader.addEventListener('module.error', (event) => {
      this.systemMetrics.failedExecutions++;
      this.updateMetrics('module.error', event);
    });
  }

  /**
   * Start metrics collection
   */
  private startMetricsCollection(): void {
    setInterval(() => {
      this.collectSystemMetrics();
    }, 60000); // Collect metrics every minute
  }

  /**
   * Execute a complete module workflow
   */
  async executeModule(
    moduleId: string,
    assetId: string,
    assetTypeId: string,
    inputData: Record<string, any>,
    context: Partial<ModuleExecutionContext>
  ): Promise<ModuleExecutionResult> {
    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    // Create execution context
    const executionContext: ModuleExecutionContext = {
      moduleId,
      assetId,
      assetTypeId,
      userId: context.userId || 'system',
      userRole: context.userRole || 'user',
      permissions: context.permissions || ['module.execute'],
      sessionId: context.sessionId || 'system-session',
      executionId,
      timestamp: new Date().toISOString(),
      environment: context.environment || 'production',
      metadata: context.metadata || {}
    };

    try {
      this.systemMetrics.totalExecutions++;

      // 1. Load and activate module
      const runtimeInstance = await this.loader.loadModule(moduleId, executionContext);
      if (runtimeInstance.status !== 'active') {
        await this.loader.activateModule(moduleId, executionContext);
      }

      // 2. Validate input data
      const module = runtimeInstance.module;
      const validationResult = await this.validationEngine.validateModule(module, inputData, executionContext);
      
      if (!validationResult.valid) {
        throw new Error(`Input validation failed: ${validationResult.errors.map(e => e.message).join(', ')}`);
      }

      // 3. Transform input data if needed
      let processedData = inputData;
      if (module.logic.nodes.some(n => n.type === 'transform')) {
        const transformations = this.extractTransformationsFromLogic(module.logic);
        const transformationResult = await this.transformationPipeline.transformData(
          processedData,
          transformations,
          executionContext
        );
        
        if (transformationResult.success) {
          processedData = transformationResult.outputData;
        }
      }

      // 4. Execute module logic
      const logicResult = await this.logicEngine.executeLogic(
        module.logic,
        executionContext,
        processedData
      );

      // 5. Render fields with computed values
      const renderResults: Record<string, FieldRenderResult> = {};
      for (const field of module.fields) {
        const fieldValue = logicResult.outputs[field.name] || processedData[field.name] || field.defaultValue;
        const renderResult = await this.renderingEngine.renderField(field, fieldValue, executionContext);
        renderResults[field.id] = renderResult;
      }

      // 6. Generate final output
      const executionTime = Date.now() - startTime;
      this.systemMetrics.successfulExecutions++;
      this.updateAverageExecutionTime(executionTime);

      const result: ModuleExecutionResult = {
        executionId,
        moduleId,
        success: true,
        executionTime,
        validationResult,
        logicResult,
        renderResults,
        transformationResults: [],
        outputs: logicResult.outputs,
        errors: [],
        warnings: [],
        metadata: {
          startTime: new Date(startTime).toISOString(),
          endTime: new Date().toISOString(),
          context: executionContext
        }
      };

      // Emit success event
      this.emitEvent('module.executed', moduleId, executionContext, {
        success: true,
        executionTime,
        outputs: Object.keys(logicResult.outputs).length
      });

      return result;

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.systemMetrics.failedExecutions++;

      const result: ModuleExecutionResult = {
        executionId,
        moduleId,
        success: false,
        executionTime,
        validationResult: null,
        logicResult: null,
        renderResults: {},
        transformationResults: [],
        outputs: {},
        errors: [{
          type: 'execution',
          message: error instanceof Error ? error.message : 'Module execution failed',
          severity: 'critical'
        }],
        warnings: [],
        metadata: {
          startTime: new Date(startTime).toISOString(),
          endTime: new Date().toISOString(),
          context: executionContext
        }
      };

      // Emit error event
      this.emitEvent('module.error', moduleId, executionContext, {
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime
      });

      return result;
    }
  }

  /**
   * Render module fields for display
   */
  async renderModuleFields(
    moduleId: string,
    data: Record<string, any>,
    context: ModuleExecutionContext
  ): Promise<FieldGroupRenderResult> {
    const module = await this.registry.getModule(moduleId);
    if (!module) {
      throw new Error(`Module ${moduleId} not found`);
    }

    return this.renderingEngine.renderFieldGroup(module.fields, data, context);
  }

  /**
   * Validate module data
   */
  async validateModuleData(
    moduleId: string,
    data: Record<string, any>,
    context: ModuleExecutionContext
  ): Promise<ModuleValidationResult> {
    const module = await this.registry.getModule(moduleId);
    if (!module) {
      throw new Error(`Module ${moduleId} not found`);
    }

    return this.validationEngine.validateModule(module, data, context);
  }

  /**
   * Execute module logic only
   */
  async executeModuleLogic(
    moduleId: string,
    inputs: Record<string, any>,
    context: ModuleExecutionContext
  ): Promise<LogicExecutionResult> {
    const module = await this.registry.getModule(moduleId);
    if (!module) {
      throw new Error(`Module ${moduleId} not found`);
    }

    return this.logicEngine.executeLogic(module.logic, context, inputs);
  }

  /**
   * Transform data using module transformations
   */
  async transformModuleData(
    moduleId: string,
    data: Record<string, any>,
    context: ModuleExecutionContext
  ): Promise<DataTransformationResult> {
    const module = await this.registry.getModule(moduleId);
    if (!module) {
      throw new Error(`Module ${moduleId} not found`);
    }

    const transformations = this.extractTransformationsFromLogic(module.logic);
    return this.transformationPipeline.transformData(data, transformations, context);
  }

  /**
   * Get system metrics
   */
  getSystemMetrics(): SystemMetrics {
    return { ...this.systemMetrics };
  }

  /**
   * Get runtime instance for a module
   */
  getModuleRuntimeInstance(moduleId: string): ModuleRuntimeInstance | null {
    return this.loader.getRuntimeInstance(moduleId);
  }

  /**
   * Get all active modules
   */
  getActiveModules(): string[] {
    return this.loader.getModulesByStatus('active');
  }

  /**
   * Add event listener
   */
  addEventListener(eventType: ModuleEventType, listener: (event: ModuleEvent) => void): void {
    const listeners = this.eventListeners.get(eventType) || [];
    listeners.push(listener);
    this.eventListeners.set(eventType, listeners);
  }

  /**
   * Remove event listener
   */
  removeEventListener(eventType: ModuleEventType, listener: (event: ModuleEvent) => void): void {
    const listeners = this.eventListeners.get(eventType) || [];
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
      this.eventListeners.set(eventType, listeners);
    }
  }

  /**
   * Shutdown the runtime system
   */
  async shutdown(): Promise<void> {
    console.log('Shutting down Asset Module Runtime System...');
    
    // Deactivate all modules
    const activeModules = this.getActiveModules();
    for (const moduleId of activeModules) {
      await this.loader.deactivateModule(moduleId, {
        moduleId,
        userId: 'system',
        userRole: 'admin',
        permissions: ['system.shutdown'],
        sessionId: 'shutdown-session',
        executionId: 'shutdown',
        timestamp: new Date().toISOString(),
        environment: 'production',
        metadata: { reason: 'system-shutdown' }
      } as ModuleExecutionContext);
    }

    // Clear caches
    this.cache.clear();

    console.log('Asset Module Runtime System shutdown complete');
  }

  // Helper methods
  private extractTransformationsFromLogic(logic: any): any[] {
    // Extract transformation configurations from logic nodes
    return logic.nodes
      .filter((node: any) => node.type === 'transform')
      .map((node: any, index: number) => ({
        id: node.id,
        name: `Transform ${index + 1}`,
        type: node.data.transformType || 'custom',
        inputSchema: { type: 'any' },
        outputSchema: { type: 'any' },
        configuration: node.data,
        dependencies: [],
        order: index,
        metadata: {
          version: '1.0.0',
          author: 'system',
          description: 'Auto-generated from logic node',
          tags: []
        }
      }));
  }

  private emitEvent(type: ModuleEventType, moduleId: string, context: ModuleExecutionContext, data: Record<string, any>): void {
    const event: ModuleEvent = {
      id: `event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      moduleId,
      timestamp: new Date().toISOString(),
      context,
      data,
      severity: 'info',
      source: 'runtime-system',
      metadata: {}
    };

    const listeners = this.eventListeners.get(type) || [];
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error(`Error in event listener for ${type}:`, error);
      }
    });

    // Also emit to integration manager
    this.integrationManager.emitEvent(event);
  }

  private updateMetrics(eventType: string, event: ModuleEvent): void {
    // Update system metrics based on events
    if (event.data.executionTime) {
      this.updateAverageExecutionTime(event.data.executionTime);
    }
  }

  private updateAverageExecutionTime(executionTime: number): void {
    const totalExecutions = this.systemMetrics.totalExecutions;
    const currentAverage = this.systemMetrics.averageExecutionTime;
    this.systemMetrics.averageExecutionTime = 
      (currentAverage * (totalExecutions - 1) + executionTime) / totalExecutions;
  }

  private collectSystemMetrics(): void {
    // Collect system-wide metrics
    this.systemMetrics.memoryUsage = process.memoryUsage().heapUsed;
    this.systemMetrics.cacheHitRate = this.calculateCacheHitRate();
    this.systemMetrics.activeModules = this.getActiveModules().length;
  }

  private calculateCacheHitRate(): number {
    const cacheStats = this.cache.getStats();
    // This would need to be implemented in the cache manager
    return 0.85; // Placeholder
  }
}

// Helper interfaces
interface ModuleExecutionResult {
  executionId: string;
  moduleId: string;
  success: boolean;
  executionTime: number;
  validationResult: ModuleValidationResult | null;
  logicResult: LogicExecutionResult | null;
  renderResults: Record<string, FieldRenderResult>;
  transformationResults: DataTransformationResult[];
  outputs: Record<string, any>;
  errors: ExecutionError[];
  warnings: ExecutionWarning[];
  metadata: {
    startTime: string;
    endTime: string;
    context: ModuleExecutionContext;
  };
}

interface ExecutionError {
  type: 'validation' | 'logic' | 'rendering' | 'transformation' | 'execution';
  message: string;
  severity: 'warning' | 'error' | 'critical';
  details?: any;
}

interface ExecutionWarning {
  type: 'performance' | 'compatibility' | 'best-practice';
  message: string;
  suggestion?: string;
}

interface SystemMetrics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  activeModules: number;
  memoryUsage: number;
  cacheHitRate: number;
  uptime: number;
}

// Export singleton instance
export const runtimeSystem = AssetModuleRuntimeSystem.getInstance();
