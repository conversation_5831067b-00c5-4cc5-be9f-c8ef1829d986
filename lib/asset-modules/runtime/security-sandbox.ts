import {
  ModuleSandbox,
  ModulePermissions,
  ModuleExecutionContext,
  ModuleRuntimeInstance,
  ModuleEvent,
  AssetModule
} from '@/lib/types/asset-modules';
import { SecurityManager } from '@/lib/utils/security';
import { CacheManager } from '@/lib/utils/cache';

/**
 * Security Sandbox Manager handles module execution sandboxing,
 * permission validation, and security monitoring
 */
export class ModuleSecuritySandbox {
  private static instance: ModuleSecuritySandbox;
  private cache: CacheManager;
  private sandboxes: Map<string, SandboxEnvironment> = new Map();
  private auditLog: AuditLogEntry[] = [];
  private securityPolicies: Map<string, SecurityPolicy> = new Map();
  private rateLimiters: Map<string, RateLimiter> = new Map();

  private constructor() {
    this.cache = CacheManager.getInstance();
    this.initializeSecurityPolicies();
    this.startSecurityMonitoring();
  }

  static getInstance(): ModuleSecuritySandbox {
    if (!ModuleSecuritySandbox.instance) {
      ModuleSecuritySandbox.instance = new ModuleSecuritySandbox();
    }
    return ModuleSecuritySandbox.instance;
  }

  /**
   * Initialize default security policies
   */
  private initializeSecurityPolicies(): void {
    // Default security policy for modules
    this.securityPolicies.set('default', {
      id: 'default',
      name: 'Default Module Policy',
      isolationLevel: 'moderate',
      allowedAPIs: [
        'console.log',
        'JSON.parse',
        'JSON.stringify',
        'Math.*',
        'Date.*',
        'String.*',
        'Number.*',
        'Array.*',
        'Object.*'
      ],
      blockedAPIs: [
        'eval',
        'Function',
        'setTimeout',
        'setInterval',
        'XMLHttpRequest',
        'fetch',
        'WebSocket',
        'Worker',
        'SharedWorker',
        'ServiceWorker',
        'localStorage',
        'sessionStorage',
        'indexedDB',
        'navigator',
        'location',
        'history',
        'document',
        'window'
      ],
      memoryLimit: 50 * 1024 * 1024, // 50MB
      executionTimeout: 30000, // 30 seconds
      networkAccess: false,
      fileSystemAccess: false,
      databaseAccess: ['read'],
      customRestrictions: []
    });

    // Strict security policy for untrusted modules
    this.securityPolicies.set('strict', {
      id: 'strict',
      name: 'Strict Security Policy',
      isolationLevel: 'strict',
      allowedAPIs: [
        'console.log',
        'JSON.parse',
        'JSON.stringify',
        'Math.abs',
        'Math.ceil',
        'Math.floor',
        'Math.round',
        'Math.min',
        'Math.max'
      ],
      blockedAPIs: ['*'], // Block everything not explicitly allowed
      memoryLimit: 10 * 1024 * 1024, // 10MB
      executionTimeout: 10000, // 10 seconds
      networkAccess: false,
      fileSystemAccess: false,
      databaseAccess: [],
      customRestrictions: [
        'no-loops-over-1000',
        'no-recursive-calls-over-10',
        'no-large-objects-over-1mb'
      ]
    });

    // Minimal security policy for trusted system modules
    this.securityPolicies.set('minimal', {
      id: 'minimal',
      name: 'Minimal Security Policy',
      isolationLevel: 'minimal',
      allowedAPIs: ['*'], // Allow most APIs
      blockedAPIs: [
        'eval',
        'Function',
        'process.exit',
        'process.kill',
        'child_process.*',
        'fs.unlink',
        'fs.rmdir'
      ],
      memoryLimit: 200 * 1024 * 1024, // 200MB
      executionTimeout: 120000, // 2 minutes
      networkAccess: true,
      fileSystemAccess: true,
      databaseAccess: ['read', 'write'],
      customRestrictions: []
    });
  }

  /**
   * Start security monitoring
   */
  private startSecurityMonitoring(): void {
    // Monitor sandbox violations
    setInterval(() => {
      this.monitorSandboxViolations();
    }, 5000); // Check every 5 seconds

    // Clean up old audit logs
    setInterval(() => {
      this.cleanupAuditLogs();
    }, 3600000); // Clean up every hour
  }

  /**
   * Create sandbox for module
   */
  async createSandbox(
    moduleId: string,
    module: AssetModule,
    context: ModuleExecutionContext
  ): Promise<ModuleSandbox> {
    try {
      // Determine security policy
      const policyId = this.determineSecurityPolicy(module, context);
      const policy = this.securityPolicies.get(policyId) || this.securityPolicies.get('default')!;

      // Create sandbox configuration
      const sandbox: ModuleSandbox = {
        id: `sandbox-${moduleId}-${Date.now()}`,
        moduleId,
        isolationLevel: policy.isolationLevel,
        allowedAPIs: policy.allowedAPIs,
        blockedAPIs: policy.blockedAPIs,
        memoryLimit: policy.memoryLimit,
        executionTimeout: policy.executionTimeout,
        networkAccess: policy.networkAccess,
        fileSystemAccess: policy.fileSystemAccess,
        databaseAccess: policy.databaseAccess,
        permissions: this.createModulePermissions(module, context, policy)
      };

      // Create sandbox environment
      const environment = await this.createSandboxEnvironment(sandbox, policy);
      this.sandboxes.set(sandbox.id, environment);

      // Create rate limiter
      this.rateLimiters.set(moduleId, new RateLimiter(100, 60000)); // 100 requests per minute

      // Log sandbox creation
      this.logAuditEvent('sandbox.created', moduleId, context, {
        sandboxId: sandbox.id,
        policy: policyId,
        permissions: sandbox.permissions
      });

      return sandbox;

    } catch (error) {
      this.logAuditEvent('sandbox.creation.failed', moduleId, context, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Validate module permissions
   */
  async validatePermissions(
    moduleId: string,
    action: string,
    resource: string,
    context: ModuleExecutionContext
  ): Promise<PermissionValidationResult> {
    try {
      const sandbox = this.getSandboxByModuleId(moduleId);
      if (!sandbox) {
        return {
          allowed: false,
          reason: 'No sandbox found for module',
          requiredPermissions: [],
          missingPermissions: [action]
        };
      }

      const permissions = sandbox.config.permissions;
      const allowed = this.checkPermission(permissions, action, resource, context);

      if (!allowed) {
        this.logAuditEvent('permission.denied', moduleId, context, {
          action,
          resource,
          reason: 'Insufficient permissions'
        });
      }

      return {
        allowed,
        reason: allowed ? 'Permission granted' : 'Insufficient permissions',
        requiredPermissions: [action],
        missingPermissions: allowed ? [] : [action]
      };

    } catch (error) {
      this.logAuditEvent('permission.validation.error', moduleId, context, {
        action,
        resource,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        allowed: false,
        reason: 'Permission validation error',
        requiredPermissions: [action],
        missingPermissions: [action]
      };
    }
  }

  /**
   * Execute code in sandbox
   */
  async executeInSandbox<T>(
    moduleId: string,
    code: string,
    inputs: Record<string, any>,
    context: ModuleExecutionContext
  ): Promise<SandboxExecutionResult<T>> {
    const startTime = Date.now();
    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Get sandbox environment
      const environment = this.sandboxes.get(this.getSandboxIdByModuleId(moduleId));
      if (!environment) {
        throw new Error(`No sandbox environment found for module ${moduleId}`);
      }

      // Check rate limiting
      const rateLimiter = this.rateLimiters.get(moduleId);
      if (rateLimiter && !rateLimiter.checkLimit()) {
        throw new Error('Rate limit exceeded');
      }

      // Validate code before execution
      const codeValidation = await this.validateCode(code, environment.config);
      if (!codeValidation.valid) {
        throw new Error(`Code validation failed: ${codeValidation.violations.join(', ')}`);
      }

      // Execute code with timeout and memory monitoring
      const result = await this.executeWithLimits(
        code,
        inputs,
        environment,
        context,
        executionId
      );

      const executionTime = Date.now() - startTime;

      this.logAuditEvent('sandbox.execution.success', moduleId, context, {
        executionId,
        executionTime,
        memoryUsed: result.memoryUsed,
        outputSize: JSON.stringify(result.output).length
      });

      return {
        success: true,
        output: result.output,
        executionTime,
        memoryUsed: result.memoryUsed,
        violations: [],
        logs: result.logs,
        metadata: {
          executionId,
          sandboxId: environment.config.id,
          startTime: new Date(startTime).toISOString(),
          endTime: new Date().toISOString()
        }
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;

      this.logAuditEvent('sandbox.execution.failed', moduleId, context, {
        executionId,
        executionTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        success: false,
        output: null,
        executionTime,
        memoryUsed: 0,
        violations: [error instanceof Error ? error.message : 'Execution failed'],
        logs: [],
        metadata: {
          executionId,
          sandboxId: 'unknown',
          startTime: new Date(startTime).toISOString(),
          endTime: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Monitor field access
   */
  async monitorFieldAccess(
    moduleId: string,
    fieldId: string,
    accessType: 'read' | 'write',
    context: ModuleExecutionContext
  ): Promise<boolean> {
    try {
      const sandbox = this.getSandboxByModuleId(moduleId);
      if (!sandbox) {
        return false;
      }

      const permissions = sandbox.config.permissions;
      
      // Check field-level permissions
      if (accessType === 'read' && permissions.restrictedFields.includes(fieldId)) {
        this.logAuditEvent('field.access.denied', moduleId, context, {
          fieldId,
          accessType,
          reason: 'Field is restricted'
        });
        return false;
      }

      if (accessType === 'write' && !permissions.allowedFields.includes(fieldId)) {
        this.logAuditEvent('field.access.denied', moduleId, context, {
          fieldId,
          accessType,
          reason: 'Field write not allowed'
        });
        return false;
      }

      // Log successful access
      this.logAuditEvent('field.access.granted', moduleId, context, {
        fieldId,
        accessType
      });

      return true;

    } catch (error) {
      this.logAuditEvent('field.access.error', moduleId, context, {
        fieldId,
        accessType,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Get audit logs
   */
  getAuditLogs(
    moduleId?: string,
    eventType?: string,
    startTime?: Date,
    endTime?: Date,
    limit: number = 100
  ): AuditLogEntry[] {
    let logs = [...this.auditLog];

    // Apply filters
    if (moduleId) {
      logs = logs.filter(log => log.moduleId === moduleId);
    }
    if (eventType) {
      logs = logs.filter(log => log.eventType === eventType);
    }
    if (startTime) {
      logs = logs.filter(log => new Date(log.timestamp) >= startTime);
    }
    if (endTime) {
      logs = logs.filter(log => new Date(log.timestamp) <= endTime);
    }

    // Sort by timestamp (newest first) and limit
    return logs
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  /**
   * Destroy sandbox
   */
  async destroySandbox(moduleId: string, context: ModuleExecutionContext): Promise<boolean> {
    try {
      const sandboxId = this.getSandboxIdByModuleId(moduleId);
      const environment = this.sandboxes.get(sandboxId);
      
      if (environment) {
        // Cleanup sandbox resources
        await this.cleanupSandboxEnvironment(environment);
        this.sandboxes.delete(sandboxId);
      }

      // Remove rate limiter
      this.rateLimiters.delete(moduleId);

      this.logAuditEvent('sandbox.destroyed', moduleId, context, {
        sandboxId
      });

      return true;
    } catch (error) {
      this.logAuditEvent('sandbox.destruction.failed', moduleId, context, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  // Helper methods
  private determineSecurityPolicy(module: AssetModule, context: ModuleExecutionContext): string {
    // Determine security policy based on module properties and context
    if (module.isBuiltIn) {
      return 'minimal';
    }
    if (context.userRole === 'admin' && context.environment === 'development') {
      return 'moderate';
    }
    return 'strict';
  }

  private createModulePermissions(
    module: AssetModule,
    context: ModuleExecutionContext,
    policy: SecurityPolicy
  ): ModulePermissions {
    return {
      canReadAssets: policy.databaseAccess.includes('read'),
      canWriteAssets: policy.databaseAccess.includes('write'),
      canDeleteAssets: policy.databaseAccess.includes('delete'),
      canAccessUserData: context.userRole === 'admin',
      canSendNotifications: false,
      canExecuteWorkflows: false,
      canAccessExternalAPIs: policy.networkAccess,
      allowedAssetTypes: module.compatibleAssetTypes,
      allowedFields: module.fields.map(f => f.id),
      restrictedFields: module.fields.filter(f => f.name.includes('sensitive')).map(f => f.id)
    };
  }

  private async createSandboxEnvironment(
    sandbox: ModuleSandbox,
    policy: SecurityPolicy
  ): Promise<SandboxEnvironment> {
    return {
      id: sandbox.id,
      config: sandbox,
      policy,
      createdAt: new Date().toISOString(),
      lastUsed: new Date().toISOString(),
      executionCount: 0,
      violationCount: 0
    };
  }

  private getSandboxByModuleId(moduleId: string): SandboxEnvironment | null {
    for (const environment of this.sandboxes.values()) {
      if (environment.config.moduleId === moduleId) {
        return environment;
      }
    }
    return null;
  }

  private getSandboxIdByModuleId(moduleId: string): string {
    const environment = this.getSandboxByModuleId(moduleId);
    return environment ? environment.id : '';
  }

  private checkPermission(
    permissions: ModulePermissions,
    action: string,
    resource: string,
    context: ModuleExecutionContext
  ): boolean {
    // Implement permission checking logic
    switch (action) {
      case 'asset.read':
        return permissions.canReadAssets;
      case 'asset.write':
        return permissions.canWriteAssets;
      case 'asset.delete':
        return permissions.canDeleteAssets;
      case 'user.access':
        return permissions.canAccessUserData;
      case 'notification.send':
        return permissions.canSendNotifications;
      case 'workflow.execute':
        return permissions.canExecuteWorkflows;
      case 'api.external':
        return permissions.canAccessExternalAPIs;
      default:
        return false;
    }
  }

  private async validateCode(code: string, sandbox: ModuleSandbox): Promise<CodeValidationResult> {
    const violations: string[] = [];

    // Check for blocked APIs
    for (const blockedAPI of sandbox.blockedAPIs) {
      if (blockedAPI === '*') continue; // Handle wildcard separately
      
      const regex = new RegExp(`\\b${blockedAPI.replace('*', '\\w*')}\\b`, 'g');
      if (regex.test(code)) {
        violations.push(`Blocked API usage: ${blockedAPI}`);
      }
    }

    // Check for dangerous patterns
    const dangerousPatterns = [
      /eval\s*\(/g,
      /Function\s*\(/g,
      /new\s+Function/g,
      /setTimeout\s*\(/g,
      /setInterval\s*\(/g
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(code)) {
        violations.push(`Dangerous pattern detected: ${pattern.source}`);
      }
    }

    return {
      valid: violations.length === 0,
      violations
    };
  }

  private async executeWithLimits(
    code: string,
    inputs: Record<string, any>,
    environment: SandboxEnvironment,
    context: ModuleExecutionContext,
    executionId: string
  ): Promise<{ output: any; memoryUsed: number; logs: string[] }> {
    // In a real implementation, this would use a proper sandboxing solution
    // like vm2, isolated-vm, or a containerized environment
    
    const logs: string[] = [];
    const startMemory = process.memoryUsage().heapUsed;

    try {
      // Create a limited execution context
      const sandboxContext = {
        inputs,
        console: {
          log: (...args: any[]) => logs.push(args.join(' '))
        },
        Math,
        JSON,
        Date,
        String,
        Number,
        Array,
        Object
      };

      // Simple code execution (in production, use proper sandboxing)
      const func = new Function('context', `
        with (context) {
          ${code}
        }
      `);

      const output = func(sandboxContext);
      const endMemory = process.memoryUsage().heapUsed;
      const memoryUsed = endMemory - startMemory;

      environment.executionCount++;
      environment.lastUsed = new Date().toISOString();

      return {
        output,
        memoryUsed,
        logs
      };

    } catch (error) {
      environment.violationCount++;
      throw error;
    }
  }

  private logAuditEvent(
    eventType: string,
    moduleId: string,
    context: ModuleExecutionContext,
    data: Record<string, any>
  ): void {
    const entry: AuditLogEntry = {
      id: `audit-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      eventType,
      moduleId,
      userId: context.userId,
      sessionId: context.sessionId,
      data,
      severity: this.getEventSeverity(eventType),
      source: 'security-sandbox'
    };

    this.auditLog.push(entry);

    // Keep only last 10000 entries in memory
    if (this.auditLog.length > 10000) {
      this.auditLog = this.auditLog.slice(-10000);
    }
  }

  private getEventSeverity(eventType: string): 'info' | 'warning' | 'error' | 'critical' {
    if (eventType.includes('denied') || eventType.includes('violation')) {
      return 'warning';
    }
    if (eventType.includes('failed') || eventType.includes('error')) {
      return 'error';
    }
    return 'info';
  }

  private monitorSandboxViolations(): void {
    for (const environment of this.sandboxes.values()) {
      if (environment.violationCount > 10) {
        // High violation count - potential security issue
        this.logAuditEvent('sandbox.high.violations', environment.config.moduleId, {
          moduleId: environment.config.moduleId,
          userId: 'system',
          userRole: 'system',
          permissions: [],
          sessionId: 'monitoring',
          executionId: 'monitoring',
          timestamp: new Date().toISOString(),
          environment: 'production',
          metadata: {}
        }, {
          violationCount: environment.violationCount,
          sandboxId: environment.id
        });
      }
    }
  }

  private cleanupAuditLogs(): void {
    const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    this.auditLog = this.auditLog.filter(entry => new Date(entry.timestamp) > oneWeekAgo);
  }

  private async cleanupSandboxEnvironment(environment: SandboxEnvironment): Promise<void> {
    // Cleanup sandbox resources
    // In a real implementation, this would cleanup containers, processes, etc.
  }
}

// Helper interfaces and classes
interface SecurityPolicy {
  id: string;
  name: string;
  isolationLevel: 'strict' | 'moderate' | 'minimal';
  allowedAPIs: string[];
  blockedAPIs: string[];
  memoryLimit: number;
  executionTimeout: number;
  networkAccess: boolean;
  fileSystemAccess: boolean;
  databaseAccess: string[];
  customRestrictions: string[];
}

interface SandboxEnvironment {
  id: string;
  config: ModuleSandbox;
  policy: SecurityPolicy;
  createdAt: string;
  lastUsed: string;
  executionCount: number;
  violationCount: number;
}

interface PermissionValidationResult {
  allowed: boolean;
  reason: string;
  requiredPermissions: string[];
  missingPermissions: string[];
}

interface SandboxExecutionResult<T> {
  success: boolean;
  output: T | null;
  executionTime: number;
  memoryUsed: number;
  violations: string[];
  logs: string[];
  metadata: {
    executionId: string;
    sandboxId: string;
    startTime: string;
    endTime: string;
  };
}

interface CodeValidationResult {
  valid: boolean;
  violations: string[];
}

interface AuditLogEntry {
  id: string;
  timestamp: string;
  eventType: string;
  moduleId: string;
  userId: string;
  sessionId: string;
  data: Record<string, any>;
  severity: 'info' | 'warning' | 'error' | 'critical';
  source: string;
}

class RateLimiter {
  private requests: number[] = [];

  constructor(
    private maxRequests: number,
    private windowMs: number
  ) {}

  checkLimit(): boolean {
    const now = Date.now();
    const windowStart = now - this.windowMs;

    // Remove old requests
    this.requests = this.requests.filter(time => time > windowStart);

    if (this.requests.length >= this.maxRequests) {
      return false;
    }

    this.requests.push(now);
    return true;
  }
}
