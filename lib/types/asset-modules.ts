import { FormDefinition } from "@/components/form-builder";

// Core Asset Module Types
export interface AssetModule {
  id: string;
  name: string;
  version: string;
  description: string;
  category: ModuleCategory;
  author: string;
  tags: string[];
  
  // Module Configuration
  fields: ModuleField[];
  logic: ModuleLogic;
  rendering: ModuleRendering;
  validation: ModuleValidation;
  
  // Metadata
  isActive: boolean;
  isBuiltIn: boolean;
  createdAt: string;
  updatedAt: string;
  usageCount: number;
  
  // Dependencies
  dependencies: string[];
  compatibleAssetTypes: string[];
  requiredPermissions: string[];
}

export type ModuleCategory = 
  | 'location'
  | 'software'
  | 'hardware'
  | 'financial'
  | 'compliance'
  | 'maintenance'
  | 'security'
  | 'custom';

// Module Field Definition
export interface ModuleField {
  id: string;
  name: string;
  label: string;
  type: ModuleFieldType;
  description?: string;
  
  // Field Configuration
  isRequired: boolean;
  isUnique: boolean;
  defaultValue?: any;
  placeholder?: string;
  helpText?: string;
  
  // Validation Rules
  validation: FieldValidationRules;
  
  // UI Configuration
  uiConfig: FieldUIConfig;
  
  // Logic Dependencies
  dependsOn: string[];
  computedFrom?: ComputedFieldDefinition;
  
  // Grouping
  group?: string;
  order: number;
}

export type ModuleFieldType = 
  | 'text'
  | 'number'
  | 'email'
  | 'url'
  | 'phone'
  | 'date'
  | 'datetime'
  | 'boolean'
  | 'select'
  | 'multiselect'
  | 'textarea'
  | 'file'
  | 'image'
  | 'json'
  | 'address'
  | 'coordinates'
  | 'currency'
  | 'percentage'
  | 'rating'
  | 'color'
  | 'code'
  | 'markdown'
  | 'encrypted';

// Field Validation Rules
export interface FieldValidationRules {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  step?: number;
  pattern?: string;
  options?: { value: any; label: string }[];
  customValidation?: ValidationLogicNode[];
  errorMessages?: Record<string, string>;
}

// Field UI Configuration
export interface FieldUIConfig {
  width: 'full' | 'half' | 'third' | 'quarter';
  component?: string;
  props?: Record<string, any>;
  conditional?: ConditionalDisplay;
  styling?: FieldStyling;

  // Extended properties for rendering engine
  disabled?: boolean;
  customCSS?: string;
  customJS?: string;
  responsive?: boolean;
  attributes?: Record<string, any>;
  customClass?: string;
  labelClass?: string;
  inputClass?: string;
  selectClass?: string;
}

export interface ConditionalDisplay {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
  value: any;
}

export interface FieldStyling {
  className?: string;
  style?: Record<string, any>;
  variant?: string;
}

// Computed Field Definition
export interface ComputedFieldDefinition {
  expression: string;
  dependencies: string[];
  updateTrigger: 'onChange' | 'onBlur' | 'onSave';
  logicNodes?: ComputationLogicNode[];
}

// Module Logic System (inspired by Automation Hub)
export interface ModuleLogic {
  nodes: LogicNode[];
  edges: LogicEdge[];
  variables: LogicVariable[];
  functions: LogicFunction[];
}

export interface LogicNode {
  id: string;
  type: LogicNodeType;
  position: { x: number; y: number };
  data: LogicNodeData;
}

export type LogicNodeType = 
  | 'field_input'
  | 'field_output'
  | 'computation'
  | 'validation'
  | 'condition'
  | 'transform'
  | 'api_call'
  | 'database_query'
  | 'constant'
  | 'variable'
  | 'function';

export interface LogicNodeData {
  label: string;
  description?: string;
  config: Record<string, any>;
  inputs: LogicPort[];
  outputs: LogicPort[];
}

export interface LogicPort {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'any';
  required: boolean;
}

export interface LogicEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle: string;
  targetHandle: string;
  type?: string;
  data?: Record<string, any>;
}

export interface LogicVariable {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  defaultValue?: any;
  description?: string;
}

export interface LogicFunction {
  id: string;
  name: string;
  description: string;
  parameters: LogicFunctionParameter[];
  returnType: string;
  implementation: string;
}

export interface LogicFunctionParameter {
  name: string;
  type: string;
  required: boolean;
  defaultValue?: any;
}

// Validation Logic Nodes
export interface ValidationLogicNode extends LogicNode {
  type: 'validation';
  data: ValidationNodeData;
}

export interface ValidationNodeData extends LogicNodeData {
  validationType: 'required' | 'format' | 'range' | 'custom';
  errorMessage: string;
  condition: string;
}

// Computation Logic Nodes
export interface ComputationLogicNode extends LogicNode {
  type: 'computation';
  data: ComputationNodeData;
}

export interface ComputationNodeData extends LogicNodeData {
  operation: 'add' | 'subtract' | 'multiply' | 'divide' | 'concat' | 'format' | 'custom';
  expression: string;
}

// Module Rendering Configuration
export interface ModuleRendering {
  formLayout: FormLayoutConfig;
  displayLayout: DisplayLayoutConfig;
  components: CustomComponentConfig[];
}

export interface FormLayoutConfig {
  sections: FormSection[];
  columns: number;
  spacing: 'compact' | 'normal' | 'spacious';
  grouping: FieldGroupConfig[];
}

export interface FormSection {
  id: string;
  title: string;
  description?: string;
  fields: string[];
  collapsible: boolean;
  defaultExpanded: boolean;
}

export interface FieldGroupConfig {
  id: string;
  title: string;
  fields: string[];
  layout: 'horizontal' | 'vertical' | 'grid';
  columns?: number;
}

export interface DisplayLayoutConfig {
  views: DisplayView[];
  defaultView: string;
}

export interface DisplayView {
  id: string;
  name: string;
  type: 'card' | 'table' | 'list' | 'grid' | 'custom';
  fields: string[];
  template?: string;
}

export interface CustomComponentConfig {
  id: string;
  name: string;
  type: 'field' | 'display' | 'action';
  component: string;
  props: Record<string, any>;
}

// Module Validation
export interface ModuleValidation {
  rules: ModuleValidationRule[];
  crossFieldValidation: CrossFieldValidation[];
}

export interface ModuleValidationRule {
  id: string;
  field: string;
  type: 'required' | 'format' | 'range' | 'custom';
  config: Record<string, any>;
  errorMessage: string;
}

export interface CrossFieldValidation {
  id: string;
  name: string;
  fields: string[];
  condition: string;
  errorMessage: string;
  logicNodes?: ValidationLogicNode[];
}

// Module Builder State
export interface ModuleBuilderState {
  module: AssetModule;
  activeTab: 'fields' | 'logic' | 'rendering' | 'validation' | 'preview';
  selectedField?: string;
  selectedLogicNode?: string;
  isDirty: boolean;
  errors: ModuleError[];
  warnings: ModuleWarning[];
}

export interface ModuleError {
  id: string;
  type: 'field' | 'logic' | 'validation' | 'rendering';
  message: string;
  field?: string;
  node?: string;
}

export interface ModuleWarning {
  id: string;
  type: 'performance' | 'usability' | 'compatibility';
  message: string;
  suggestion?: string;
}

// Module Templates
export interface ModuleTemplate {
  id: string;
  name: string;
  description: string;
  category: ModuleCategory;
  preview: string;
  module: Partial<AssetModule>;
}

// Built-in Module Templates
export const BUILT_IN_MODULE_TEMPLATES: ModuleTemplate[] = [
  {
    id: 'location-basic',
    name: 'Basic Location',
    description: 'Address and basic location information',
    category: 'location',
    preview: '/templates/location-basic.png',
    module: {
      name: 'Basic Location',
      category: 'location',
      fields: [
        {
          id: 'address',
          name: 'address',
          label: 'Address',
          type: 'address',
          isRequired: true,
          isUnique: false,
          validation: { required: true },
          uiConfig: { width: 'full' },
          dependsOn: [],
          group: 'location',
          order: 1
        }
      ]
    }
  }
];

// Module Registry
export interface ModuleRegistry {
  modules: AssetModule[];
  templates: ModuleTemplate[];
  categories: ModuleCategory[];
}

// ============================================================================
// ASSET MODULE RUNTIME SYSTEM TYPES
// ============================================================================

// Module Runtime Status
export type ModuleRuntimeStatus =
  | 'inactive'
  | 'loading'
  | 'active'
  | 'error'
  | 'updating'
  | 'uninstalling';

// Module Execution Context
export interface ModuleExecutionContext {
  moduleId: string;
  assetId?: string;
  assetTypeId: string;
  userId: string;
  userRole: string;
  permissions: string[];
  sessionId: string;
  executionId: string;
  timestamp: string;
  environment: 'development' | 'staging' | 'production';
  metadata: Record<string, any>;
}

// Module Runtime Instance
export interface ModuleRuntimeInstance {
  id: string;
  moduleId: string;
  module: AssetModule;
  status: ModuleRuntimeStatus;
  version: string;
  loadedAt: string;
  lastUsed: string;
  usageCount: number;
  memoryUsage: number;
  executionStats: ModuleExecutionStats;
  dependencies: ModuleRuntimeInstance[];
  sandbox: ModuleSandbox;
  cache: ModuleCache;
}

// Module Execution Statistics
export interface ModuleExecutionStats {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  lastExecutionTime: number;
  errorRate: number;
  performanceMetrics: {
    fieldRenderingTime: number;
    validationTime: number;
    logicExecutionTime: number;
    dataTransformationTime: number;
  };
}

// Module Sandbox Configuration
export interface ModuleSandbox {
  id: string;
  moduleId: string;
  isolationLevel: 'strict' | 'moderate' | 'minimal';
  allowedAPIs: string[];
  blockedAPIs: string[];
  memoryLimit: number;
  executionTimeout: number;
  networkAccess: boolean;
  fileSystemAccess: boolean;
  databaseAccess: string[]; // Allowed database operations
  permissions: ModulePermissions;
}

// Module Permissions
export interface ModulePermissions {
  canReadAssets: boolean;
  canWriteAssets: boolean;
  canDeleteAssets: boolean;
  canAccessUserData: boolean;
  canSendNotifications: boolean;
  canExecuteWorkflows: boolean;
  canAccessExternalAPIs: boolean;
  allowedAssetTypes: string[];
  allowedFields: string[];
  restrictedFields: string[];
}

// Module Cache
export interface ModuleCache {
  id: string;
  moduleId: string;
  fieldCache: Map<string, any>;
  validationCache: Map<string, FieldValidationResult>;
  logicCache: Map<string, any>;
  renderingCache: Map<string, string>;
  ttl: number;
  lastCleared: string;
  hitRate: number;
  size: number;
}

// Module Event Types
export type ModuleEventType =
  | 'module.loaded'
  | 'module.activated'
  | 'module.deactivated'
  | 'module.updated'
  | 'module.uninstalled'
  | 'module.error'
  | 'field.rendered'
  | 'field.validated'
  | 'logic.executed'
  | 'data.transformed'
  | 'dependency.resolved'
  | 'cache.cleared'
  | 'sandbox.violation';

// Module Event
export interface ModuleEvent {
  id: string;
  type: ModuleEventType;
  moduleId: string;
  timestamp: string;
  context: ModuleExecutionContext;
  data: Record<string, any>;
  severity: 'info' | 'warning' | 'error' | 'critical';
  source: string;
  metadata: Record<string, any>;
}

// Module Dependency
export interface ModuleDependency {
  moduleId: string;
  version: string;
  type: 'required' | 'optional' | 'peer';
  resolved: boolean;
  resolvedVersion?: string;
  conflictsWith?: string[];
}

// Module Installation Package
export interface ModuleInstallationPackage {
  module: AssetModule;
  dependencies: ModuleDependency[];
  assets: {
    icons: string[];
    templates: string[];
    documentation: string[];
  };
  installation: {
    preInstallScript?: string;
    postInstallScript?: string;
    migrationScript?: string;
    rollbackScript?: string;
  };
  verification: {
    checksum: string;
    signature: string;
    certificate: string;
  };
}

// ============================================================================
// RUNTIME ENGINE TYPES
// ============================================================================

// Field Rendering Engine
export interface FieldRenderingEngine {
  renderField(field: ModuleField, value: any, context: ModuleExecutionContext): Promise<FieldRenderResult>;
  renderFieldGroup(fields: ModuleField[], values: Record<string, any>, context: ModuleExecutionContext): Promise<FieldGroupRenderResult>;
  validateRendering(field: ModuleField, renderResult: FieldRenderResult): Promise<boolean>;
  optimizeRendering(fields: ModuleField[]): Promise<RenderingOptimization>;
}

// Field Render Result
export interface FieldRenderResult {
  fieldId: string;
  html: string;
  css: string;
  javascript: string;
  dependencies: string[];
  metadata: {
    renderTime: number;
    cacheKey: string;
    version: string;
  };
  errors: RenderError[];
}

// Field Group Render Result
export interface FieldGroupRenderResult {
  groupId: string;
  fields: FieldRenderResult[];
  layout: string;
  totalRenderTime: number;
  cacheHits: number;
  cacheMisses: number;
}

// Render Error
export interface RenderError {
  fieldId: string;
  type: 'syntax' | 'dependency' | 'security' | 'performance';
  message: string;
  line?: number;
  column?: number;
  severity: 'warning' | 'error' | 'critical';
}

// Rendering Optimization
export interface RenderingOptimization {
  bundledCSS: string;
  bundledJS: string;
  optimizedFields: string[];
  removedDuplicates: string[];
  compressionRatio: number;
  estimatedPerformanceGain: number;
}

// Logic Execution Engine
export interface LogicExecutionEngine {
  executeLogic(logic: ModuleLogic, context: ModuleExecutionContext, inputs: Record<string, any>): Promise<LogicExecutionResult>;
  executeNode(node: LogicNode, context: ModuleExecutionContext, inputs: Record<string, any>): Promise<NodeExecutionResult>;
  validateLogic(logic: ModuleLogic): Promise<LogicValidationResult>;
  optimizeLogic(logic: ModuleLogic): Promise<LogicOptimization>;
}

// Logic Execution Result
export interface LogicExecutionResult {
  executionId: string;
  success: boolean;
  outputs: Record<string, any>;
  executionTime: number;
  nodesExecuted: number;
  nodeResults: Record<string, NodeExecutionResult>;
  errors: LogicExecutionError[];
  warnings: LogicExecutionWarning[];
  metadata: {
    startTime: string;
    endTime: string;
    memoryUsage: number;
    cacheHits: number;
  };
}

// Node Execution Result
export interface NodeExecutionResult {
  nodeId: string;
  success: boolean;
  output: any;
  executionTime: number;
  memoryUsage: number;
  error?: LogicExecutionError;
  metadata: Record<string, any>;
}

// Logic Execution Error
export interface LogicExecutionError {
  nodeId: string;
  type: 'runtime' | 'validation' | 'timeout' | 'memory' | 'security';
  message: string;
  stack?: string;
  severity: 'warning' | 'error' | 'critical';
  recoverable: boolean;
}

// Logic Execution Warning
export interface LogicExecutionWarning {
  nodeId: string;
  type: 'performance' | 'deprecated' | 'security' | 'best-practice';
  message: string;
  suggestion?: string;
}

// Logic Validation Result
export interface LogicValidationResult {
  valid: boolean;
  errors: LogicValidationError[];
  warnings: LogicValidationWarning[];
  suggestions: LogicSuggestion[];
  complexity: {
    cyclomaticComplexity: number;
    cognitiveComplexity: number;
    estimatedExecutionTime: number;
  };
}

// Logic Validation Error
export interface LogicValidationError {
  nodeId: string;
  type: 'syntax' | 'type' | 'dependency' | 'circular' | 'unreachable';
  message: string;
  severity: 'error' | 'critical';
}

// Logic Validation Warning
export interface LogicValidationWarning {
  nodeId: string;
  type: 'performance' | 'maintainability' | 'security';
  message: string;
  impact: 'low' | 'medium' | 'high';
}

// Logic Suggestion
export interface LogicSuggestion {
  nodeId: string;
  type: 'optimization' | 'refactoring' | 'best-practice';
  message: string;
  benefit: string;
  effort: 'low' | 'medium' | 'high';
}

// Logic Optimization
export interface LogicOptimization {
  optimizedLogic: ModuleLogic;
  optimizations: {
    type: 'dead-code-elimination' | 'constant-folding' | 'loop-unrolling' | 'caching';
    description: string;
    impact: number;
  }[];
  performanceGain: number;
  maintainabilityScore: number;
}

// Validation Engine
export interface ValidationEngine {
  validateField(field: ModuleField, value: any, context: ModuleExecutionContext): Promise<FieldValidationResult>;
  validateModule(module: AssetModule, data: Record<string, any>, context: ModuleExecutionContext): Promise<ModuleValidationResult>;
  validateDependencies(dependencies: ModuleDependency[]): Promise<DependencyValidationResult>;
  createValidationSchema(fields: ModuleField[]): Promise<ValidationSchema>;
}

// Field Validation Result
export interface FieldValidationResult {
  fieldId: string;
  valid: boolean;
  value: any;
  normalizedValue: any;
  errors: FieldValidationError[];
  warnings: FieldValidationWarning[];
  metadata: {
    validationTime: number;
    rulesApplied: string[];
    transformationsApplied: string[];
  };
}

// Field Validation Error
export interface FieldValidationError {
  fieldId: string;
  rule: string;
  type: 'required' | 'type' | 'format' | 'range' | 'custom' | 'dependency';
  message: string;
  value: any;
  expectedValue?: any;
  severity: 'error' | 'critical';
}

// Field Validation Warning
export interface FieldValidationWarning {
  fieldId: string;
  rule: string;
  type: 'performance' | 'best-practice' | 'deprecated';
  message: string;
  suggestion?: string;
}

// Module Validation Result
export interface ModuleValidationResult {
  moduleId: string;
  valid: boolean;
  fieldResults: Record<string, FieldValidationResult>;
  crossFieldValidation: CrossFieldValidationResult[];
  errors: ModuleValidationError[];
  warnings: ModuleValidationWarning[];
  metadata: {
    totalValidationTime: number;
    fieldsValidated: number;
    rulesExecuted: number;
  };
}

// Cross Field Validation Result
export interface CrossFieldValidationResult {
  ruleId: string;
  fields: string[];
  valid: boolean;
  message?: string;
  severity: 'warning' | 'error';
}

// Module Validation Error
export interface ModuleValidationError {
  type: 'structure' | 'dependency' | 'security' | 'performance';
  message: string;
  affectedFields: string[];
  severity: 'error' | 'critical';
}

// Module Validation Warning
export interface ModuleValidationWarning {
  type: 'performance' | 'maintainability' | 'best-practice';
  message: string;
  affectedFields: string[];
  impact: 'low' | 'medium' | 'high';
}

// Dependency Validation Result
export interface DependencyValidationResult {
  valid: boolean;
  resolvedDependencies: ModuleDependency[];
  unresolvedDependencies: ModuleDependency[];
  conflicts: DependencyConflict[];
  warnings: DependencyWarning[];
}

// Dependency Conflict
export interface DependencyConflict {
  moduleId: string;
  conflictingModules: string[];
  type: 'version' | 'api' | 'resource';
  severity: 'warning' | 'error' | 'critical';
  resolution?: string;
}

// Dependency Warning
export interface DependencyWarning {
  moduleId: string;
  type: 'deprecated' | 'security' | 'performance';
  message: string;
  recommendation?: string;
}

// Validation Schema
export interface ValidationSchema {
  id: string;
  moduleId: string;
  fields: Record<string, FieldValidationSchema>;
  crossFieldRules: CrossFieldValidationRule[];
  metadata: {
    version: string;
    createdAt: string;
    updatedAt: string;
  };
}

// Field Validation Schema
export interface FieldValidationSchema {
  fieldId: string;
  type: ModuleFieldType;
  required: boolean;
  rules: ValidationRule[];
  transformations: ValidationTransformation[];
  dependencies: string[];
}

// Validation Rule
export interface ValidationRule {
  id: string;
  type: string;
  parameters: Record<string, any>;
  message: string;
  severity: 'warning' | 'error';
  condition?: string;
}

// Validation Transformation
export interface ValidationTransformation {
  id: string;
  type: 'normalize' | 'format' | 'convert' | 'sanitize';
  parameters: Record<string, any>;
  order: number;
}

// Cross Field Validation Rule
export interface CrossFieldValidationRule {
  id: string;
  name: string;
  fields: string[];
  condition: string;
  message: string;
  severity: 'warning' | 'error';
}

// ============================================================================
// DATA TRANSFORMATION PIPELINE TYPES
// ============================================================================

// Data Transformation Pipeline
export interface DataTransformationPipeline {
  transformData(data: Record<string, any>, transformations: DataTransformation[], context: ModuleExecutionContext): Promise<DataTransformationResult>;
  validateTransformation(transformation: DataTransformation): Promise<TransformationValidationResult>;
  optimizeTransformations(transformations: DataTransformation[]): Promise<TransformationOptimization>;
  createTransformationChain(transformations: DataTransformation[]): Promise<TransformationChain>;
}

// Data Transformation
export interface DataTransformation {
  id: string;
  name: string;
  type: 'map' | 'filter' | 'reduce' | 'validate' | 'normalize' | 'aggregate' | 'join' | 'split' | 'custom';
  inputSchema: TransformationSchema;
  outputSchema: TransformationSchema;
  configuration: Record<string, any>;
  script?: string;
  dependencies: string[];
  order: number;
  condition?: string;
  metadata: {
    version: string;
    author: string;
    description: string;
    tags: string[];
  };
}

// Transformation Schema
export interface TransformationSchema {
  type: 'object' | 'array' | 'string' | 'number' | 'boolean' | 'any';
  properties?: Record<string, TransformationSchema>;
  items?: TransformationSchema;
  required?: string[];
  format?: string;
  pattern?: string;
  minimum?: number;
  maximum?: number;
}

// Data Transformation Result
export interface DataTransformationResult {
  success: boolean;
  inputData: Record<string, any>;
  outputData: Record<string, any>;
  transformationsApplied: string[];
  executionTime: number;
  errors: TransformationError[];
  warnings: TransformationWarning[];
  metadata: {
    dataSize: number;
    transformationCount: number;
    cacheHits: number;
    memoryUsage: number;
  };
}

// Transformation Error
export interface TransformationError {
  transformationId: string;
  type: 'syntax' | 'runtime' | 'validation' | 'dependency';
  message: string;
  data?: any;
  severity: 'error' | 'critical';
}

// Transformation Warning
export interface TransformationWarning {
  transformationId: string;
  type: 'performance' | 'data-quality' | 'best-practice';
  message: string;
  suggestion?: string;
}

// Transformation Validation Result
export interface TransformationValidationResult {
  valid: boolean;
  errors: TransformationValidationError[];
  warnings: TransformationValidationWarning[];
  suggestions: TransformationSuggestion[];
}

// Transformation Validation Error
export interface TransformationValidationError {
  type: 'schema' | 'syntax' | 'dependency' | 'security';
  message: string;
  severity: 'error' | 'critical';
}

// Transformation Validation Warning
export interface TransformationValidationWarning {
  type: 'performance' | 'maintainability' | 'data-quality';
  message: string;
  impact: 'low' | 'medium' | 'high';
}

// Transformation Suggestion
export interface TransformationSuggestion {
  type: 'optimization' | 'refactoring' | 'best-practice';
  message: string;
  benefit: string;
  effort: 'low' | 'medium' | 'high';
}

// Transformation Optimization
export interface TransformationOptimization {
  optimizedTransformations: DataTransformation[];
  optimizations: {
    type: 'merge' | 'reorder' | 'cache' | 'parallelize';
    description: string;
    impact: number;
  }[];
  performanceGain: number;
  memoryReduction: number;
}

// Transformation Chain
export interface TransformationChain {
  id: string;
  transformations: DataTransformation[];
  executionPlan: TransformationExecutionStep[];
  parallelizable: boolean;
  estimatedExecutionTime: number;
  memoryRequirement: number;
}

// Transformation Execution Step
export interface TransformationExecutionStep {
  stepId: string;
  transformationIds: string[];
  dependencies: string[];
  canRunInParallel: boolean;
  estimatedTime: number;
}

// ============================================================================
// INTEGRATION LAYER TYPES
// ============================================================================

// Module Integration Manager
export interface ModuleIntegrationManager {
  integrateWithAssetType(moduleId: string, assetTypeId: string, configuration: AssetTypeIntegrationConfig): Promise<IntegrationResult>;
  integrateWithFormBuilder(moduleId: string, formId: string, configuration: FormBuilderIntegrationConfig): Promise<IntegrationResult>;
  integrateWithAPI(moduleId: string, apiConfig: APIIntegrationConfig): Promise<IntegrationResult>;
  setupEventHandlers(moduleId: string, eventConfig: EventIntegrationConfig): Promise<IntegrationResult>;
  removeIntegration(integrationId: string): Promise<boolean>;
}

// Asset Type Integration Config
export interface AssetTypeIntegrationConfig {
  assetTypeId: string;
  fieldMappings: FieldMapping[];
  lifecycleHooks: LifecycleHook[];
  validationRules: string[];
  permissions: ModulePermissions;
  priority: number;
}

// Field Mapping
export interface FieldMapping {
  moduleFieldId: string;
  assetTypeFieldId: string;
  mappingType: 'direct' | 'computed' | 'conditional';
  transformation?: string;
  condition?: string;
  defaultValue?: any;
}

// Lifecycle Hook
export interface LifecycleHook {
  event: 'create' | 'update' | 'delete' | 'validate' | 'render';
  handler: string;
  priority: number;
  condition?: string;
}

// Form Builder Integration Config
export interface FormBuilderIntegrationConfig {
  formId: string;
  sectionId?: string;
  position: number;
  fieldOverrides: Record<string, any>;
  layoutConfig: FormLayoutConfig;
  validationConfig: FormValidationConfig;
}

// Form Layout Config
export interface FormLayoutConfig {
  columns: number;
  spacing: 'compact' | 'normal' | 'spacious';
  groupingStyle: 'none' | 'category' | 'custom';
  responsive: boolean;
}

// Form Validation Config
export interface FormValidationConfig {
  validateOnChange: boolean;
  validateOnBlur: boolean;
  showInlineErrors: boolean;
  debounceMs: number;
}

// API Integration Config
export interface APIIntegrationConfig {
  endpoints: APIEndpoint[];
  authentication: APIAuthentication;
  rateLimiting: APIRateLimiting;
  caching: APICaching;
  errorHandling: APIErrorHandling;
}

// API Endpoint
export interface APIEndpoint {
  id: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  handler: string;
  middleware: string[];
  validation: APIValidation;
  documentation: string;
}

// API Authentication
export interface APIAuthentication {
  required: boolean;
  type: 'bearer' | 'api-key' | 'oauth' | 'custom';
  permissions: string[];
}

// API Rate Limiting
export interface APIRateLimiting {
  enabled: boolean;
  requestsPerMinute: number;
  burstLimit: number;
  keyGenerator: string;
}

// API Caching
export interface APICaching {
  enabled: boolean;
  ttl: number;
  keyGenerator: string;
  invalidationRules: string[];
}

// API Error Handling
export interface APIErrorHandling {
  retryAttempts: number;
  retryDelay: number;
  fallbackResponse?: any;
  logErrors: boolean;
}

// API Validation
export interface APIValidation {
  requestSchema?: Record<string, any>;
  responseSchema?: Record<string, any>;
  customValidators: string[];
}

// Event Integration Config
export interface EventIntegrationConfig {
  eventHandlers: EventHandler[];
  eventEmitters: EventEmitter[];
  eventFilters: EventFilter[];
}

// Event Handler
export interface EventHandler {
  id: string;
  eventType: ModuleEventType;
  handler: string;
  priority: number;
  condition?: string;
  async: boolean;
}

// Event Emitter
export interface EventEmitter {
  id: string;
  eventType: ModuleEventType;
  trigger: string;
  data: Record<string, any>;
  condition?: string;
}

// Event Filter
export interface EventFilter {
  id: string;
  eventType: ModuleEventType;
  condition: string;
  action: 'allow' | 'deny' | 'modify';
  modification?: Record<string, any>;
}

// Integration Result
export interface IntegrationResult {
  success: boolean;
  integrationId: string;
  errors: IntegrationError[];
  warnings: IntegrationWarning[];
  metadata: {
    integrationTime: number;
    resourcesCreated: string[];
    configurationApplied: Record<string, any>;
  };
}

// Integration Error
export interface IntegrationError {
  type: 'configuration' | 'dependency' | 'permission' | 'resource';
  message: string;
  details?: Record<string, any>;
  severity: 'error' | 'critical';
}

// Integration Warning
export interface IntegrationWarning {
  type: 'performance' | 'compatibility' | 'best-practice';
  message: string;
  suggestion?: string;
  impact: 'low' | 'medium' | 'high';
}

// ============================================================================
// PERFORMANCE OPTIMIZATION TYPES
// ============================================================================

// Performance Optimizer
export interface PerformanceOptimizer {
  optimizeModule(moduleId: string): Promise<ModuleOptimizationResult>;
  optimizeFieldRendering(fields: ModuleField[]): Promise<RenderingOptimization>;
  optimizeLogicExecution(logic: ModuleLogic): Promise<LogicOptimization>;
  optimizeDataTransformation(transformations: DataTransformation[]): Promise<TransformationOptimization>;
  analyzePerformance(moduleId: string): Promise<PerformanceAnalysis>;
}

// Module Optimization Result
export interface ModuleOptimizationResult {
  moduleId: string;
  optimizations: ModuleOptimization[];
  performanceGain: number;
  memoryReduction: number;
  loadTimeReduction: number;
  executionTimeReduction: number;
  recommendations: OptimizationRecommendation[];
}

// Module Optimization
export interface ModuleOptimization {
  type: 'caching' | 'lazy-loading' | 'bundling' | 'compression' | 'minification' | 'tree-shaking';
  description: string;
  impact: number;
  applied: boolean;
  reason?: string;
}

// Optimization Recommendation
export interface OptimizationRecommendation {
  type: 'critical' | 'high' | 'medium' | 'low';
  category: 'performance' | 'memory' | 'network' | 'storage';
  description: string;
  benefit: string;
  effort: 'low' | 'medium' | 'high';
  implementation: string;
}

// Performance Analysis
export interface PerformanceAnalysis {
  moduleId: string;
  metrics: PerformanceMetrics;
  bottlenecks: PerformanceBottleneck[];
  recommendations: PerformanceRecommendation[];
  trends: PerformanceTrend[];
  comparison: PerformanceComparison;
}

// Performance Metrics
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  validationTime: number;
  logicExecutionTime: number;
  dataTransformationTime: number;
  memoryUsage: number;
  cacheHitRate: number;
  errorRate: number;
  throughput: number;
  latency: number;
}

// Performance Bottleneck
export interface PerformanceBottleneck {
  type: 'cpu' | 'memory' | 'io' | 'network' | 'cache';
  location: string;
  impact: number;
  description: string;
  suggestion: string;
}

// Performance Recommendation
export interface PerformanceRecommendation {
  priority: 'critical' | 'high' | 'medium' | 'low';
  category: 'optimization' | 'refactoring' | 'infrastructure';
  description: string;
  expectedGain: number;
  effort: 'low' | 'medium' | 'high';
  implementation: string;
}

// Performance Trend
export interface PerformanceTrend {
  metric: string;
  trend: 'improving' | 'degrading' | 'stable';
  change: number;
  period: string;
  significance: 'high' | 'medium' | 'low';
}

// Performance Comparison
export interface PerformanceComparison {
  baseline: PerformanceMetrics;
  current: PerformanceMetrics;
  improvement: Record<string, number>;
  regression: Record<string, number>;
}

// ============================================================================
// MODULE REGISTRY CORE TYPES
// ============================================================================

// Enhanced Module Registry
export interface EnhancedModuleRegistry {
  // Module Management
  registerModule(installationPackage: ModuleInstallationPackage): Promise<ModuleRegistrationResult>;
  unregisterModule(moduleId: string): Promise<boolean>;
  updateModule(moduleId: string, updates: Partial<AssetModule>): Promise<ModuleUpdateResult>;

  // Module Discovery
  findModules(criteria: ModuleSearchCriteria): Promise<AssetModule[]>;
  getModule(moduleId: string): Promise<AssetModule | null>;
  getModuleVersions(moduleId: string): Promise<ModuleVersion[]>;

  // Dependency Management
  resolveDependencies(moduleId: string): Promise<DependencyResolutionResult>;
  checkCompatibility(moduleId: string, targetVersion: string): Promise<CompatibilityResult>;

  // Lifecycle Management
  activateModule(moduleId: string): Promise<ModuleActivationResult>;
  deactivateModule(moduleId: string): Promise<boolean>;

  // Registry Operations
  getRegistryStats(): Promise<RegistryStats>;
  validateRegistry(): Promise<RegistryValidationResult>;
  cleanupRegistry(): Promise<RegistryCleanupResult>;
}

// Module Registration Result
export interface ModuleRegistrationResult {
  success: boolean;
  moduleId: string;
  version: string;
  errors: RegistrationError[];
  warnings: RegistrationWarning[];
  dependencies: ModuleDependency[];
  metadata: {
    registrationTime: number;
    resourcesCreated: string[];
    permissions: string[];
  };
}

// Registration Error
export interface RegistrationError {
  type: 'validation' | 'dependency' | 'permission' | 'resource' | 'security';
  message: string;
  details?: Record<string, any>;
  severity: 'error' | 'critical';
}

// Registration Warning
export interface RegistrationWarning {
  type: 'compatibility' | 'performance' | 'best-practice' | 'deprecated';
  message: string;
  suggestion?: string;
  impact: 'low' | 'medium' | 'high';
}

// Module Update Result
export interface ModuleUpdateResult {
  success: boolean;
  moduleId: string;
  oldVersion: string;
  newVersion: string;
  changes: ModuleChange[];
  migrationRequired: boolean;
  rollbackAvailable: boolean;
}

// Module Change
export interface ModuleChange {
  type: 'added' | 'modified' | 'removed';
  component: 'field' | 'logic' | 'rendering' | 'validation' | 'metadata';
  description: string;
  impact: 'breaking' | 'compatible' | 'enhancement';
}

// Module Search Criteria
export interface ModuleSearchCriteria {
  name?: string;
  category?: ModuleCategory;
  tags?: string[];
  author?: string;
  version?: string;
  isActive?: boolean;
  compatibleAssetTypes?: string[];
  requiredPermissions?: string[];
  sortBy?: 'name' | 'version' | 'usageCount' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// Module Version
export interface ModuleVersion {
  version: string;
  releaseDate: string;
  changelog: string;
  isStable: boolean;
  isDeprecated: boolean;
  compatibilityInfo: CompatibilityInfo;
}

// Compatibility Info
export interface CompatibilityInfo {
  minSystemVersion: string;
  maxSystemVersion?: string;
  requiredFeatures: string[];
  deprecatedFeatures: string[];
  breakingChanges: string[];
}

// Dependency Resolution Result
export interface DependencyResolutionResult {
  success: boolean;
  resolvedDependencies: ResolvedDependency[];
  unresolvedDependencies: ModuleDependency[];
  conflicts: DependencyConflict[];
  resolutionPlan: ResolutionStep[];
}

// Resolved Dependency
export interface ResolvedDependency {
  moduleId: string;
  version: string;
  source: 'registry' | 'local' | 'remote';
  path: string;
  checksum: string;
}

// Resolution Step
export interface ResolutionStep {
  stepId: string;
  action: 'install' | 'update' | 'remove' | 'configure';
  moduleId: string;
  version: string;
  dependencies: string[];
  order: number;
}

// Compatibility Result
export interface CompatibilityResult {
  compatible: boolean;
  issues: CompatibilityIssue[];
  recommendations: CompatibilityRecommendation[];
  migrationRequired: boolean;
  migrationPath?: MigrationStep[];
}

// Compatibility Issue
export interface CompatibilityIssue {
  type: 'version' | 'api' | 'dependency' | 'feature';
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  affectedComponents: string[];
  resolution?: string;
}

// Compatibility Recommendation
export interface CompatibilityRecommendation {
  type: 'upgrade' | 'downgrade' | 'alternative' | 'workaround';
  description: string;
  effort: 'low' | 'medium' | 'high';
  risk: 'low' | 'medium' | 'high';
}

// Migration Step
export interface MigrationStep {
  stepId: string;
  description: string;
  type: 'automatic' | 'manual' | 'assisted';
  script?: string;
  validation: string;
  rollback?: string;
}

// Module Activation Result
export interface ModuleActivationResult {
  success: boolean;
  moduleId: string;
  activationTime: number;
  errors: ActivationError[];
  warnings: ActivationWarning[];
  resourcesInitialized: string[];
  integrations: string[];
}

// Activation Error
export interface ActivationError {
  type: 'dependency' | 'permission' | 'resource' | 'configuration';
  message: string;
  details?: Record<string, any>;
  severity: 'error' | 'critical';
}

// Activation Warning
export interface ActivationWarning {
  type: 'performance' | 'compatibility' | 'configuration';
  message: string;
  suggestion?: string;
  impact: 'low' | 'medium' | 'high';
}

// Registry Stats
export interface RegistryStats {
  totalModules: number;
  activeModules: number;
  inactiveModules: number;
  modulesByCategory: Record<ModuleCategory, number>;
  totalUsage: number;
  averageRating: number;
  storageUsed: number;
  lastUpdated: string;
}

// Registry Validation Result
export interface RegistryValidationResult {
  valid: boolean;
  errors: RegistryValidationError[];
  warnings: RegistryValidationWarning[];
  corruptedModules: string[];
  orphanedDependencies: string[];
  duplicateModules: string[];
}

// Registry Validation Error
export interface RegistryValidationError {
  type: 'corruption' | 'missing' | 'invalid' | 'security';
  moduleId?: string;
  message: string;
  severity: 'error' | 'critical';
}

// Registry Validation Warning
export interface RegistryValidationWarning {
  type: 'outdated' | 'unused' | 'deprecated' | 'performance';
  moduleId?: string;
  message: string;
  suggestion?: string;
}

// Registry Cleanup Result
export interface RegistryCleanupResult {
  success: boolean;
  removedModules: string[];
  freedSpace: number;
  optimizedModules: string[];
  errors: string[];
  summary: {
    totalCleaned: number;
    spaceFreed: number;
    timeElapsed: number;
  };
}

// Module Installation
export interface ModuleInstallation {
  moduleId: string;
  assetTypeId: string;
  configuration: Record<string, any>;
  isActive: boolean;
  installedAt: string;
  installedBy: string;
}
