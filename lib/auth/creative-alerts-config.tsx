import { 
  CheckCircle, 
  XCircle, 
  Sparkles, 
  Zap, 
  Shield, 
  AlertTriangle,
  Rocket,
  Heart,
  Star,
  Trophy,
  Mail,
  Key,
  UserPlus,
  RefreshCw,
  Lock,
  Unlock,
  PartyPopper,
  Coffee,
  Lightbulb,
  Target
} from "lucide-react";

export type AlertType = 'success' | 'error' | 'info' | 'warning';
export type AuthContext = 'login' | 'register' | 'forgot-password' | 'reset-password' | 'verify-email' | 'logout';

export interface CreativeAlertMessage {
  title: string;
  description: string;
  icon: any;
  theme: string;
  hint?: string;
  duration?: number;
}

export interface AlertConfig {
  [key: string]: {
    success: CreativeAlertMessage[];
    error: CreativeAlertMessage[];
    info?: CreativeAlertMessage[];
    warning?: CreativeAlertMessage[];
  };
}

export const CREATIVE_ALERTS_CONFIG: AlertConfig = {
  login: {
    success: [
      {
        title: "🎉 Welcome back, Champion!",
        description: "Your digital fortress awaits your command!",
        icon: Trophy,
        theme: "celebration",
        duration: 4000
      },
      {
        title: "⚡ Lightning Login!",
        description: "Faster than a speeding bullet! Dashboard loading...",
        icon: Zap,
        theme: "speed",
        duration: 3500
      },
      {
        title: "🚀 Mission Control Activated!",
        description: "All systems green. Preparing for asset management...",
        icon: Rocket,
        theme: "space",
        duration: 4000
      },
      {
        title: "🛡️ Access Granted!",
        description: "Security protocols verified. Welcome to the command center!",
        icon: Shield,
        theme: "security",
        duration: 4000
      },
      {
        title: "✨ Magic Happens!",
        description: "Abracadabra! Your workspace is materializing...",
        icon: Sparkles,
        theme: "magic",
        duration: 4000
      }
    ],
    error: [
      {
        title: "🔐 Access Denied!",
        description: "Hmm, those credentials don't match our records. Double-check and try again!",
        icon: XCircle,
        theme: "security",
        hint: "💡 Tip: Check your caps lock and try typing slowly",
        duration: 6000
      },
      {
        title: "🚫 Authentication Failed!",
        description: "Oops! It seems like your password is playing hide and seek.",
        icon: AlertTriangle,
        theme: "warning",
        hint: "💡 Tip: Try the 'Forgot Password' link if you're stuck",
        duration: 6000
      },
      {
        title: "⚠️ Login Unsuccessful!",
        description: "Close, but not quite there yet. Your account is waiting for you!",
        icon: XCircle,
        theme: "retry",
        hint: "💡 Tip: Make sure you're using the correct email format",
        duration: 6000
      },
      {
        title: "🔒 Security Check Failed!",
        description: "The digital bouncer says 'Not today!' Let's try those credentials again.",
        icon: Shield,
        theme: "security",
        hint: "💡 Tip: Password must be at least 8 characters long",
        duration: 6000
      }
    ]
  },

  register: {
    success: [
      {
        title: "🎊 Welcome to the Family!",
        description: "Your account has been created successfully! Time to explore!",
        icon: PartyPopper,
        theme: "celebration",
        duration: 5000
      },
      {
        title: "🌟 Account Created!",
        description: "You're now part of our amazing community. Let's get started!",
        icon: Star,
        theme: "achievement",
        duration: 4500
      },
      {
        title: "🚀 Ready for Takeoff!",
        description: "Your journey begins now. Buckle up for an amazing experience!",
        icon: Rocket,
        theme: "journey",
        duration: 4500
      }
    ],
    error: [
      {
        title: "📧 Email Already Exists!",
        description: "Looks like you're already one of us! Try logging in instead.",
        icon: Mail,
        theme: "info",
        hint: "💡 Tip: Use the login form or try password reset if you forgot your password",
        duration: 6000
      },
      {
        title: "🔑 Password Too Weak!",
        description: "Your password needs to be stronger to protect your account.",
        icon: Key,
        theme: "security",
        hint: "💡 Tip: Use at least 8 characters with numbers and special characters",
        duration: 6000
      },
      {
        title: "⚠️ Registration Failed!",
        description: "Something went wrong during registration. Let's try again!",
        icon: AlertTriangle,
        theme: "retry",
        hint: "💡 Tip: Check all fields and ensure your email is valid",
        duration: 6000
      }
    ]
  },

  "forgot-password": {
    success: [
      {
        title: "📬 Reset Link Sent!",
        description: "Check your email for password reset instructions. It should arrive shortly!",
        icon: Mail,
        theme: "mail",
        duration: 5000
      },
      {
        title: "🔄 Recovery Initiated!",
        description: "Password recovery is in progress. Check your inbox!",
        icon: RefreshCw,
        theme: "process",
        duration: 4500
      }
    ],
    error: [
      {
        title: "📧 Email Not Found!",
        description: "We couldn't find an account with that email address.",
        icon: AlertTriangle,
        theme: "warning",
        hint: "💡 Tip: Double-check the email or try creating a new account",
        duration: 6000
      },
      {
        title: "⚠️ Request Failed!",
        description: "Unable to process your password reset request right now.",
        icon: XCircle,
        theme: "error",
        hint: "💡 Tip: Try again in a few minutes or contact support",
        duration: 6000
      }
    ]
  },

  "reset-password": {
    success: [
      {
        title: "🔓 Password Updated!",
        description: "Your password has been successfully changed. You can now log in!",
        icon: Unlock,
        theme: "success",
        duration: 4000
      },
      {
        title: "🛡️ Security Restored!",
        description: "Your account is now secure with your new password!",
        icon: Shield,
        theme: "security",
        duration: 4000
      }
    ],
    error: [
      {
        title: "🔗 Invalid Reset Link!",
        description: "This reset link has expired or is invalid.",
        icon: AlertTriangle,
        theme: "warning",
        hint: "💡 Tip: Request a new password reset link",
        duration: 6000
      },
      {
        title: "🔑 Password Update Failed!",
        description: "Unable to update your password. Please try again.",
        icon: XCircle,
        theme: "error",
        hint: "💡 Tip: Ensure your new password meets all requirements",
        duration: 6000
      }
    ]
  },

  "verify-email": {
    success: [
      {
        title: "✅ Email Verified!",
        description: "Your email has been successfully verified. Welcome aboard!",
        icon: CheckCircle,
        theme: "verification",
        duration: 4000
      }
    ],
    error: [
      {
        title: "❌ Verification Failed!",
        description: "Unable to verify your email address.",
        icon: XCircle,
        theme: "error",
        hint: "💡 Tip: The link may have expired. Request a new verification email",
        duration: 6000
      }
    ]
  },

  logout: {
    success: [
      {
        title: "👋 See You Soon!",
        description: "You've been safely logged out. Thanks for using our platform!",
        icon: Coffee,
        theme: "farewell",
        duration: 3000
      },
      {
        title: "🔒 Session Ended!",
        description: "Your session has been securely terminated. Come back anytime!",
        icon: Lock,
        theme: "security",
        duration: 3000
      }
    ],
    error: [
      {
        title: "⚠️ Logout Issue!",
        description: "There was a problem logging you out, but your session is now cleared.",
        icon: AlertTriangle,
        theme: "warning",
        duration: 4000
      }
    ]
  }
};

// Helper function to get random message from context and type
export const getRandomAlertMessage = (context: AuthContext, type: AlertType): CreativeAlertMessage | null => {
  const contextConfig = CREATIVE_ALERTS_CONFIG[context];
  if (!contextConfig || !contextConfig[type]) {
    return null;
  }
  
  const messages = contextConfig[type];
  return messages[Math.floor(Math.random() * messages.length)];
};

// Helper function to get specific error message based on error content
export const getContextualErrorMessage = (context: AuthContext, error: any): CreativeAlertMessage => {
  const defaultMessage = getRandomAlertMessage(context, 'error');
  if (!defaultMessage) {
    return {
      title: "❌ Something went wrong!",
      description: "An unexpected error occurred. Please try again.",
      icon: XCircle,
      theme: "error",
      duration: 5000
    };
  }

  // Customize message based on error content
  const errorMessage = error?.message?.toLowerCase() || '';
  
  if (errorMessage.includes('email')) {
    return {
      ...defaultMessage,
      description: "That email address doesn't look quite right. Let's fix that!",
      hint: "💡 Tip: Make sure your email follows the format: <EMAIL>"
    };
  } else if (errorMessage.includes('password')) {
    return {
      ...defaultMessage,
      description: "Password mismatch detected! Your account is secure, but we need the right key.",
      hint: "💡 Tip: Try typing your password in a text editor first, then copy-paste it"
    };
  } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
    return {
      ...defaultMessage,
      description: "Connection hiccup detected! Our servers are playing hard to get.",
      hint: "💡 Tip: Check your internet connection and try again in a moment"
    };
  }

  return defaultMessage;
};
