import { 
  User, 
  Settings, 
  LogOut, 
  Shield, 
  Bell, 
  HelpCircle, 
  Palette,
  Code,
  Layers,
  CreditCard,
  UserCog,
  Activity,
  Mail,
  Phone,
  Building,
  Calendar,
  FileText,
  Download,
  Upload,
  Key,
  Globe,
  Smartphone,
  Monitor,
  Database,
  Cloud,
  Zap,
  BarChart3,
  MessageSquare,
  Star,
  Heart,
  Bookmark,
  Archive,
  Trash2,
  Edit,
  Copy,
  Share,
  ExternalLink,
  RefreshCw,
  Search,
  Filter,
  SortAsc,
  MoreHorizontal
} from "lucide-react";

export interface UserActionConfig {
  id: string;
  label: string;
  icon: any;
  href?: string;
  onClick?: () => void;
  variant?: 'default' | 'destructive';
  separator?: boolean;
  badge?: string | number;
  disabled?: boolean;
  description?: string;
  shortcut?: string;
  category?: string;
  requiredRole?: string[];
  requiredPermission?: string[];
  showInSidebar?: boolean;
  showInHeader?: boolean;
  showInProfile?: boolean;
  priority?: number;
}

export interface UserActionsGroup {
  id: string;
  label: string;
  actions: UserActionConfig[];
  order: number;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

// Core user actions that are available across the application
export const CORE_USER_ACTIONS: UserActionConfig[] = [
  // Profile & Account
  {
    id: 'profile',
    label: 'Profile',
    icon: User,
    href: '/admin/profile',
    description: 'View and edit your profile information',
    shortcut: 'Ctrl+P',
    category: 'account',
    showInSidebar: true,
    showInHeader: true,
    showInProfile: true,
    priority: 100
  },
  {
    id: 'account-settings',
    label: 'Account Settings',
    icon: UserCog,
    href: '/admin/account',
    description: 'Manage your account preferences',
    category: 'account',
    showInSidebar: true,
    showInProfile: true,
    priority: 90
  },
  {
    id: 'preferences',
    label: 'Preferences',
    icon: Settings,
    href: '/admin/preferences',
    description: 'Customize your application experience',
    category: 'account',
    showInSidebar: true,
    showInProfile: true,
    priority: 85
  },

  // Security & Privacy
  {
    id: 'security',
    label: 'Security',
    icon: Shield,
    href: '/admin/security',
    description: 'Manage your account security settings',
    category: 'security',
    showInSidebar: true,
    showInProfile: true,
    priority: 80
  },
  {
    id: 'change-password',
    label: 'Change Password',
    icon: Key,
    href: '/admin/security/password',
    description: 'Update your account password',
    category: 'security',
    showInProfile: true,
    priority: 75
  },
  {
    id: 'two-factor',
    label: 'Two-Factor Authentication',
    icon: Smartphone,
    href: '/admin/security/2fa',
    description: 'Enable or disable two-factor authentication',
    category: 'security',
    showInProfile: true,
    priority: 70
  },

  // Notifications & Communication
  {
    id: 'notifications',
    label: 'Notifications',
    icon: Bell,
    href: '/admin/notifications',
    description: 'View and manage your notifications',
    category: 'communication',
    showInSidebar: true,
    showInHeader: true,
    priority: 65
  },
  {
    id: 'messages',
    label: 'Messages',
    icon: MessageSquare,
    href: '/admin/messages',
    description: 'View your messages and conversations',
    category: 'communication',
    showInHeader: true,
    priority: 60
  },

  // Development & Tools (Admin/Developer specific)
  {
    id: 'asset-module-editor',
    label: 'Asset Module Editor',
    icon: Code,
    href: '/module-editor/new',
    description: 'Create and edit asset modules',
    badge: 'New',
    category: 'development',
    requiredRole: ['admin', 'developer'],
    showInSidebar: true,
    showInHeader: true,
    priority: 55
  },
  {
    id: 'form-builder',
    label: 'Form Builder',
    icon: Layers,
    href: '/admin/settings/form-builder',
    description: 'Build custom forms and workflows',
    category: 'development',
    requiredRole: ['admin', 'manager'],
    showInSidebar: true,
    priority: 50
  },
  {
    id: 'api-keys',
    label: 'API Keys',
    icon: Database,
    href: '/admin/api-keys',
    description: 'Manage your API keys and integrations',
    category: 'development',
    requiredRole: ['admin', 'developer'],
    showInProfile: true,
    priority: 45
  },

  // Billing & Subscription
  {
    id: 'billing',
    label: 'Billing',
    icon: CreditCard,
    href: '/admin/billing',
    description: 'Manage billing and subscription',
    category: 'billing',
    showInSidebar: true,
    showInProfile: true,
    priority: 40
  },
  {
    id: 'invoices',
    label: 'Invoices',
    icon: FileText,
    href: '/admin/billing/invoices',
    description: 'View and download invoices',
    category: 'billing',
    showInProfile: true,
    priority: 35
  },
  {
    id: 'usage',
    label: 'Usage & Analytics',
    icon: BarChart3,
    href: '/admin/usage',
    description: 'View your usage statistics',
    category: 'billing',
    showInProfile: true,
    priority: 30
  },

  // Activity & History
  {
    id: 'activity',
    label: 'Activity Log',
    icon: Activity,
    href: '/admin/activity',
    description: 'View your recent activity and changes',
    category: 'activity',
    showInSidebar: true,
    showInProfile: true,
    priority: 25
  },
  {
    id: 'audit-log',
    label: 'Audit Log',
    icon: FileText,
    href: '/admin/audit',
    description: 'View detailed audit logs',
    category: 'activity',
    requiredRole: ['admin'],
    showInProfile: true,
    priority: 20
  },

  // Support & Help
  {
    id: 'help',
    label: 'Help & Support',
    icon: HelpCircle,
    href: '/help',
    description: 'Get help and access documentation',
    category: 'support',
    showInSidebar: true,
    showInHeader: true,
    priority: 15
  },
  {
    id: 'documentation',
    label: 'Documentation',
    icon: FileText,
    href: '/docs',
    description: 'Access user documentation',
    category: 'support',
    priority: 10
  },
  {
    id: 'contact-support',
    label: 'Contact Support',
    icon: Mail,
    href: '/support/contact',
    description: 'Contact our support team',
    category: 'support',
    priority: 5
  },

  // System Actions
  {
    id: 'logout',
    label: 'Sign out',
    icon: LogOut,
    variant: 'destructive' as const,
    description: 'Sign out of your account',
    shortcut: 'Ctrl+Shift+Q',
    category: 'system',
    showInSidebar: true,
    showInHeader: true,
    showInProfile: true,
    priority: 0
  }
];

// Grouped actions for better organization
export const USER_ACTIONS_GROUPS: UserActionsGroup[] = [
  {
    id: 'account',
    label: 'Account',
    order: 1,
    actions: CORE_USER_ACTIONS.filter(action => action.category === 'account')
  },
  {
    id: 'security',
    label: 'Security & Privacy',
    order: 2,
    actions: CORE_USER_ACTIONS.filter(action => action.category === 'security')
  },
  {
    id: 'communication',
    label: 'Communication',
    order: 3,
    actions: CORE_USER_ACTIONS.filter(action => action.category === 'communication')
  },
  {
    id: 'development',
    label: 'Development Tools',
    order: 4,
    actions: CORE_USER_ACTIONS.filter(action => action.category === 'development')
  },
  {
    id: 'billing',
    label: 'Billing & Usage',
    order: 5,
    actions: CORE_USER_ACTIONS.filter(action => action.category === 'billing')
  },
  {
    id: 'activity',
    label: 'Activity & Logs',
    order: 6,
    actions: CORE_USER_ACTIONS.filter(action => action.category === 'activity')
  },
  {
    id: 'support',
    label: 'Help & Support',
    order: 7,
    actions: CORE_USER_ACTIONS.filter(action => action.category === 'support')
  },
  {
    id: 'system',
    label: 'System',
    order: 8,
    actions: CORE_USER_ACTIONS.filter(action => action.category === 'system')
  }
];

// Context-specific action configurations
export const SIDEBAR_USER_ACTIONS = CORE_USER_ACTIONS
  .filter(action => action.showInSidebar)
  .sort((a, b) => (b.priority || 0) - (a.priority || 0));

export const HEADER_USER_ACTIONS = CORE_USER_ACTIONS
  .filter(action => action.showInHeader)
  .sort((a, b) => (b.priority || 0) - (a.priority || 0));

export const PROFILE_USER_ACTIONS = CORE_USER_ACTIONS
  .filter(action => action.showInProfile)
  .sort((a, b) => (b.priority || 0) - (a.priority || 0));

// Utility functions
export const filterActionsByRole = (actions: UserActionConfig[], userRole: string): UserActionConfig[] => {
  return actions.filter(action => {
    if (!action.requiredRole) return true;
    return action.requiredRole.includes(userRole);
  });
};

export const filterActionsByPermission = (actions: UserActionConfig[], userPermissions: string[]): UserActionConfig[] => {
  return actions.filter(action => {
    if (!action.requiredPermission) return true;
    return action.requiredPermission.some(permission => userPermissions.includes(permission));
  });
};

export const addSeparators = (actions: UserActionConfig[]): UserActionConfig[] => {
  const result: UserActionConfig[] = [];
  let currentCategory = '';
  
  actions.forEach((action, index) => {
    if (action.category !== currentCategory && index > 0) {
      result.push({
        id: `separator-${currentCategory}-${action.category}`,
        label: '',
        icon: null,
        separator: true,
        category: 'separator',
        priority: action.priority || 0
      });
    }
    result.push(action);
    currentCategory = action.category || '';
  });
  
  return result;
};

export const getActionById = (actionId: string): UserActionConfig | undefined => {
  return CORE_USER_ACTIONS.find(action => action.id === actionId);
};

export const getActionsByCategory = (category: string): UserActionConfig[] => {
  return CORE_USER_ACTIONS.filter(action => action.category === category);
};
