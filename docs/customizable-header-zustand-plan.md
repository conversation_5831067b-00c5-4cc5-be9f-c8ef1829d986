# Customizable Header Component Plan (with Zustand)

**Goal:** Create a Zustand store and a custom hook to enable pages to dynamically set the `AppHeader`'s title, breadcrumbs, and actions using Zustand for state management.

**Components and Files:**

1.  **`store/app-header-store.ts`:** This file will define the Zustand store.
2.  **`components/layout/app-header.tsx`:** This file will contain the `AppHeader` component, which will now consume state from the Zustand store.
3.  **Individual Page Files (e.g., `app/assets/page.tsx`):** These files will use the store's actions to set the header content.

**Plan Details:**

1.  **Create `store/app-header-store.ts`:**
    *   Install Zustand if not already installed (`npm install zustand` or `yarn add zustand`).
    *   Define an interface for the header state, including `title`, `breadcrumbs`, and `actions`.
    *   Define an interface for the store's actions (e.g., `setHeaderContent`).
    *   Create the Zustand store using `create`, defining the initial state and the `setHeaderContent` action.

    ```typescript
    import { create } from 'zustand';

    interface AppHeaderState {
      title?: string;
      breadcrumbs?: { label: string; url: string }[];
      actions?: React.ReactNode[];
    }

    interface AppHeaderActions {
      setHeaderContent: (content: AppHeaderState) => void;
      resetHeaderContent: () => void; // Optional: for cleanup
    }

    type AppHeaderStore = AppHeaderState & AppHeaderActions;

    export const useAppHeaderStore = create<AppHeaderStore>((set) => ({
      title: undefined,
      breadcrumbs: undefined,
      actions: undefined,
      setHeaderContent: (content) => set(content),
      resetHeaderContent: () => set({ title: undefined, breadcrumbs: undefined, actions: undefined }),
    }));
    ```

    ```mermaid
    graph TD
        store/app-header-store.ts --> useAppHeaderStore(Zustand Store Hook)
        useAppHeaderStore --> HeaderState({title, breadcrumbs, actions})
        useAppHeaderStore --> setHeaderContent(function)
        useAppHeaderStore --> resetHeaderContent(function)
    ```

2.  **Modify `components/layout/app-header.tsx`:**
    *   Import the `useAppHeaderStore` hook.
    *   Use the hook to select the relevant state (`title`, `breadcrumbs`, `actions`) from the store.
    *   The component will render based on this state, similar to the previous plan, but reading from the store instead of props.
    *   Remove the `AppHeaderProps` interface and the props parameter from the component function.

    ```mermaid
    graph TD
        components/layout/app-header.tsx --> useAppHeaderStore
        useAppHeaderStore --> HeaderState(Reads from Store)
        components/layout/app-header.tsx --> HeaderElement(Renders based on State)
    ```

3.  **Modify `app/layout.tsx`:**
    *   Remove the `AppHeaderProvider` (as it's no longer needed with Zustand).
    *   Keep the `AppHeader` component rendering as before. It will automatically connect to the Zustand store.

    ```mermaid
    graph TD
        app/layout.tsx --> AppHeader(Connects to Store)
        AppHeader --> children(Page Content)
    ```

4.  **Modify Individual Page Files (e.g., `app/assets/page.tsx`):**
    *   Import the `useAppHeaderStore` hook.
    *   Use the hook to access the `setHeaderContent` action from the store.
    *   Use `useEffect` to call `setHeaderContent` with the desired title, breadcrumbs, and actions for that specific page when the component mounts.
    *   Optionally, use the `resetHeaderContent` action in the `useEffect` cleanup function to clear the header content when navigating away from the page.

    ```mermaid
    graph TD
        app/assets/page.tsx --> useAppHeaderStore
        useAppHeaderStore --> setHeaderContent(action)
        useAppHeaderStore --> resetHeaderContent(action)
        app/assets/page.tsx --> useEffect
        useEffect --> setHeaderContent(Page Specific Content)
        useEffect --> cleanup(resetHeaderContent)
    ```

**Implementation Steps (in Code Mode):**

1.  Install Zustand.
2.  Create the `store/app-header-store.ts` file with the Zustand store definition.
3.  Update `components/layout/app-header.tsx` to consume state from the Zustand store.
4.  Update `app/layout.tsx` by removing the `AppHeaderProvider`.
5.  Update `app/assets/page.tsx` to use the `useAppHeaderStore` hook to set its header content in a `useEffect`.
6.  Repeat step 5 for any other pages that need to customize the header.