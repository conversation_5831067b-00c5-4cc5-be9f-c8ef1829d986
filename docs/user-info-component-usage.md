# Modular User Info Component - Usage Guide

## Overview

The Modular User Info Component is a production-ready, reusable system for displaying user information across your application. It provides consistent user experience with customizable layouts, actions, and integrations.

## Features

- 🎨 **Multiple Variants**: Sidebar, header, compact, and full layouts
- 👤 **Smart Avatar System**: Fallbacks, loading states, and status indicators
- ⚙️ **Configurable Actions**: Role-based action filtering with custom actions support
- 🔗 **Asset Module Editor Integration**: Direct link to the module editor for admins
- 📱 **Responsive Design**: Adapts to different screen sizes and contexts
- 🎯 **Type Safety**: Full TypeScript support with comprehensive interfaces
- 🔄 **Real-time Updates**: Automatic user data synchronization

## Quick Start

### Basic Usage

```typescript
import { UserInfoSidebar } from "@/components/user/user-info";

export function MySidebar() {
  return (
    <UserInfoSidebar 
      size="lg"
      showRole={true}
      showStatus={false}
    />
  );
}
```

### Available Variants

```typescript
import { 
  UserInfo,           // Base component with full customization
  UserInfoSidebar,    // Optimized for sidebar usage
  UserInfoHeader,     // Optimized for header/navbar usage
  UserInfoCompact,    // Minimal avatar-only display
  UserInfoFull        // Full details with all information
} from "@/components/user/user-info";
```

## Component Variants

### 1. Sidebar Variant (Default)

Perfect for application sidebars with full user information and dropdown menu.

```typescript
<UserInfoSidebar 
  size="lg"
  showRole={true}
  showStatus={false}
  dropdownAlign="end"
  dropdownSide="top"
/>
```

**Features:**
- Large avatar with user name and role
- Comprehensive dropdown menu with all actions
- Asset Module Editor link for admin users
- Optimized for vertical sidebar layouts

### 2. Header Variant

Designed for application headers and navigation bars.

```typescript
<UserInfoHeader 
  size="md"
  showRole={false}
  dropdownAlign="end"
  dropdownSide="bottom"
/>
```

**Features:**
- Compact horizontal layout
- Essential actions only
- Quick access to notifications and profile
- Responsive design for mobile headers

### 3. Compact Variant

Minimal avatar-only display for space-constrained areas.

```typescript
<UserInfoCompact 
  size="sm"
  showDropdown={true}
  onUserClick={() => router.push('/profile')}
/>
```

**Features:**
- Avatar only, no text
- Optional dropdown menu
- Perfect for mobile navigation
- Minimal space requirements

### 4. Full Variant

Complete user information display for profile pages and dashboards.

```typescript
<UserInfoFull 
  size="xl"
  layout="vertical"
  showStatus={true}
  showRole={true}
  showLastActive={true}
/>
```

**Features:**
- All user information displayed
- Status indicators and role badges
- Last active timestamp
- Vertical or horizontal layouts

## Configuration Options

### Size Options

```typescript
type Size = 'sm' | 'md' | 'lg';

// Small - 32px avatar, compact text
<UserInfo size="sm" />

// Medium - 40px avatar, standard text (default)
<UserInfo size="md" />

// Large - 48px avatar, larger text
<UserInfo size="lg" />
```

### Layout Options

```typescript
type Layout = 'horizontal' | 'vertical';

// Horizontal layout (default)
<UserInfo layout="horizontal" />

// Vertical layout - avatar above text
<UserInfo layout="vertical" />
```

### Display Options

```typescript
<UserInfo 
  showStatus={true}        // Show online/offline status
  showRole={true}          // Show user role badge
  showLastActive={true}    // Show last active timestamp
  showDropdown={true}      // Enable dropdown menu
/>
```

### Dropdown Configuration

```typescript
<UserInfo 
  dropdownAlign="end"      // 'start' | 'end' | 'center'
  dropdownSide="bottom"    // 'top' | 'bottom' | 'left' | 'right'
/>
```

## Custom Actions

### Adding Custom Actions

```typescript
const customActions = [
  {
    id: 'custom-action',
    label: 'Custom Action',
    icon: Star,
    href: '/custom-page',
    description: 'Navigate to custom page',
    category: 'custom'
  }
];

<UserInfo customActions={customActions} />
```

### Action Configuration

```typescript
interface UserActionConfig {
  id: string;
  label: string;
  icon: any;                    // Lucide React icon
  href?: string;                // Navigation URL
  onClick?: () => void;         // Custom click handler
  variant?: 'default' | 'destructive';
  separator?: boolean;          // Add separator before action
  badge?: string | number;      // Action badge (e.g., notification count)
  disabled?: boolean;           // Disable action
  description?: string;         // Tooltip description
  shortcut?: string;           // Keyboard shortcut display
  category?: string;           // Action grouping
  requiredRole?: string[];     // Required user roles
  showInSidebar?: boolean;     // Show in sidebar variant
  showInHeader?: boolean;      // Show in header variant
  priority?: number;           // Display order priority
}
```

## Asset Module Editor Integration

The component automatically includes a link to the Asset Module Editor for admin users:

```typescript
// Automatically included for admin users
{
  id: 'asset-module-editor',
  label: 'Asset Module Editor',
  icon: Code,
  href: '/module-editor/new',
  description: 'Create and edit asset modules',
  badge: 'New',
  requiredRole: ['admin', 'developer']
}
```

**Features:**
- Only visible to admin and developer roles
- Direct navigation to module editor
- "New" badge to highlight the feature
- Integrated with existing module editor routing

## Advanced Usage

### Custom Hook Integration

```typescript
import { useUserInfo } from "@/hooks/use-user-info";

export function CustomUserComponent() {
  const { 
    user, 
    userDisplayInfo, 
    userActions, 
    handleAction 
  } = useUserInfo({
    config: {
      showStatus: true,
      customActions: myCustomActions
    }
  });

  return (
    <div>
      <h1>Welcome, {userDisplayInfo.name}!</h1>
      <p>Role: {userDisplayInfo.role}</p>
      {/* Custom implementation */}
    </div>
  );
}
```

### Role-Based Action Filtering

```typescript
import { filterActionsByRole } from "@/lib/config/user-actions-config";

// Actions are automatically filtered based on user role
// Admin users see: Profile, Settings, Asset Module Editor, Billing, etc.
// Regular users see: Profile, Settings, Notifications, Help, etc.
```

### Event Handlers

```typescript
<UserInfo 
  onUserClick={() => {
    // Handle user avatar/name click
    router.push('/profile');
  }}
  config={{
    onLogout: () => {
      // Custom logout handler
      analytics.track('user_logout');
      logout();
    },
    onProfileClick: () => {
      // Custom profile click handler
      router.push('/profile');
    }
  }}
/>
```

## Styling and Theming

### CSS Classes

The component uses Tailwind CSS classes and follows the design system:

```typescript
<UserInfo 
  className="custom-user-info"           // Custom wrapper class
  dropdownAlign="end"
  dropdownSide="bottom"
/>
```

### Theme Integration

The component automatically adapts to your application's theme:

- Light/dark mode support
- Consistent with sidebar and header styling
- Follows design system color palette
- Responsive breakpoints

## Examples

### Sidebar Implementation

```typescript
// components/app-sidebar.tsx
import { UserInfoSidebar } from "@/components/user/user-info";

export function AppSidebar() {
  return (
    <Sidebar>
      {/* Navigation content */}
      <SidebarFooter>
        <UserInfoSidebar 
          size="lg"
          showRole={true}
          dropdownAlign="end"
          dropdownSide="top"
        />
      </SidebarFooter>
    </Sidebar>
  );
}
```

### Header Implementation

```typescript
// components/app-header.tsx
import { UserInfoHeader } from "@/components/user/user-info";

export function AppHeader() {
  return (
    <header className="flex items-center justify-between p-4">
      <h1>Application Title</h1>
      <UserInfoHeader 
        size="md"
        showRole={false}
        dropdownAlign="end"
      />
    </header>
  );
}
```

### Mobile Navigation

```typescript
// components/mobile-nav.tsx
import { UserInfoCompact } from "@/components/user/user-info";

export function MobileNav() {
  return (
    <nav className="flex items-center justify-between p-2">
      <MenuButton />
      <UserInfoCompact size="sm" />
    </nav>
  );
}
```

## Best Practices

1. **Choose the Right Variant**: Use sidebar variant for sidebars, header variant for headers, etc.
2. **Role-Based Display**: Let the component handle role-based action filtering automatically
3. **Consistent Sizing**: Use consistent sizes across similar contexts
4. **Custom Actions**: Add custom actions sparingly to avoid overwhelming users
5. **Responsive Design**: Test across different screen sizes and devices
6. **Performance**: The component is optimized for performance with proper memoization

## Troubleshooting

### Common Issues

1. **Actions not showing**: Check user role and action configuration
2. **Avatar not loading**: Verify avatar URL and fallback handling
3. **Dropdown positioning**: Adjust `dropdownAlign` and `dropdownSide` props
4. **Custom actions not working**: Ensure proper action configuration and handlers

### Debug Mode

Enable debug logging:

```typescript
const { user, userActions } = useUserInfo({
  config: { /* config */ }
});

console.log('User:', user);
console.log('Available actions:', userActions);
```

## Migration Guide

### From Old Sidebar Implementation

```typescript
// Old implementation ❌
<DropdownMenu>
  <DropdownMenuTrigger>
    <div>John Doe</div>
  </DropdownMenuTrigger>
  <DropdownMenuContent>
    <DropdownMenuItem>Profile</DropdownMenuItem>
    <DropdownMenuItem>Settings</DropdownMenuItem>
    <DropdownMenuItem>Sign out</DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>

// New implementation ✅
<UserInfoSidebar 
  size="lg"
  showRole={true}
/>
```

The new component provides much richer functionality with minimal code changes!

## Demo Component

A demo component is available to test all variants and configurations:

```typescript
import { UserInfoDemo } from "@/components/user/user-info-demo";

// Use in development to test different configurations
<UserInfoDemo />
```

## Contributing

To extend the user info system:

1. Add new actions to `lib/config/user-actions-config.ts`
2. Update the user hook in `hooks/use-user-info.ts`
3. Modify the component in `components/user/user-info.tsx`
4. Update this documentation
5. Test across all variants and screen sizes
