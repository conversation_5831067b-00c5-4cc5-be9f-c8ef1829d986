# Asset Type Management System - Setup Instructions

## 🚀 Quick Start

Follow these steps to set up the complete Asset Type Management System in your WizeAssets platform.

### Prerequisites

- Node.js 18+ installed
- PostgreSQL database running
- Prisma configured and connected

### Step 1: Update Database Schema

```bash
# Generate Prisma client with new schema
npm run db:generate

# Push schema changes to database
npm run db:push
```

### Step 2: Run Migration Script

```bash
# Run the asset type system migration
npm run migrate:asset-types
```

This script will:
- ✅ Create sample asset categories (IT Equipment, Office Furniture, Vehicles, Manufacturing Equipment)
- ✅ Create sample asset types (Laptop Computer, Office Desk)
- ✅ Set up form definitions for common operations
- ✅ Associate forms with asset types
- ✅ Configure depreciation settings
- ✅ Set up lifecycle stages
- ✅ Create maintenance schedules

### Step 3: Verify Installation

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Navigate to Asset Types Management:**
   - Go to `http://localhost:3065/admin/asset-types`
   - You should see the new Asset Types dashboard with:
     - Overview tab with metrics
     - Asset Types tab with sample data
     - Categories tab
     - Templates tab with pre-built templates
     - Analytics tab

3. **Test Template System:**
   - Click on the "Templates" tab
   - Click "Use Template" button
   - Select a template (Laptop Computer, Office Desk, or Company Vehicle)
   - Customize the asset type details
   - Click "Create Asset Type"

4. **Test Asset Operations:**
   - Navigate to any asset detail page
   - Look for the "Operations" section
   - Try creating a new operation using the dynamic forms

## 🎯 Key Features Available

### ✅ Asset Type Templates
- **Laptop Computer Template**: Complete IT asset configuration
- **Office Desk Template**: Furniture management setup
- **Company Vehicle Template**: Fleet management configuration

### ✅ Dynamic Form System
- Operation-specific forms for each asset type
- Runtime form rendering with validation
- Pre-population with existing data
- Conditional field logic

### ✅ Automation Engines
- **Depreciation Engine**: Automatic calculations with multiple methods
- **Lifecycle Engine**: Automated stage transitions
- **Maintenance Engine**: Scheduled task generation
- **Workflow Integration**: External workflow triggers

### ✅ Operation Types
- `asset.create` - Asset creation with full initialization
- `asset.update` - Asset updates with recalculations
- `asset.transfer` - Asset transfers with notifications
- `asset.disposal` - Asset disposal with lifecycle management
- `maintenance.log` - Maintenance activity logging
- `maintenance.schedule` - Maintenance task scheduling
- `depreciation.calculate` - Depreciation calculations
- `lifecycle.transition` - Manual lifecycle transitions
- `inventory.audit` - Inventory auditing

## 🔧 Configuration

### Custom Asset Types

1. **Using Templates:**
   - Go to Admin → Asset Types → Templates
   - Select a template that matches your needs
   - Customize name, code, and description
   - The system will create the asset type with all configurations

2. **Manual Creation:**
   - Go to Admin → Asset Types → Asset Types
   - Click "Create New Asset Type"
   - Configure custom fields, lifecycle stages, etc.

### Custom Forms

1. **Form Builder:**
   - Navigate to the Form Builder interface
   - Create operation-specific forms
   - Associate forms with asset types

2. **Form Configuration:**
   - Define sections and fields
   - Set up validation rules
   - Configure conditional logic

### Depreciation Settings

Configure depreciation for each asset type:
- **Straight Line**: Even depreciation over useful life
- **Declining Balance**: Accelerated depreciation
- **Double Declining Balance**: Maximum early depreciation
- **Sum of Years Digits**: Graduated depreciation

### Lifecycle Management

Set up lifecycle stages for each asset type:
- Define stage transitions
- Set required fields for each stage
- Configure automated actions
- Set up notifications

### Maintenance Scheduling

Configure maintenance schedules:
- **Preventive**: Time-based maintenance
- **Predictive**: Condition-based maintenance
- **Corrective**: Repair-based maintenance

## 📊 Monitoring and Analytics

### Dashboard Metrics
- Total asset types and active count
- Custom fields usage statistics
- Maintenance schedules overview
- Lifecycle stage distribution
- Depreciation method usage

### Operation History
- Complete audit trail of all operations
- Filterable by operation type, user, date
- Detailed operation context and results

### Performance Monitoring
- Operation success/failure rates
- Average operation completion times
- User activity patterns

## 🔍 Troubleshooting

### Common Issues

1. **Migration Script Fails:**
   ```bash
   # Check database connection
   npx prisma db pull
   
   # Verify schema
   npx prisma validate
   
   # Re-run migration
   npm run migrate:asset-types
   ```

2. **Templates Not Loading:**
   - Check API endpoint: `GET /api/asset-type-templates`
   - Verify template service is working
   - Check browser console for errors

3. **Forms Not Rendering:**
   - Verify form definitions exist in database
   - Check asset type form associations
   - Validate form JSON structure

4. **Operations Failing:**
   - Check operation type configuration
   - Verify required fields are provided
   - Check workflow engine integration

### Database Verification

```sql
-- Check if new tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
  'FormDefinition',
  'AssetTypeForm', 
  'AssetOperationHistory',
  'DepreciationSchedule',
  'MaintenanceTask',
  'LifecycleTransition'
);

-- Check sample data
SELECT COUNT(*) as asset_types FROM "AssetType";
SELECT COUNT(*) as form_definitions FROM "FormDefinition";
SELECT COUNT(*) as asset_type_forms FROM "AssetTypeForm";
```

## 🚀 Next Steps

### Immediate Actions
1. ✅ Run the setup steps above
2. ✅ Test template creation
3. ✅ Create your first asset using the new system
4. ✅ Test asset operations workflow

### Customization
1. **Create Custom Templates**: Add your own asset type templates
2. **Configure Workflows**: Set up automated workflows for your processes
3. **Customize Forms**: Create operation-specific forms for your needs
4. **Set Up Integrations**: Connect with external systems

### Advanced Features
1. **API Integration**: Use the REST APIs for external integrations
2. **Workflow Automation**: Set up complex automated workflows
3. **Custom Engines**: Extend the automation engines
4. **Analytics**: Build custom reports and dashboards

## 📚 Documentation

- **Full Documentation**: `/docs/ASSET_TYPE_MANAGEMENT_SYSTEM.md`
- **API Reference**: Check the API endpoints in `/app/api/`
- **Component Library**: Browse components in `/components/`
- **Type Definitions**: Review types in `/lib/types/`

## 🆘 Support

If you encounter any issues:

1. **Check the logs**: Look for error messages in the console
2. **Verify database**: Ensure all tables and data exist
3. **Test APIs**: Use tools like Postman to test endpoints
4. **Review documentation**: Check the comprehensive docs

The Asset Type Management System is now ready for production use! 🎉