# Creative Alerts System - Usage Guide

## Overview

The Creative Alerts System is a modular, reusable solution for displaying engaging success and error messages across authentication pages. It provides contextual, animated alerts with helpful hints and creative messaging.

## Features

- 🎨 **Creative Messaging**: Randomized, context-aware messages with themes
- 🎭 **Animated Icons**: Bouncing, pulsing, and sparkling icon animations
- 🎯 **Contextual Hints**: Smart error messages with helpful tips
- 🔄 **Reusable**: Works across all auth pages (login, register, forgot password, etc.)
- 🎪 **Themed Alerts**: Different visual themes for different contexts
- ⚡ **Auto-redirect**: Built-in success callback handling

## Quick Start

### 1. Import the Hook

```typescript
import { useCreativeAlerts } from "@/hooks/use-creative-alerts";
```

### 2. Initialize in Your Component

```typescript
export function YourAuthForm() {
  const { showSuccessAlert, showErrorAlert } = useCreativeAlerts({
    context: 'login', // or 'register', 'forgot-password', etc.
    onSuccess: () => {
      router.push('/dashboard');
    },
    successDelay: 1500 // Optional: delay before onSuccess callback
  });

  // Your form logic here...
}
```

### 3. Use in Form Submission

```typescript
async function onSubmit(values: FormData) {
  try {
    await yourAuthFunction(values);
    showSuccessAlert(); // Shows random success message
  } catch (error) {
    showErrorAlert(error); // Shows contextual error message
  }
}
```

## Available Contexts

### Login (`'login'`)
- **Success Messages**: Welcome back themes, mission control, lightning login
- **Error Messages**: Access denied, authentication failed, security check failed

### Register (`'register'`)
- **Success Messages**: Welcome to family, account created, ready for takeoff
- **Error Messages**: Email exists, password too weak, registration failed

### Forgot Password (`'forgot-password'`)
- **Success Messages**: Reset link sent, recovery initiated
- **Error Messages**: Email not found, request failed

### Reset Password (`'reset-password'`)
- **Success Messages**: Password updated, security restored
- **Error Messages**: Invalid reset link, password update failed

### Email Verification (`'verify-email'`)
- **Success Messages**: Email verified
- **Error Messages**: Verification failed

### Logout (`'logout'`)
- **Success Messages**: See you soon, session ended
- **Error Messages**: Logout issue

## Advanced Usage

### Custom Messages

You can override default messages:

```typescript
showSuccessAlert({
  title: "🎉 Custom Success!",
  description: "Your custom success message here",
  duration: 5000
});

showErrorAlert(error, {
  title: "❌ Custom Error!",
  description: "Your custom error message here",
  hint: "💡 Your custom hint here"
});
```

### Info and Warning Alerts

```typescript
const { showInfoAlert, showWarningAlert } = useCreativeAlerts({
  context: 'login'
});

showInfoAlert("Info Title", "Info description");
showWarningAlert("Warning Title", "Warning description");
```

### Quick Alerts (Without Hook Setup)

```typescript
import { createQuickAlert } from "@/hooks/use-creative-alerts";

const alerts = createQuickAlert('login');
alerts.success();
alerts.error(error);
```

## Configuration

### Adding New Contexts

Edit `lib/auth/creative-alerts-config.ts`:

```typescript
export const CREATIVE_ALERTS_CONFIG: AlertConfig = {
  'your-new-context': {
    success: [
      {
        title: "🎉 Success Title",
        description: "Success description",
        icon: YourIcon,
        theme: "celebration",
        duration: 4000
      }
    ],
    error: [
      {
        title: "❌ Error Title",
        description: "Error description",
        icon: ErrorIcon,
        theme: "error",
        hint: "💡 Helpful tip",
        duration: 6000
      }
    ]
  }
};
```

### Custom Animations

Add to `app/globals.css`:

```css
@keyframes yourCustomAnimation {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.animate-yourCustom {
  animation: yourCustomAnimation 1s ease-in-out;
}
```

## Examples

### Login Form Example

```typescript
"use client";

import { useCreativeAlerts } from "@/hooks/use-creative-alerts";
import { useAuth } from "@/contexts/auth-context";

export function LoginForm() {
  const { login } = useAuth();
  const router = useRouter();

  const { showSuccessAlert, showErrorAlert } = useCreativeAlerts({
    context: 'login',
    onSuccess: () => router.push('/admin'),
    successDelay: 1500
  });

  async function onSubmit(values: LoginFormData) {
    try {
      await login(values.email, values.password);
      showSuccessAlert();
    } catch (error) {
      showErrorAlert(error);
    }
  }

  // Rest of your form JSX...
}
```

### Register Form Example

```typescript
export function RegisterForm() {
  const { register } = useAuth();
  
  const { showSuccessAlert, showErrorAlert } = useCreativeAlerts({
    context: 'register',
    onSuccess: () => router.push('/welcome'),
    successDelay: 2000
  });

  async function onSubmit(values: RegisterFormData) {
    try {
      await register(values.name, values.email, values.password);
      showSuccessAlert();
    } catch (error) {
      showErrorAlert(error);
    }
  }

  // Rest of your form JSX...
}
```

## Styling

The system uses Tailwind CSS classes and custom animations. Key classes:

- `.animate-slideInBounce` - Success alert entrance
- `.animate-shakeError` - Error alert shake
- `.success-glow` - Page success glow effect
- `.error-glow` - Page error glow effect

## Best Practices

1. **Always use appropriate context** for your auth page
2. **Handle success callbacks** with appropriate delays
3. **Provide meaningful error context** when possible
4. **Test animations** across different devices
5. **Keep messages concise** but engaging
6. **Use consistent theming** across your app

## Troubleshooting

### Common Issues

1. **Alerts not showing**: Check if toast provider is properly set up
2. **Animations not working**: Ensure CSS animations are loaded
3. **Wrong context messages**: Verify you're using the correct context string
4. **Callback not firing**: Check onSuccess configuration and delay settings

### Debug Mode

Enable debug logging:

```typescript
const { showSuccessAlert } = useCreativeAlerts({
  context: 'login',
  onSuccess: () => {
    console.log('Success callback fired');
    router.push('/admin');
  }
});
```

## Contributing

To add new message themes or contexts:

1. Update `creative-alerts-config.ts`
2. Add new animations to `globals.css`
3. Update this documentation
4. Test across all supported contexts

## Migration from Old Toast System

Replace old toast calls:

```typescript
// Old way ❌
toast({
  title: "Success",
  description: "Login successful"
});

// New way ✅
showSuccessAlert();
```

The new system provides much richer, more engaging user experiences with minimal code changes.
