@tailwind base;
@tailwind components;
@tailwind utilities;

/* Creative Login Alert Animations */
@keyframes celebration {
  0%, 100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.1) rotate(-5deg); }
  75% { transform: scale(1.1) rotate(5deg); }
}

@keyframes sparkle {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

@keyframes slideInBounce {
  0% { transform: translateX(-100%) scale(0.8); opacity: 0; }
  60% { transform: translateX(10%) scale(1.05); opacity: 1; }
  100% { transform: translateX(0) scale(1); opacity: 1; }
}

@keyframes shakeError {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(34, 197, 94, 0.3); }
  50% { box-shadow: 0 0 20px rgba(34, 197, 94, 0.6), 0 0 30px rgba(34, 197, 94, 0.4); }
}

@keyframes errorGlow {
  0%, 100% { box-shadow: 0 0 5px rgba(239, 68, 68, 0.3); }
  50% { box-shadow: 0 0 20px rgba(239, 68, 68, 0.6), 0 0 30px rgba(239, 68, 68, 0.4); }
}

/* Utility classes for creative alerts */
@layer utilities {
  .animate-celebration {
    animation: celebration 0.6s ease-in-out;
  }

  .animate-sparkle {
    animation: sparkle 1s ease-in-out infinite;
  }

  .animate-slideInBounce {
    animation: slideInBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .animate-shakeError {
    animation: shakeError 0.5s ease-in-out;
  }

  .animate-heartbeat {
    animation: heartbeat 1s ease-in-out infinite;
  }

  .success-glow {
    animation: glow 2s ease-in-out;
  }

  .error-glow {
    animation: errorGlow 2s ease-in-out;
  }

  /* Toast container enhancements */
  .creative-toast-success {
    @apply border-green-200 bg-green-50 dark:bg-green-950 dark:border-green-800;
    animation: slideInBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .creative-toast-error {
    @apply border-red-200 bg-red-50 dark:bg-red-950 dark:border-red-800;
    animation: shakeError 0.5s ease-in-out;
  }

  .creative-toast-info {
    @apply border-blue-200 bg-blue-50 dark:bg-blue-950 dark:border-blue-800;
  }

  .creative-toast-warning {
    @apply border-yellow-200 bg-yellow-50 dark:bg-yellow-950 dark:border-yellow-800;
  }
}

@layer base {
  :root {
    --background: 220 28% 95%;
    --foreground: 220 28.4211% 18.6275%;

    --card: 222 21% 98%;
    --card-foreground: 220 28.4211% 18.6275%;

    --popover: 222 18% 98%;
    --popover-foreground: 220 28.4211% 18.6275%;

    --primary: 49 96% 50%;
    --primary-foreground: 220 28% 95%;

    --secondary: 220 19% 90%;
    --secondary-foreground: 220 28.4211% 18.6275%;

    --muted: 220 19% 90%;
    --muted-foreground: 0 0% 40%;

    --accent: 223 34% 90%;
    --accent-foreground: 220 28.4211% 18.6275%;

    --destructive: 0 84% 50%;
    --destructive-foreground: 0 0% 98%;

    --border: 225 16% 80%;
    --input: 225 16% 80%;
    --ring: 49 96% 50%;

    --radius: 0.5rem;

    --sidebar-background: 221 21% 92%;
    --sidebar-foreground: 220 28.4211% 18.6275%;
    --sidebar-primary: 49 96% 50%;
    --sidebar-primary-foreground: 220 28% 95%;
    --sidebar-accent: 223 34% 90%;
    --sidebar-accent-foreground: 220 28.4211% 18.6275%;
    --sidebar-border: 225 16% 80%;
    --sidebar-ring: 49 96% 50%;

    --underline: 217 91% 59%;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;


    --font-sans: var(--font-outfit), system-ui, sans-serif;
    --font-mono: var(--font-jetbrains-mono), Consolas, Monaco, monospace;
    --font-serif: var(--font-montserrat), Georgia, serif;
    --font-manrope: var(--font-manrope), system-ui, sans-serif;
    --radius: 0.475rem;

    --shadow-color: #1a1a1a;

    --shadow-opacity: 0.1;

    --shadow-blur: 3px;

    --shadow-spread: 0px;

    --shadow-offset-x: 0px;

    --shadow-offset-y: 1px;

    --letter-spacing: -0.025em;

    --spacing: 0.23rem;

    --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 15% / 0.1);
    --shadow-xs: 0px 1px 4px 0px hsl(0 0% 15% / 0.15);
    --shadow-sm: 0px 1px 3px 0px hsl(0 0% 15% / 0.1), 0px 2px 5px 0px hsl(0 0% 15% / 0.15);
    --shadow: 0px 1px 4px 0px hsl(0 0% 15% / 0.15), 0px 2px 5px 0px hsl(0 0% 15% / 0.1);
    --shadow-md: 0px 3px 6px -1px hsl(0 0% 15% / 0.15), 0px 5px 8px -1px hsl(0 0% 15% / 0.15);
    --shadow-lg: 0px 5px 8px -2px hsl(0 0% 15% / 0.15), 0px 12px 18px -3px hsl(0 0% 15% / 0.15);
    --shadow-xl: 0px 12px 18px -3px hsl(0 0% 15% / 0.15), 0px 25px 30px -5px hsl(0 0% 15% / 0.15);
    --shadow-2xl: 0px 30px 60px -12px hsl(0 0% 15% / 0.35);

    --tracking-normal: -0.025em;
  }
  .dark {
  --background: 220 28.4211% 18.6275%;
  --foreground: 0 0% 89.8039%;
  --card: 222.2222 21.2598% 24.9020%;
  --card-foreground: 0 0% 89.8039%;
  --popover: 222.8571 18.5841% 22.1569%;
  --popover-foreground: 0 0% 89.8039%;
  --primary: 49.6335 95.9799% 60.9804%;
  --primary-foreground: 220 28.4211% 18.6275%;
  --secondary: 220.0000 19.3548% 24.3137%;
  --secondary-foreground: 0 0% 89.8039%;
  --muted: 220.0000 19.3548% 24.3137%;
  --muted-foreground: 0 0% 63.9216%;
  --accent: 223.0189 34.6405% 30%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 84.3972% 72.3529%;
  --destructive-foreground: 0 0% 100%;
  --border: 225 16.0920% 34.1176%;
  --input: 225 16.0920% 34.1176%;
  --ring: 49.6335 95.9799% 60.9804%;
  --chart-1: 210.7317 37.6147% 78.6275%;
  --chart-2: 12 64.5161% 87.8431%;
  --chart-3: 213.4426 30.0493% 60.1961%;
  --chart-4: 216.4045 35.7430% 51.1765%;
  --chart-5: 221.0526 43.7788% 42.5490%;
  --sidebar-background: 221.53847 21% 25%;
  --sidebar-foreground: 0 0% 89.8039%;
  --sidebar-primary: 49.6335 95.9799% 60.9804%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 223.0189 34.6405% 30%;
  --sidebar-accent-foreground: 0 0% 100%;
  --sidebar-border: 225 16.0920% 34.1176%;
  --sidebar-ring: 49.6335 95.9799% 60.9804%;
  --font-sans: Outfit, sans-serif;
  --font-serif: Montserrat, sans-serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.55rem;
  --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.3);
  --shadow-xs: 0px 1px 4px 0px hsl(0 0% 10.1961% / 0.35);
  --shadow-sm: 0px 1px 3px 0px hsl(0 0% 10.1961% / 0.5), 0px 2px 5px -1px hsl(0 0% 10.1961% / 0.5);
  --shadow: 0px 1px 4px 0px hsl(0 0% 10.1961% / 0.5), 0px 2px 5px -1px hsl(0 0% 10.1961% / 0.5);
  --shadow-md: 0px 3px 6px -1px hsl(0 0% 10.1961% / 0.5), 0px 5px 8px -1px hsl(0 0% 10.1961% / 0.5);
  --shadow-lg: 0px 5px 8px -2px hsl(0 0% 10.1961% / 0.5), 0px 12px 18px -3px hsl(0 0% 10.1961% / 0.5);
  --shadow-xl: 0px 12px 18px -3px hsl(0 0% 10.1961% / 0.5), 0px 25px 30px -5px hsl(0 0% 10.1961% / 0.5);
  --shadow-2xl: 0px 30px 60px -12px hsl(0 0% 10.1961% / 1.25);
}
  .theme {
    --font-sans: var(--font-outfit), system-ui, sans-serif;
    --font-mono: var(--font-jetbrains-mono), Consolas, Monaco, monospace;
    --font-serif: var(--font-montserrat), Georgia, serif;
    --font-manrope: var(--font-manrope), system-ui, sans-serif;
    --radius: 0.475rem;
    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  }
  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading font-semibold;
  }
  /* Remove underlines from all elements */
  a,
  button,
  [role="button"],
  input,
  select,
  textarea {
    text-decoration: none !important;
  }
  /* Remove underline from focus states */
  *:focus {
    text-decoration: none !important;
  }
  /* Remove underline from hover states */
  *:hover {
    text-decoration: none !important;
  }
}