"use client"

import { useState, use<PERSON>em<PERSON>, useEffect } from "react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { useHeaderTabs } from "@/hooks/use-admin-tabs"
import { getMaintenanceHeaderConfig } from "@/lib/utils/admin-header-configs"
import { getMaintenanceHeaderTabs } from "@/lib/utils/admin-tabs-configs"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { HeaderTabContent } from "@/components/ui/header-tab-content"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { DatePicker } from "@/components/ui/date-picker"
import { toast } from "@/components/ui/use-toast"
import {
  Plus,
  AlertTriangle,
  Clock,
  Wrench,
  DollarSign,
  Calendar,
  CheckCircle,
  XCircle,
  User,
  MapPin,
  FileText,
  Timer,
  TrendingUp,
  Activity,
  Zap,
  Target,
  BarChart3,
  Filter,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Play,
  Pause,
  Square,
  RefreshCw,
  Download,
  Upload,
  Bell,
  Settings,
  Eye,
  Calendar as CalendarIcon,
  Users,
  Brain,
} from "lucide-react"
import { getStatusBadge } from "@/lib/utils/asset-status";
import { getMaintenanceStatusBadge } from "@/lib/utils/maintenance-types";
import { MaintenanceTasksTable } from "@/components/maintenance/maintenance-tasks-table";
import { MaintenanceCalendar } from "@/components/maintenance/maintenance-calendar";
import { MaintenanceAnalytics } from "@/components/maintenance/maintenance-analytics";
import { CreateMaintenanceTaskDialog } from "@/components/maintenance/create-maintenance-task-dialog";
import { MaintenanceNotifications } from "@/components/maintenance/maintenance-notifications";
import { PredictiveMaintenancePanel } from "@/components/maintenance/predictive-maintenance-panel";

// Enhanced maintenance data
const maintenanceTasks = [
  {
    id: "MNT-001",
    assetId: "AST-002",
    assetName: "Toyota Forklift Model X",
    type: "Preventive",
    priority: "High",
    status: "Scheduled",
    scheduledDate: "2024-01-15",
    assignedTo: "Mike Johnson",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    estimatedCost: 500,
    actualCost: 0,
    estimatedDuration: 4,
    actualDuration: 0,
    description: "Regular oil change and filter replacement",
    location: "Warehouse A, Bay 3",
    completionPercentage: 0,
    lastUpdated: "2024-01-10",
    checklist: [
      { item: "Check oil level", completed: false },
      { item: "Replace oil filter", completed: false },
      { item: "Replace air filter", completed: false },
      { item: "Inspect hydraulic system", completed: false },
    ],
    notes: [],
    attachments: 2,
  },
  {
    id: "MNT-002",
    assetId: "AST-005",
    assetName: "Security Camera System Pro",
    type: "Inspection",
    priority: "Medium",
    status: "In Progress",
    scheduledDate: "2024-01-12",
    assignedTo: "David Park",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    estimatedCost: 200,
    actualCost: 150,
    estimatedDuration: 2,
    actualDuration: 1.5,
    description: "Quarterly security system inspection and calibration",
    location: "Building Perimeter",
    completionPercentage: 75,
    lastUpdated: "2024-01-12",
    checklist: [
      { item: "Test camera functionality", completed: true },
      { item: "Check recording quality", completed: true },
      { item: "Calibrate motion sensors", completed: true },
      { item: "Update firmware", completed: false },
    ],
    notes: ["All cameras operational", "Motion detection working properly"],
    attachments: 5,
  },
  {
    id: "MNT-003",
    assetId: "AST-001",
    assetName: "Dell Laptop XPS 15",
    type: "Repair",
    priority: "Low",
    status: "Completed",
    scheduledDate: "2024-01-10",
    assignedTo: "Sarah Wilson",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    estimatedCost: 150,
    actualCost: 125,
    estimatedDuration: 1,
    actualDuration: 0.5,
    description: "Screen replacement and system update",
    location: "IT Department",
    completionPercentage: 100,
    lastUpdated: "2024-01-10",
    checklist: [
      { item: "Replace screen", completed: true },
      { item: "Update operating system", completed: true },
      { item: "Install security patches", completed: true },
      { item: "Test functionality", completed: true },
    ],
    notes: ["Screen replaced successfully", "All updates installed", "User satisfied with repair"],
    attachments: 3,
  },
  {
    id: "MNT-004",
    assetId: "AST-004",
    assetName: "HP LaserJet Pro 4000",
    type: "Preventive",
    priority: "Medium",
    status: "Overdue",
    scheduledDate: "2024-01-08",
    assignedTo: "Robert Chen",
    assigneeAvatar: "/placeholder.svg?height=32&width=32",
    estimatedCost: 75,
    actualCost: 0,
    estimatedDuration: 1,
    actualDuration: 0,
    description: "Toner replacement and cleaning",
    location: "Office Floor 1",
    completionPercentage: 0,
    lastUpdated: "2024-01-08",
    checklist: [
      { item: "Replace toner cartridge", completed: false },
      { item: "Clean print heads", completed: false },
      { item: "Check paper feed", completed: false },
      { item: "Test print quality", completed: false },
    ],
    notes: [],
    attachments: 1,
  },
]

const maintenanceStats = {
  totalTasks: maintenanceTasks.length,
  scheduled: maintenanceTasks.filter((task) => task.status === "Scheduled").length,
  inProgress: maintenanceTasks.filter((task) => task.status === "In Progress").length,
  overdue: maintenanceTasks.filter((task) => task.status === "Overdue").length,
  completed: maintenanceTasks.filter((task) => task.status === "Completed").length,
  totalCost: maintenanceTasks.reduce((sum, task) => sum + task.actualCost, 0),
  avgCompletionTime: 2.5,
  efficiency: 87,
  costSavings: 15000,
}

const technicians = [
  {
    name: "Mike Johnson",
    avatar: "/placeholder.svg?height=32&width=32",
    activeTasks: 3,
    completedTasks: 15,
    rating: 4.8,
  },
  {
    name: "Sarah Wilson",
    avatar: "/placeholder.svg?height=32&width=32",
    activeTasks: 2,
    completedTasks: 22,
    rating: 4.9,
  },
  {
    name: "David Park",
    avatar: "/placeholder.svg?height=32&width=32",
    activeTasks: 4,
    completedTasks: 18,
    rating: 4.7,
  },
  {
    name: "Robert Chen",
    avatar: "/placeholder.svg?height=32&width=32",
    activeTasks: 1,
    completedTasks: 12,
    rating: 4.6,
  },
]

export default function MaintenancePage() {
  // State management
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [selectedTask, setSelectedTask] = useState<string | null>(null)
  const [maintenanceTasks, setMaintenanceTasks] = useState([])
  const [maintenanceStats, setMaintenanceStats] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState({
    status: [],
    priority: [],
    type: [],
    assignedTo: "",
    search: "",
  })
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
  })

  // Data fetching
  useEffect(() => {
    fetchMaintenanceTasks()
    fetchMaintenanceStats()
  }, [filters, pagination])

  const fetchMaintenanceTasks = async () => {
    try {
      setLoading(true)
      const queryParams = new URLSearchParams()
      queryParams.append("page", pagination.page.toString())
      queryParams.append("limit", pagination.limit.toString())

      // Add array filters
      if (filters.status.length > 0) {
        queryParams.append("status", filters.status.join(","))
      }
      if (filters.priority.length > 0) {
        queryParams.append("priority", filters.priority.join(","))
      }
      if (filters.type.length > 0) {
        queryParams.append("type", filters.type.join(","))
      }
      if (filters.assignedTo) {
        queryParams.append("assignedTo", filters.assignedTo)
      }
      if (filters.search) {
        queryParams.append("search", filters.search)
      }

      const response = await fetch(`/api/maintenance/tasks?${queryParams}`)
      const data = await response.json()

      if (data.success) {
        setMaintenanceTasks(data.data.tasks)
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch maintenance tasks",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error fetching maintenance tasks:", error)
      toast({
        title: "Error",
        description: "Failed to fetch maintenance tasks",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchMaintenanceStats = async () => {
    try {
      const response = await fetch("/api/maintenance/statistics")
      const data = await response.json()

      if (data.success) {
        setMaintenanceStats(data.data)
      }
    } catch (error) {
      console.error("Error fetching maintenance statistics:", error)
    }
  }

  const handleCreateTask = async (taskData: any) => {
    try {
      const response = await fetch("/api/maintenance/tasks", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(taskData),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: "Maintenance task created successfully",
        })
        setIsAddDialogOpen(false)
        fetchMaintenanceTasks()
        fetchMaintenanceStats()
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to create maintenance task",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error creating maintenance task:", error)
      toast({
        title: "Error",
        description: "Failed to create maintenance task",
        variant: "destructive",
      })
    }
  }

  const handleUpdateTask = async (taskId: string, updates: any) => {
    try {
      const response = await fetch(`/api/maintenance/tasks/${taskId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updates),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: "Maintenance task updated successfully",
        })
        fetchMaintenanceTasks()
        fetchMaintenanceStats()
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to update maintenance task",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error updating maintenance task:", error)
      toast({
        title: "Error",
        description: "Failed to update maintenance task",
        variant: "destructive",
      })
    }
  }

  const handleDeleteTask = async (taskId: string) => {
    try {
      const response = await fetch(`/api/maintenance/tasks/${taskId}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: "Maintenance task deleted successfully",
        })
        fetchMaintenanceTasks()
        fetchMaintenanceStats()
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to delete maintenance task",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting maintenance task:", error)
      toast({
        title: "Error",
        description: "Failed to delete maintenance task",
        variant: "destructive",
      })
    }
  }

  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => ({
    ...getMaintenanceHeaderConfig(),
    actions: [
      <Button variant="outline" key="refresh" onClick={() => {
        fetchMaintenanceTasks()
        fetchMaintenanceStats()
      }}>
        <RefreshCw className="mr-2 h-4 w-4" />
        Refresh
      </Button>,
      <Button variant="outline" key="schedule-maintenance">
        <Calendar className="mr-2 h-4 w-4" />
        Schedule
      </Button>,
      <Button key="new-task" onClick={() => setIsAddDialogOpen(true)}>
        <Wrench className="mr-2 h-4 w-4" />
        New Task
      </Button>,
    ],
  }), []);

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getMaintenanceHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "overview");

  // Function to get tab content based on tab ID
  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "overview":
        return (
          <div className="space-y-6">
            {/* Enhanced Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card className="relative overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
                  <Wrench className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{maintenanceStats?.totalTasks || 0}</div>
                  <p className="text-xs text-muted-foreground">All maintenance tasks</p>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500" />
                </CardContent>
              </Card>
              <Card className="relative overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{maintenanceStats?.scheduledTasks || 0}</div>
                  <p className="text-xs text-muted-foreground">Upcoming tasks</p>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-blue-500" />
                </CardContent>
              </Card>
              <Card className="relative overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">In Progress</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{maintenanceStats?.inProgressTasks || 0}</div>
                  <p className="text-xs text-muted-foreground">Active tasks</p>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-500 to-orange-500" />
                </CardContent>
              </Card>
              <Card className="relative overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Overdue</CardTitle>
                  <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">{maintenanceStats?.overdueTasks || 0}</div>
                  <p className="text-xs text-muted-foreground">Needs attention</p>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-red-500 to-pink-500" />
                </CardContent>
              </Card>
            </div>
          </div>
        );
      default:
        return <div>Content for {tabId} tab</div>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "High":
        return <Badge variant="destructive">High</Badge>
      case "Medium":
        return <Badge variant="default">Medium</Badge>
      case "Low":
        return <Badge variant="secondary">Low</Badge>
      default:
        return <Badge variant="secondary">{priority}</Badge>
    }
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Schedule Maintenance Task</DialogTitle>
                <DialogDescription>
                  Create a comprehensive maintenance task with detailed specifications.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="asset">Asset *</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select asset" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ast-001">Dell Laptop XPS 15</SelectItem>
                        <SelectItem value="ast-002">Toyota Forklift Model X</SelectItem>
                        <SelectItem value="ast-003">Executive Conference Table</SelectItem>
                        <SelectItem value="ast-004">HP LaserJet Pro 4000</SelectItem>
                        <SelectItem value="ast-005">Security Camera System Pro</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="type">Maintenance Type *</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="preventive">Preventive</SelectItem>
                        <SelectItem value="repair">Repair</SelectItem>
                        <SelectItem value="inspection">Inspection</SelectItem>
                        <SelectItem value="emergency">Emergency</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="priority">Priority *</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="technician">Assign Technician</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select technician" />
                      </SelectTrigger>
                      <SelectContent>
                        {technicians.map((tech) => (
                          <SelectItem key={tech.name} value={tech.name.toLowerCase().replace(" ", "-")}>
                            {tech.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">Scheduled Date *</Label>
                    <Input id="date" type="date" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="duration">Estimated Duration (hours)</Label>
                    <Input id="duration" type="number" step="0.5" placeholder="2.0" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="cost">Estimated Cost</Label>
                    <Input id="cost" type="number" placeholder="0.00" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="location">Location</Label>
                    <Input id="location" placeholder="Asset location" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea id="description" placeholder="Detailed description of maintenance task..." />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="checklist">Maintenance Checklist</Label>
                  <Textarea id="checklist" placeholder="Enter checklist items (one per line)..." />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" onClick={() => setIsAddDialogOpen(false)}>
                  Schedule Task
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        

      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "scheduled", content: getTabContent("scheduled") },
          { id: "calendar", content: getTabContent("calendar") },
          { id: "technicians", content: getTabContent("technicians") },
          { id: "analytics", content: getTabContent("analytics") },
        ]}
      />
    </div>
  )
}
            