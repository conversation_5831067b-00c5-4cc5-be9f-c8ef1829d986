import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { runtimeSystem } from '@/lib/asset-modules/runtime/runtime-system';
import { ModuleExecutionContext } from '@/lib/types/asset-modules';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';

// Request validation schema
const validateModuleSchema = z.object({
  moduleId: z.string().min(1, 'Module ID is required'),
  data: z.record(z.any()).default({}),
  context: z.object({
    assetTypeId: z.string().min(1, 'Asset type ID is required'),
    userId: z.string().optional(),
    userRole: z.string().optional(),
    permissions: z.array(z.string()).optional(),
    sessionId: z.string().optional(),
    environment: z.enum(['development', 'staging', 'production']).optional(),
    metadata: z.record(z.any()).optional()
  })
});

/**
 * POST /api/asset-modules/runtime/validate
 * Validate data against a module's validation rules
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = validateModuleSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { moduleId, data, context } = validationResult.data;

    // Check if user has permission to validate modules
    if (!session.user.permissions?.includes('module.validate')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to validate modules' },
        { status: 403 }
      );
    }

    // Verify module exists and is accessible
    const module = await db.assetModule.findFirst({
      where: {
        id: moduleId,
        isActive: true,
        OR: [
          { isPublic: true },
          { authorId: session.user.id },
          { 
            permissions: {
              some: {
                userId: session.user.id,
                permission: 'validate'
              }
            }
          }
        ]
      }
    });

    if (!module) {
      return NextResponse.json(
        { error: 'Module not found or access denied' },
        { status: 404 }
      );
    }

    // Create execution context
    const executionContext: ModuleExecutionContext = {
      moduleId,
      assetId: 'validation',
      assetTypeId: context.assetTypeId,
      userId: session.user.id,
      userRole: session.user.role || 'user',
      permissions: session.user.permissions || ['module.validate'],
      sessionId: session.sessionId || 'unknown',
      executionId: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      environment: context.environment || 'production',
      metadata: {
        ...context.metadata,
        requestId: crypto.randomUUID(),
        validationOnly: true,
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    };

    // Validate module data
    const result = await runtimeSystem.validateModuleData(
      moduleId,
      data,
      executionContext
    );

    // Log validation for audit
    await db.moduleValidationLog.create({
      data: {
        moduleId,
        userId: session.user.id,
        assetTypeId: context.assetTypeId,
        valid: result.valid,
        validationTime: result.metadata.totalValidationTime,
        fieldsValidated: result.metadata.fieldsValidated,
        rulesExecuted: result.metadata.rulesExecuted,
        inputData: JSON.stringify(data),
        errors: result.errors.length > 0 ? JSON.stringify(result.errors) : null,
        warnings: result.warnings.length > 0 ? JSON.stringify(result.warnings) : null,
        metadata: JSON.stringify({
          executionContext,
          fieldResults: Object.keys(result.fieldResults).length
        })
      }
    });

    // Return validation result
    return NextResponse.json({
      success: true,
      data: {
        moduleId: result.moduleId,
        valid: result.valid,
        validationTime: result.metadata.totalValidationTime,
        fieldsValidated: result.metadata.fieldsValidated,
        rulesExecuted: result.metadata.rulesExecuted,
        fieldResults: Object.fromEntries(
          Object.entries(result.fieldResults).map(([fieldId, fieldResult]) => [
            fieldId,
            {
              fieldId: fieldResult.fieldId,
              valid: fieldResult.valid,
              value: fieldResult.value,
              normalizedValue: fieldResult.normalizedValue,
              errors: fieldResult.errors.length > 0 ? fieldResult.errors : undefined,
              warnings: fieldResult.warnings.length > 0 ? fieldResult.warnings : undefined,
              validationTime: fieldResult.metadata.validationTime,
              rulesApplied: fieldResult.metadata.rulesApplied
            }
          ])
        ),
        crossFieldValidation: result.crossFieldValidation.length > 0 ? result.crossFieldValidation : undefined,
        errors: result.errors.length > 0 ? result.errors : undefined,
        warnings: result.warnings.length > 0 ? result.warnings : undefined,
        summary: {
          totalFields: Object.keys(result.fieldResults).length,
          validFields: Object.values(result.fieldResults).filter(r => r.valid).length,
          invalidFields: Object.values(result.fieldResults).filter(r => !r.valid).length,
          totalErrors: result.errors.length + Object.values(result.fieldResults).reduce((sum, r) => sum + r.errors.length, 0),
          totalWarnings: result.warnings.length + Object.values(result.fieldResults).reduce((sum, r) => sum + r.warnings.length, 0)
        }
      }
    });

  } catch (error) {
    console.error('Module validation error:', error);
    
    return NextResponse.json(
      { 
        error: 'Module validation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/asset-modules/runtime/validate
 * Get validation history and statistics
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const moduleId = searchParams.get('moduleId');
    const assetTypeId = searchParams.get('assetTypeId');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    const includeStats = searchParams.get('includeStats') === 'true';

    // Build query conditions
    const where: any = {
      userId: session.user.id
    };

    if (moduleId) {
      where.moduleId = moduleId;
    }

    if (assetTypeId) {
      where.assetTypeId = assetTypeId;
    }

    // Get validation history
    const validations = await db.moduleValidationLog.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset,
      select: {
        id: true,
        moduleId: true,
        assetTypeId: true,
        valid: true,
        validationTime: true,
        fieldsValidated: true,
        rulesExecuted: true,
        errors: true,
        warnings: true,
        createdAt: true,
        module: {
          select: {
            name: true,
            version: true
          }
        }
      }
    });

    // Get total count
    const total = await db.moduleValidationLog.count({ where });

    let stats = undefined;
    if (includeStats) {
      // Calculate validation statistics
      const allValidations = await db.moduleValidationLog.findMany({
        where,
        select: {
          valid: true,
          validationTime: true,
          fieldsValidated: true,
          rulesExecuted: true,
          errors: true,
          warnings: true
        }
      });

      const totalValidations = allValidations.length;
      const successfulValidations = allValidations.filter(v => v.valid).length;
      const averageValidationTime = totalValidations > 0 
        ? allValidations.reduce((sum, v) => sum + v.validationTime, 0) / totalValidations 
        : 0;
      const averageFieldsValidated = totalValidations > 0
        ? allValidations.reduce((sum, v) => sum + v.fieldsValidated, 0) / totalValidations
        : 0;
      const averageRulesExecuted = totalValidations > 0
        ? allValidations.reduce((sum, v) => sum + v.rulesExecuted, 0) / totalValidations
        : 0;

      stats = {
        totalValidations,
        successfulValidations,
        failedValidations: totalValidations - successfulValidations,
        successRate: totalValidations > 0 ? (successfulValidations / totalValidations) * 100 : 0,
        averageValidationTime: Math.round(averageValidationTime * 100) / 100,
        averageFieldsValidated: Math.round(averageFieldsValidated * 100) / 100,
        averageRulesExecuted: Math.round(averageRulesExecuted * 100) / 100
      };
    }

    return NextResponse.json({
      success: true,
      data: {
        validations: validations.map(validation => ({
          id: validation.id,
          moduleId: validation.moduleId,
          moduleName: validation.module?.name,
          moduleVersion: validation.module?.version,
          assetTypeId: validation.assetTypeId,
          valid: validation.valid,
          validationTime: validation.validationTime,
          fieldsValidated: validation.fieldsValidated,
          rulesExecuted: validation.rulesExecuted,
          hasErrors: !!validation.errors,
          hasWarnings: !!validation.warnings,
          createdAt: validation.createdAt
        })),
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        },
        stats
      }
    });

  } catch (error) {
    console.error('Error fetching validation history:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch validation history',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
