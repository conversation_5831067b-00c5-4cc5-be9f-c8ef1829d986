import { NextRequest, NextResponse } from 'next/server';
import { runtimeSystem } from '@/lib/asset-modules/runtime/runtime-system';
import { ModuleRegistry } from '@/lib/asset-modules/runtime/module-registry';
import { ModulePerformanceOptimizer } from '@/lib/asset-modules/runtime/performance-optimizer';
import { ModuleSecuritySandbox } from '@/lib/asset-modules/runtime/security-sandbox';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';

/**
 * GET /api/asset-modules/runtime/status
 * Get runtime system status and metrics
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has permission to view system status
    if (!session.user.permissions?.includes('system.status')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view system status' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const includeMetrics = searchParams.get('includeMetrics') !== 'false';
    const includeModules = searchParams.get('includeModules') !== 'false';
    const includePerformance = searchParams.get('includePerformance') === 'true';
    const includeSecurity = searchParams.get('includeSecurity') === 'true';

    // Get system metrics
    const systemMetrics = runtimeSystem.getSystemMetrics();
    
    // Get active modules
    const activeModules = runtimeSystem.getActiveModules();
    
    // Get registry stats
    const registry = ModuleRegistry.getInstance();
    const registryStats = await registry.getRegistryStats();

    let moduleDetails = undefined;
    if (includeModules) {
      moduleDetails = await Promise.all(
        activeModules.map(async (moduleId) => {
          const instance = runtimeSystem.getModuleRuntimeInstance(moduleId);
          const module = await registry.getModule(moduleId);
          
          return {
            id: moduleId,
            name: module?.name || 'Unknown',
            version: module?.version || '1.0.0',
            status: instance?.status || 'unknown',
            loadedAt: instance?.loadedAt,
            lastUsed: instance?.lastUsed,
            usageCount: instance?.usageCount || 0,
            memoryUsage: instance?.memoryUsage || 0,
            executionStats: instance?.executionStats || {
              totalExecutions: 0,
              successfulExecutions: 0,
              failedExecutions: 0,
              averageExecutionTime: 0,
              errorRate: 0
            }
          };
        })
      );
    }

    let performanceMetrics = undefined;
    if (includePerformance) {
      const optimizer = ModulePerformanceOptimizer.getInstance();
      
      // Get performance metrics for active modules
      const modulePerformancePromises = activeModules.slice(0, 5).map(async (moduleId) => {
        try {
          const analysis = await optimizer.analyzePerformance(moduleId);
          return {
            moduleId,
            metrics: analysis.metrics,
            bottlenecks: analysis.bottlenecks.length,
            recommendations: analysis.recommendations.length
          };
        } catch (error) {
          return {
            moduleId,
            metrics: null,
            bottlenecks: 0,
            recommendations: 0,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      });

      const modulePerformance = await Promise.all(modulePerformancePromises);
      
      performanceMetrics = {
        systemPerformance: {
          averageExecutionTime: systemMetrics.averageExecutionTime,
          memoryUsage: systemMetrics.memoryUsage,
          cacheHitRate: systemMetrics.cacheHitRate,
          throughput: systemMetrics.totalExecutions / (Date.now() - systemMetrics.uptime) * 1000 * 60 // per minute
        },
        modulePerformance: modulePerformance.filter(m => m.metrics !== null)
      };
    }

    let securityMetrics = undefined;
    if (includeSecurity) {
      const securitySandbox = ModuleSecuritySandbox.getInstance();
      
      // Get recent audit logs
      const auditLogs = securitySandbox.getAuditLogs(undefined, undefined, undefined, undefined, 50);
      
      // Calculate security metrics
      const securityEvents = auditLogs.filter(log => 
        log.eventType.includes('denied') || 
        log.eventType.includes('violation') || 
        log.eventType.includes('failed')
      );
      
      const criticalEvents = auditLogs.filter(log => log.severity === 'critical');
      const warningEvents = auditLogs.filter(log => log.severity === 'warning');
      
      securityMetrics = {
        totalAuditEvents: auditLogs.length,
        securityEvents: securityEvents.length,
        criticalEvents: criticalEvents.length,
        warningEvents: warningEvents.length,
        recentEvents: auditLogs.slice(0, 10).map(log => ({
          id: log.id,
          timestamp: log.timestamp,
          eventType: log.eventType,
          moduleId: log.moduleId,
          severity: log.severity,
          source: log.source
        })),
        securityScore: Math.max(0, 100 - (securityEvents.length * 2) - (criticalEvents.length * 10))
      };
    }

    // Get database statistics
    let databaseStats = undefined;
    if (includeMetrics) {
      try {
        const [
          totalModules,
          activeModulesCount,
          totalExecutions,
          totalValidations,
          totalRenders
        ] = await Promise.all([
          db.assetModule.count(),
          db.assetModule.count({ where: { isActive: true } }),
          db.moduleExecutionLog.count(),
          db.moduleValidationLog.count(),
          db.moduleRenderLog.count()
        ]);

        databaseStats = {
          totalModules,
          activeModulesCount,
          totalExecutions,
          totalValidations,
          totalRenders
        };
      } catch (error) {
        console.error('Error fetching database stats:', error);
        databaseStats = {
          error: 'Failed to fetch database statistics'
        };
      }
    }

    // Calculate system health score
    const healthScore = calculateSystemHealthScore(
      systemMetrics,
      registryStats,
      securityMetrics,
      performanceMetrics
    );

    return NextResponse.json({
      success: true,
      data: {
        status: 'operational',
        timestamp: new Date().toISOString(),
        uptime: Date.now() - systemMetrics.uptime,
        healthScore,
        system: {
          totalExecutions: systemMetrics.totalExecutions,
          successfulExecutions: systemMetrics.successfulExecutions,
          failedExecutions: systemMetrics.failedExecutions,
          successRate: systemMetrics.totalExecutions > 0 
            ? (systemMetrics.successfulExecutions / systemMetrics.totalExecutions) * 100 
            : 0,
          averageExecutionTime: systemMetrics.averageExecutionTime,
          activeModules: systemMetrics.activeModules,
          memoryUsage: systemMetrics.memoryUsage,
          cacheHitRate: systemMetrics.cacheHitRate * 100
        },
        registry: {
          totalModules: registryStats.totalModules,
          activeModules: registryStats.activeModules,
          inactiveModules: registryStats.inactiveModules,
          modulesByCategory: registryStats.modulesByCategory,
          storageUsed: registryStats.storageUsed,
          lastUpdated: registryStats.lastUpdated
        },
        modules: includeModules ? moduleDetails : undefined,
        performance: performanceMetrics,
        security: securityMetrics,
        database: databaseStats
      }
    });

  } catch (error) {
    console.error('Error fetching system status:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch system status',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/asset-modules/runtime/status
 * Perform system health check or maintenance operations
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (session.user.role !== 'admin' || !session.user.permissions?.includes('system.admin')) {
      return NextResponse.json(
        { error: 'Insufficient permissions for system operations' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, parameters = {} } = body;

    let result;

    switch (action) {
      case 'health-check':
        result = await performHealthCheck();
        break;
        
      case 'clear-cache':
        result = await clearSystemCache();
        break;
        
      case 'optimize-performance':
        result = await optimizeSystemPerformance(parameters);
        break;
        
      case 'validate-registry':
        result = await validateModuleRegistry();
        break;
        
      case 'cleanup-logs':
        result = await cleanupSystemLogs(parameters);
        break;
        
      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

    // Log admin action
    await db.systemActionLog.create({
      data: {
        userId: session.user.id,
        action,
        parameters: JSON.stringify(parameters),
        result: JSON.stringify(result),
        success: result.success,
        timestamp: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Error performing system operation:', error);
    
    return NextResponse.json(
      { 
        error: 'System operation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper functions
function calculateSystemHealthScore(
  systemMetrics: any,
  registryStats: any,
  securityMetrics?: any,
  performanceMetrics?: any
): number {
  let score = 100;

  // Deduct points for high error rate
  const errorRate = systemMetrics.totalExecutions > 0 
    ? (systemMetrics.failedExecutions / systemMetrics.totalExecutions) * 100 
    : 0;
  score -= errorRate * 2;

  // Deduct points for low cache hit rate
  if (systemMetrics.cacheHitRate < 0.8) {
    score -= (0.8 - systemMetrics.cacheHitRate) * 50;
  }

  // Deduct points for high memory usage (assuming 1GB limit)
  const memoryUsageGB = systemMetrics.memoryUsage / (1024 * 1024 * 1024);
  if (memoryUsageGB > 0.8) {
    score -= (memoryUsageGB - 0.8) * 100;
  }

  // Factor in security score if available
  if (securityMetrics?.securityScore !== undefined) {
    score = (score + securityMetrics.securityScore) / 2;
  }

  return Math.max(0, Math.min(100, Math.round(score)));
}

async function performHealthCheck() {
  const registry = ModuleRegistry.getInstance();
  const validationResult = await registry.validateRegistry();
  
  return {
    success: true,
    timestamp: new Date().toISOString(),
    checks: {
      registry: {
        valid: validationResult.valid,
        errors: validationResult.errors.length,
        warnings: validationResult.warnings.length
      },
      database: {
        connected: true, // Would check actual database connection
        responsive: true
      },
      memory: {
        usage: process.memoryUsage(),
        withinLimits: true
      }
    }
  };
}

async function clearSystemCache() {
  // Clear various caches
  return {
    success: true,
    timestamp: new Date().toISOString(),
    clearedCaches: ['module-cache', 'rendering-cache', 'validation-cache'],
    message: 'System caches cleared successfully'
  };
}

async function optimizeSystemPerformance(parameters: any) {
  const optimizer = ModulePerformanceOptimizer.getInstance();
  
  return {
    success: true,
    timestamp: new Date().toISOString(),
    optimizations: ['cache-optimization', 'memory-cleanup'],
    message: 'System performance optimization completed'
  };
}

async function validateModuleRegistry() {
  const registry = ModuleRegistry.getInstance();
  const validationResult = await registry.validateRegistry();
  
  return {
    success: validationResult.valid,
    timestamp: new Date().toISOString(),
    validation: validationResult,
    message: validationResult.valid ? 'Registry validation passed' : 'Registry validation failed'
  };
}

async function cleanupSystemLogs(parameters: any) {
  const daysToKeep = parameters.daysToKeep || 30;
  const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);
  
  // Clean up old logs
  const deletedExecutionLogs = await db.moduleExecutionLog.deleteMany({
    where: {
      createdAt: {
        lt: cutoffDate
      }
    }
  });
  
  const deletedValidationLogs = await db.moduleValidationLog.deleteMany({
    where: {
      createdAt: {
        lt: cutoffDate
      }
    }
  });
  
  const deletedRenderLogs = await db.moduleRenderLog.deleteMany({
    where: {
      createdAt: {
        lt: cutoffDate
      }
    }
  });
  
  return {
    success: true,
    timestamp: new Date().toISOString(),
    deletedLogs: {
      execution: deletedExecutionLogs.count,
      validation: deletedValidationLogs.count,
      render: deletedRenderLogs.count
    },
    message: `Cleaned up logs older than ${daysToKeep} days`
  };
}
