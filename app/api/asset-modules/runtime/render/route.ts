import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { runtimeSystem } from '@/lib/asset-modules/runtime/runtime-system';
import { ModuleExecutionContext } from '@/lib/types/asset-modules';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';

// Request validation schema
const renderModuleSchema = z.object({
  moduleId: z.string().min(1, 'Module ID is required'),
  data: z.record(z.any()).default({}),
  context: z.object({
    assetTypeId: z.string().min(1, 'Asset type ID is required'),
    userId: z.string().optional(),
    userRole: z.string().optional(),
    permissions: z.array(z.string()).optional(),
    sessionId: z.string().optional(),
    environment: z.enum(['development', 'staging', 'production']).optional(),
    metadata: z.record(z.any()).optional()
  }),
  options: z.object({
    includeCSS: z.boolean().default(true),
    includeJS: z.boolean().default(true),
    optimize: z.boolean().default(true),
    theme: z.string().optional(),
    responsive: z.boolean().default(true)
  }).optional()
});

/**
 * POST /api/asset-modules/runtime/render
 * Render module fields with provided data
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = renderModuleSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { moduleId, data, context, options = {} } = validationResult.data;

    // Check if user has permission to render modules
    if (!session.user.permissions?.includes('module.render')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to render modules' },
        { status: 403 }
      );
    }

    // Verify module exists and is accessible
    const module = await db.assetModule.findFirst({
      where: {
        id: moduleId,
        isActive: true,
        OR: [
          { isPublic: true },
          { authorId: session.user.id },
          { 
            permissions: {
              some: {
                userId: session.user.id,
                permission: 'render'
              }
            }
          }
        ]
      }
    });

    if (!module) {
      return NextResponse.json(
        { error: 'Module not found or access denied' },
        { status: 404 }
      );
    }

    // Create execution context
    const executionContext: ModuleExecutionContext = {
      moduleId,
      assetId: 'render',
      assetTypeId: context.assetTypeId,
      userId: session.user.id,
      userRole: session.user.role || 'user',
      permissions: session.user.permissions || ['module.render'],
      sessionId: session.sessionId || 'unknown',
      executionId: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      environment: context.environment || 'production',
      metadata: {
        ...context.metadata,
        requestId: crypto.randomUUID(),
        renderOnly: true,
        renderOptions: options,
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    };

    // Render module fields
    const result = await runtimeSystem.renderModuleFields(
      moduleId,
      data,
      executionContext
    );

    // Process rendering options
    let bundledCSS = '';
    let bundledJS = '';
    let optimizedHTML = result.layout;

    if (options.includeCSS) {
      bundledCSS = result.fields
        .map(field => field.css)
        .filter(css => css && css.trim())
        .join('\n');
      
      // Apply theme if specified
      if (options.theme) {
        bundledCSS = applyTheme(bundledCSS, options.theme);
      }
      
      // Add responsive styles if enabled
      if (options.responsive) {
        bundledCSS += getResponsiveStyles();
      }
    }

    if (options.includeJS) {
      bundledJS = result.fields
        .map(field => field.javascript)
        .filter(js => js && js.trim())
        .join('\n');
    }

    // Optimize if requested
    if (options.optimize) {
      bundledCSS = optimizeCSS(bundledCSS);
      bundledJS = optimizeJS(bundledJS);
      optimizedHTML = optimizeHTML(optimizedHTML);
    }

    // Log rendering for audit
    await db.moduleRenderLog.create({
      data: {
        moduleId,
        userId: session.user.id,
        assetTypeId: context.assetTypeId,
        renderTime: result.totalRenderTime,
        fieldsRendered: result.fields.length,
        cacheHits: result.cacheHits,
        cacheMisses: result.cacheMisses,
        inputData: JSON.stringify(data),
        options: JSON.stringify(options),
        metadata: JSON.stringify({
          executionContext,
          renderResult: {
            groupId: result.groupId,
            totalRenderTime: result.totalRenderTime,
            cacheHitRate: result.cacheHits / (result.cacheHits + result.cacheMisses)
          }
        })
      }
    });

    // Return rendering result
    return NextResponse.json({
      success: true,
      data: {
        moduleId,
        groupId: result.groupId,
        renderTime: result.totalRenderTime,
        fieldsRendered: result.fields.length,
        cacheHits: result.cacheHits,
        cacheMisses: result.cacheMisses,
        cacheHitRate: result.cacheHits / (result.cacheHits + result.cacheMisses),
        html: optimizedHTML,
        css: options.includeCSS ? bundledCSS : undefined,
        javascript: options.includeJS ? bundledJS : undefined,
        fields: result.fields.map(field => ({
          fieldId: field.fieldId,
          renderTime: field.metadata.renderTime,
          hasErrors: field.errors.length > 0,
          dependencies: field.dependencies,
          errors: field.errors.length > 0 ? field.errors : undefined
        })),
        metadata: {
          renderOptions: options,
          optimized: options.optimize,
          responsive: options.responsive,
          theme: options.theme
        }
      }
    });

  } catch (error) {
    console.error('Module rendering error:', error);
    
    return NextResponse.json(
      { 
        error: 'Module rendering failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/asset-modules/runtime/render
 * Get rendering history and performance metrics
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const moduleId = searchParams.get('moduleId');
    const assetTypeId = searchParams.get('assetTypeId');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    const includeMetrics = searchParams.get('includeMetrics') === 'true';

    // Build query conditions
    const where: any = {
      userId: session.user.id
    };

    if (moduleId) {
      where.moduleId = moduleId;
    }

    if (assetTypeId) {
      where.assetTypeId = assetTypeId;
    }

    // Get rendering history
    const renders = await db.moduleRenderLog.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset,
      select: {
        id: true,
        moduleId: true,
        assetTypeId: true,
        renderTime: true,
        fieldsRendered: true,
        cacheHits: true,
        cacheMisses: true,
        options: true,
        createdAt: true,
        module: {
          select: {
            name: true,
            version: true
          }
        }
      }
    });

    // Get total count
    const total = await db.moduleRenderLog.count({ where });

    let metrics = undefined;
    if (includeMetrics) {
      // Calculate rendering metrics
      const allRenders = await db.moduleRenderLog.findMany({
        where,
        select: {
          renderTime: true,
          fieldsRendered: true,
          cacheHits: true,
          cacheMisses: true
        }
      });

      const totalRenders = allRenders.length;
      const averageRenderTime = totalRenders > 0 
        ? allRenders.reduce((sum, r) => sum + r.renderTime, 0) / totalRenders 
        : 0;
      const averageFieldsRendered = totalRenders > 0
        ? allRenders.reduce((sum, r) => sum + r.fieldsRendered, 0) / totalRenders
        : 0;
      const totalCacheHits = allRenders.reduce((sum, r) => sum + r.cacheHits, 0);
      const totalCacheMisses = allRenders.reduce((sum, r) => sum + r.cacheMisses, 0);
      const overallCacheHitRate = (totalCacheHits + totalCacheMisses) > 0
        ? (totalCacheHits / (totalCacheHits + totalCacheMisses)) * 100
        : 0;

      metrics = {
        totalRenders,
        averageRenderTime: Math.round(averageRenderTime * 100) / 100,
        averageFieldsRendered: Math.round(averageFieldsRendered * 100) / 100,
        totalCacheHits,
        totalCacheMisses,
        overallCacheHitRate: Math.round(overallCacheHitRate * 100) / 100,
        fastestRender: totalRenders > 0 ? Math.min(...allRenders.map(r => r.renderTime)) : 0,
        slowestRender: totalRenders > 0 ? Math.max(...allRenders.map(r => r.renderTime)) : 0
      };
    }

    return NextResponse.json({
      success: true,
      data: {
        renders: renders.map(render => ({
          id: render.id,
          moduleId: render.moduleId,
          moduleName: render.module?.name,
          moduleVersion: render.module?.version,
          assetTypeId: render.assetTypeId,
          renderTime: render.renderTime,
          fieldsRendered: render.fieldsRendered,
          cacheHits: render.cacheHits,
          cacheMisses: render.cacheMisses,
          cacheHitRate: render.cacheHits / (render.cacheHits + render.cacheMisses),
          options: render.options ? JSON.parse(render.options as string) : {},
          createdAt: render.createdAt
        })),
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        },
        metrics
      }
    });

  } catch (error) {
    console.error('Error fetching rendering history:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch rendering history',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper functions
function applyTheme(css: string, theme: string): string {
  // Apply theme-specific styles
  const themeStyles = getThemeStyles(theme);
  return css + '\n' + themeStyles;
}

function getThemeStyles(theme: string): string {
  const themes: Record<string, string> = {
    dark: `
      .field-wrapper { background-color: #1f2937; color: #f9fafb; }
      .field-input, .field-select { background-color: #374151; border-color: #6b7280; color: #f9fafb; }
      .field-label { color: #e5e7eb; }
    `,
    light: `
      .field-wrapper { background-color: #ffffff; color: #111827; }
      .field-input, .field-select { background-color: #ffffff; border-color: #d1d5db; color: #111827; }
      .field-label { color: #374151; }
    `,
    blue: `
      .field-wrapper { border-left: 4px solid #3b82f6; }
      .field-input:focus, .field-select:focus { border-color: #3b82f6; box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }
      .field-label { color: #1e40af; }
    `
  };
  
  return themes[theme] || '';
}

function getResponsiveStyles(): string {
  return `
    @media (max-width: 768px) {
      .field-wrapper { margin-bottom: 0.75rem; }
      .field-input, .field-select { font-size: 16px; }
    }
    @media (max-width: 480px) {
      .field-group-container { padding: 0.5rem; }
      .field-wrapper { margin-bottom: 0.5rem; }
    }
  `;
}

function optimizeCSS(css: string): string {
  // Basic CSS optimization
  return css
    .replace(/\s+/g, ' ')
    .replace(/;\s*}/g, '}')
    .replace(/\s*{\s*/g, '{')
    .replace(/;\s*/g, ';')
    .trim();
}

function optimizeJS(js: string): string {
  // Basic JS optimization
  return js
    .replace(/\s+/g, ' ')
    .replace(/;\s*}/g, ';}')
    .replace(/\s*{\s*/g, '{')
    .trim();
}

function optimizeHTML(html: string): string {
  // Basic HTML optimization
  return html
    .replace(/>\s+</g, '><')
    .replace(/\s+/g, ' ')
    .trim();
}
