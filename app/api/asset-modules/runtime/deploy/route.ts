import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { editorIntegration } from '@/lib/asset-modules/runtime/editor-integration';
import { AssetModule } from '@/lib/types/asset-modules';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';

// Request validation schema
const deployModuleSchema = z.object({
  moduleData: z.object({
    id: z.string().min(1),
    name: z.string().min(1),
    description: z.string().optional(),
    version: z.string().min(1),
    fields: z.array(z.any()),
    logic: z.object({
      nodes: z.array(z.any()),
      edges: z.array(z.any())
    }),
    rendering: z.object({}).optional(),
    validation: z.object({}).optional(),
    dependencies: z.array(z.string()).default([]),
    compatibleAssetTypes: z.array(z.string()).default([]),
    tags: z.array(z.string()).default([]),
    isActive: z.boolean().default(true),
    isPublic: z.boolean().default(false),
    isBuiltIn: z.boolean().default(false),
    requiredPermissions: z.array(z.string()).default([]),
    author: z.string().optional(),
    authorId: z.string().optional(),
    createdAt: z.string().optional(),
    updatedAt: z.string().optional()
  }),
  deploymentOptions: z.object({
    autoActivate: z.boolean().default(true),
    skipValidation: z.boolean().default(false),
    environment: z.enum(['development', 'staging', 'production']).default('production')
  }).optional()
});

/**
 * POST /api/asset-modules/runtime/deploy
 * Deploy a module from editor to runtime
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = deployModuleSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { moduleData, deploymentOptions = {} } = validationResult.data;

    // Check if user has permission to deploy modules
    if (!session.user.permissions?.includes('module.deploy')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to deploy modules' },
        { status: 403 }
      );
    }

    // Verify user owns the module or has deploy permissions
    const existingModule = await db.assetModule.findFirst({
      where: {
        id: moduleData.id,
        OR: [
          { authorId: session.user.id },
          { 
            permissions: {
              some: {
                userId: session.user.id,
                permission: 'deploy'
              }
            }
          }
        ]
      }
    });

    if (!existingModule && moduleData.authorId !== session.user.id) {
      return NextResponse.json(
        { error: 'Module not found or access denied' },
        { status: 404 }
      );
    }

    // Prepare module data for deployment
    const moduleForDeployment: AssetModule = {
      ...moduleData,
      authorId: moduleData.authorId || session.user.id,
      author: moduleData.author || session.user.name || 'Unknown',
      updatedAt: new Date().toISOString()
    };

    // Deploy module
    const deploymentResult = await editorIntegration.deployModule(
      moduleForDeployment,
      session.user.id,
      deploymentOptions
    );

    // Log deployment
    await db.moduleDeploymentLog.create({
      data: {
        moduleId: moduleData.id,
        userId: session.user.id,
        deploymentId: deploymentResult.deploymentId,
        success: deploymentResult.success,
        version: deploymentResult.version,
        deploymentTime: deploymentResult.deploymentTime,
        status: deploymentResult.status,
        errors: deploymentResult.errors.length > 0 ? JSON.stringify(deploymentResult.errors) : null,
        warnings: deploymentResult.warnings.length > 0 ? JSON.stringify(deploymentResult.warnings) : null,
        options: JSON.stringify(deploymentOptions),
        metadata: JSON.stringify(deploymentResult.metadata || {})
      }
    });

    return NextResponse.json({
      success: deploymentResult.success,
      data: {
        deploymentId: deploymentResult.deploymentId,
        moduleId: deploymentResult.moduleId,
        version: deploymentResult.version,
        status: deploymentResult.status,
        deploymentTime: deploymentResult.deploymentTime,
        autoActivated: deploymentResult.activationResult?.success || false,
        errors: deploymentResult.errors.length > 0 ? deploymentResult.errors : undefined,
        warnings: deploymentResult.warnings.length > 0 ? deploymentResult.warnings : undefined,
        metadata: deploymentResult.metadata
      }
    });

  } catch (error) {
    console.error('Module deployment error:', error);
    
    return NextResponse.json(
      { 
        error: 'Module deployment failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/asset-modules/runtime/deploy
 * Get deployment history
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const moduleId = searchParams.get('moduleId');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query conditions
    const where: any = {
      userId: session.user.id
    };

    if (moduleId) {
      where.moduleId = moduleId;
    }

    // Get deployment history
    const deployments = await db.moduleDeploymentLog.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset,
      select: {
        id: true,
        moduleId: true,
        deploymentId: true,
        success: true,
        version: true,
        deploymentTime: true,
        status: true,
        errors: true,
        warnings: true,
        createdAt: true,
        module: {
          select: {
            name: true,
            description: true,
            isActive: true
          }
        }
      }
    });

    // Get total count
    const total = await db.moduleDeploymentLog.count({ where });

    return NextResponse.json({
      success: true,
      data: {
        deployments: deployments.map(deployment => ({
          id: deployment.id,
          moduleId: deployment.moduleId,
          moduleName: deployment.module?.name,
          moduleDescription: deployment.module?.description,
          deploymentId: deployment.deploymentId,
          success: deployment.success,
          version: deployment.version,
          deploymentTime: deployment.deploymentTime,
          status: deployment.status,
          hasErrors: !!deployment.errors,
          hasWarnings: !!deployment.warnings,
          isActive: deployment.module?.isActive,
          createdAt: deployment.createdAt
        })),
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      }
    });

  } catch (error) {
    console.error('Error fetching deployment history:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch deployment history',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/asset-modules/runtime/deploy
 * Undeploy a module (deactivate and remove from runtime)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const moduleId = searchParams.get('moduleId');

    if (!moduleId) {
      return NextResponse.json(
        { error: 'Module ID is required' },
        { status: 400 }
      );
    }

    // Check if user has permission to undeploy modules
    if (!session.user.permissions?.includes('module.undeploy')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to undeploy modules' },
        { status: 403 }
      );
    }

    // Verify user owns the module or has undeploy permissions
    const module = await db.assetModule.findFirst({
      where: {
        id: moduleId,
        OR: [
          { authorId: session.user.id },
          { 
            permissions: {
              some: {
                userId: session.user.id,
                permission: 'undeploy'
              }
            }
          }
        ]
      }
    });

    if (!module) {
      return NextResponse.json(
        { error: 'Module not found or access denied' },
        { status: 404 }
      );
    }

    // TODO: Implement undeployment logic
    // This would involve:
    // 1. Deactivating the module in runtime
    // 2. Removing it from the registry
    // 3. Cleaning up any integrations
    // 4. Updating database status

    // For now, just update the database status
    await db.assetModule.update({
      where: { id: moduleId },
      data: {
        isActive: false,
        deploymentStatus: 'undeployed',
        undeployedAt: new Date(),
        undeployedBy: session.user.id
      }
    });

    // Log undeployment
    await db.moduleDeploymentLog.create({
      data: {
        moduleId,
        userId: session.user.id,
        deploymentId: `undeploy-${Date.now()}`,
        success: true,
        version: module.version,
        deploymentTime: 0,
        status: 'undeployed',
        options: JSON.stringify({ action: 'undeploy' }),
        metadata: JSON.stringify({ undeployedAt: new Date().toISOString() })
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        moduleId,
        status: 'undeployed',
        message: 'Module successfully undeployed'
      }
    });

  } catch (error) {
    console.error('Module undeployment error:', error);
    
    return NextResponse.json(
      { 
        error: 'Module undeployment failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
