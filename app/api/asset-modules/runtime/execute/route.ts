import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { runtimeSystem } from '@/lib/asset-modules/runtime/runtime-system';
import { ModuleExecutionContext } from '@/lib/types/asset-modules';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';

// Request validation schema
const executeModuleSchema = z.object({
  moduleId: z.string().min(1, 'Module ID is required'),
  assetId: z.string().optional(),
  assetTypeId: z.string().min(1, 'Asset type ID is required'),
  inputData: z.record(z.any()).default({}),
  context: z.object({
    userId: z.string().optional(),
    userRole: z.string().optional(),
    permissions: z.array(z.string()).optional(),
    sessionId: z.string().optional(),
    environment: z.enum(['development', 'staging', 'production']).optional(),
    metadata: z.record(z.any()).optional()
  }).optional()
});

/**
 * POST /api/asset-modules/runtime/execute
 * Execute an asset module with provided input data
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = executeModuleSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { moduleId, assetId, assetTypeId, inputData, context } = validationResult.data;

    // Check if user has permission to execute modules
    if (!session.user.permissions?.includes('module.execute')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to execute modules' },
        { status: 403 }
      );
    }

    // Verify module exists and is accessible
    const module = await db.assetModule.findFirst({
      where: {
        id: moduleId,
        isActive: true,
        OR: [
          { isPublic: true },
          { authorId: session.user.id },
          { 
            permissions: {
              some: {
                userId: session.user.id,
                permission: 'execute'
              }
            }
          }
        ]
      }
    });

    if (!module) {
      return NextResponse.json(
        { error: 'Module not found or access denied' },
        { status: 404 }
      );
    }

    // Verify asset type exists if assetId is provided
    if (assetId) {
      const asset = await db.asset.findFirst({
        where: {
          id: assetId,
          assetTypeId: assetTypeId
        }
      });

      if (!asset) {
        return NextResponse.json(
          { error: 'Asset not found' },
          { status: 404 }
        );
      }
    }

    // Create execution context
    const executionContext: Partial<ModuleExecutionContext> = {
      userId: session.user.id,
      userRole: session.user.role || 'user',
      permissions: session.user.permissions || ['module.execute'],
      sessionId: session.sessionId || 'unknown',
      environment: context?.environment || 'production',
      metadata: {
        ...context?.metadata,
        requestId: crypto.randomUUID(),
        timestamp: new Date().toISOString(),
        userAgent: request.headers.get('user-agent') || 'unknown'
      }
    };

    // Execute module
    const result = await runtimeSystem.executeModule(
      moduleId,
      assetId || 'new',
      assetTypeId,
      inputData,
      executionContext
    );

    // Log execution for audit
    await db.moduleExecutionLog.create({
      data: {
        moduleId,
        assetId,
        assetTypeId,
        userId: session.user.id,
        executionId: result.executionId,
        success: result.success,
        executionTime: result.executionTime,
        inputData: JSON.stringify(inputData),
        outputData: JSON.stringify(result.outputs),
        errors: result.errors.length > 0 ? JSON.stringify(result.errors) : null,
        metadata: JSON.stringify(result.metadata)
      }
    });

    // Return execution result
    return NextResponse.json({
      success: true,
      data: {
        executionId: result.executionId,
        moduleId: result.moduleId,
        success: result.success,
        executionTime: result.executionTime,
        outputs: result.outputs,
        renderResults: Object.keys(result.renderResults).length > 0 ? result.renderResults : undefined,
        errors: result.errors.length > 0 ? result.errors : undefined,
        warnings: result.warnings.length > 0 ? result.warnings : undefined,
        metadata: {
          startTime: result.metadata.startTime,
          endTime: result.metadata.endTime,
          context: {
            moduleId: result.metadata.context.moduleId,
            assetTypeId: result.metadata.context.assetTypeId,
            executionId: result.metadata.context.executionId,
            timestamp: result.metadata.context.timestamp,
            environment: result.metadata.context.environment
          }
        }
      }
    });

  } catch (error) {
    console.error('Module execution error:', error);
    
    return NextResponse.json(
      { 
        error: 'Module execution failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/asset-modules/runtime/execute
 * Get execution history and status
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const moduleId = searchParams.get('moduleId');
    const assetId = searchParams.get('assetId');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query conditions
    const where: any = {
      userId: session.user.id
    };

    if (moduleId) {
      where.moduleId = moduleId;
    }

    if (assetId) {
      where.assetId = assetId;
    }

    // Get execution history
    const executions = await db.moduleExecutionLog.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset,
      select: {
        id: true,
        moduleId: true,
        assetId: true,
        assetTypeId: true,
        executionId: true,
        success: true,
        executionTime: true,
        errors: true,
        createdAt: true,
        module: {
          select: {
            name: true,
            version: true
          }
        }
      }
    });

    // Get total count
    const total = await db.moduleExecutionLog.count({ where });

    return NextResponse.json({
      success: true,
      data: {
        executions: executions.map(execution => ({
          id: execution.id,
          moduleId: execution.moduleId,
          moduleName: execution.module?.name,
          moduleVersion: execution.module?.version,
          assetId: execution.assetId,
          assetTypeId: execution.assetTypeId,
          executionId: execution.executionId,
          success: execution.success,
          executionTime: execution.executionTime,
          hasErrors: !!execution.errors,
          createdAt: execution.createdAt
        })),
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      }
    });

  } catch (error) {
    console.error('Error fetching execution history:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch execution history',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
