import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { editorIntegration } from '@/lib/asset-modules/runtime/editor-integration';
import { auth } from '@/lib/auth';

// Request validation schema
const renderPreviewSchema = z.object({
  sessionId: z.string().min(1, 'Session ID is required'),
  data: z.record(z.any()).default({}),
  assetTypeId: z.string().min(1, 'Asset type ID is required'),
  renderOptions: z.object({
    includeCSS: z.boolean().default(true),
    includeJS: z.boolean().default(true),
    theme: z.string().optional(),
    responsive: z.boolean().default(true),
    optimize: z.boolean().default(false)
  }).optional()
});

/**
 * POST /api/asset-modules/runtime/preview/render
 * Render module fields in preview mode
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = renderPreviewSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { sessionId, data, assetTypeId, renderOptions = {} } = validationResult.data;

    // Check if user has permission to preview modules
    if (!session.user.permissions?.includes('module.preview')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to preview modules' },
        { status: 403 }
      );
    }

    // Verify session belongs to user
    const activeSessions = editorIntegration.getActivePreviewSessions(session.user.id);
    const targetSession = activeSessions.find(s => s.sessionId === sessionId);

    if (!targetSession) {
      return NextResponse.json(
        { error: 'Preview session not found or access denied' },
        { status: 404 }
      );
    }

    // Render preview
    const result = await editorIntegration.previewFieldRendering(
      sessionId,
      data,
      assetTypeId,
      renderOptions
    );

    if (!result.success) {
      return NextResponse.json(
        { 
          error: 'Preview rendering failed',
          message: result.error
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        sessionId: result.sessionId,
        groupId: result.groupId,
        renderTime: result.renderTime,
        fieldsRendered: result.fieldsRendered,
        html: result.html,
        css: renderOptions.includeCSS ? result.css : undefined,
        javascript: renderOptions.includeJS ? result.javascript : undefined,
        fields: result.fields.map(field => ({
          fieldId: field.fieldId,
          renderTime: field.renderTime,
          hasErrors: field.hasErrors,
          errors: field.hasErrors ? field.errors : undefined
        })),
        performance: {
          cacheHits: result.cacheHits,
          cacheMisses: result.cacheMisses,
          cacheHitRate: result.cacheHits / (result.cacheHits + result.cacheMisses),
          renderTime: result.renderTime
        },
        metadata: {
          previewSession: true,
          renderOptions,
          optimized: renderOptions.optimize,
          responsive: renderOptions.responsive,
          theme: renderOptions.theme
        }
      }
    });

  } catch (error) {
    console.error('Preview rendering error:', error);
    
    return NextResponse.json(
      { 
        error: 'Preview rendering failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
