import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { editorIntegration } from '@/lib/asset-modules/runtime/editor-integration';
import { auth } from '@/lib/auth';

// Request validation schema
const validatePreviewSchema = z.object({
  sessionId: z.string().min(1, 'Session ID is required'),
  data: z.record(z.any()).default({}),
  assetTypeId: z.string().min(1, 'Asset type ID is required')
});

/**
 * POST /api/asset-modules/runtime/preview/validate
 * Validate data against module in preview mode
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = validatePreviewSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { sessionId, data, assetTypeId } = validationResult.data;

    // Check if user has permission to preview modules
    if (!session.user.permissions?.includes('module.preview')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to preview modules' },
        { status: 403 }
      );
    }

    // Verify session belongs to user
    const activeSessions = editorIntegration.getActivePreviewSessions(session.user.id);
    const targetSession = activeSessions.find(s => s.sessionId === sessionId);

    if (!targetSession) {
      return NextResponse.json(
        { error: 'Preview session not found or access denied' },
        { status: 404 }
      );
    }

    // Validate preview
    const result = await editorIntegration.previewValidation(
      sessionId,
      data,
      assetTypeId
    );

    if (!result.success) {
      return NextResponse.json(
        { 
          error: 'Preview validation failed',
          message: 'Validation execution failed'
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        sessionId: result.sessionId,
        valid: result.valid,
        validationTime: result.validationTime,
        fieldsValidated: result.fieldsValidated,
        fieldResults: Object.fromEntries(
          Object.entries(result.fieldResults).map(([fieldId, fieldResult]) => [
            fieldId,
            {
              fieldId: fieldResult.fieldId,
              valid: fieldResult.valid,
              value: fieldResult.value,
              normalizedValue: fieldResult.normalizedValue,
              errors: fieldResult.errors.length > 0 ? fieldResult.errors : undefined,
              warnings: fieldResult.warnings.length > 0 ? fieldResult.warnings : undefined,
              validationTime: fieldResult.metadata.validationTime,
              rulesApplied: fieldResult.metadata.rulesApplied,
              transformationsApplied: fieldResult.metadata.transformationsApplied
            }
          ])
        ),
        errors: result.errors.length > 0 ? result.errors : undefined,
        warnings: result.warnings.length > 0 ? result.warnings : undefined,
        summary: {
          totalFields: Object.keys(result.fieldResults).length,
          validFields: Object.values(result.fieldResults).filter(r => r.valid).length,
          invalidFields: Object.values(result.fieldResults).filter(r => !r.valid).length,
          totalErrors: result.errors.length + Object.values(result.fieldResults).reduce((sum, r) => sum + r.errors.length, 0),
          totalWarnings: result.warnings.length + Object.values(result.fieldResults).reduce((sum, r) => sum + r.warnings.length, 0)
        },
        metadata: {
          previewSession: true,
          sessionId: result.sessionId
        }
      }
    });

  } catch (error) {
    console.error('Preview validation error:', error);
    
    return NextResponse.json(
      { 
        error: 'Preview validation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
