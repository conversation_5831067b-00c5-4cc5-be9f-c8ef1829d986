import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { editorIntegration } from '@/lib/asset-modules/runtime/editor-integration';
import { AssetModule } from '@/lib/types/asset-modules';
import { auth } from '@/lib/auth';

// Request validation schemas
const createPreviewSchema = z.object({
  moduleData: z.object({
    id: z.string().min(1),
    name: z.string().min(1),
    description: z.string().optional(),
    version: z.string().min(1),
    fields: z.array(z.any()),
    logic: z.object({
      nodes: z.array(z.any()),
      edges: z.array(z.any())
    }),
    rendering: z.object({}).optional(),
    validation: z.object({}).optional(),
    dependencies: z.array(z.string()).default([]),
    compatibleAssetTypes: z.array(z.string()).default([]),
    tags: z.array(z.string()).default([]),
    isActive: z.boolean().default(true),
    isPublic: z.boolean().default(false),
    isBuiltIn: z.boolean().default(false),
    requiredPermissions: z.array(z.string()).default([]),
    author: z.string().optional(),
    authorId: z.string().optional(),
    createdAt: z.string().optional(),
    updatedAt: z.string().optional()
  }),
  previewOptions: z.object({
    duration: z.number().min(60000).max(3600000).default(1800000), // 1 minute to 1 hour, default 30 minutes
    skipValidation: z.boolean().default(false)
  }).optional()
});

const executePreviewSchema = z.object({
  sessionId: z.string().min(1),
  inputData: z.record(z.any()).default({}),
  assetTypeId: z.string().min(1)
});

const renderPreviewSchema = z.object({
  sessionId: z.string().min(1),
  data: z.record(z.any()).default({}),
  assetTypeId: z.string().min(1),
  renderOptions: z.object({
    includeCSS: z.boolean().default(true),
    includeJS: z.boolean().default(true),
    theme: z.string().optional(),
    responsive: z.boolean().default(true)
  }).optional()
});

const validatePreviewSchema = z.object({
  sessionId: z.string().min(1),
  data: z.record(z.any()).default({}),
  assetTypeId: z.string().min(1)
});

/**
 * POST /api/asset-modules/runtime/preview
 * Create a new preview session
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = createPreviewSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { moduleData, previewOptions = {} } = validationResult.data;

    // Check if user has permission to preview modules
    if (!session.user.permissions?.includes('module.preview')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to preview modules' },
        { status: 403 }
      );
    }

    // Prepare module data for preview
    const moduleForPreview: AssetModule = {
      ...moduleData,
      authorId: moduleData.authorId || session.user.id,
      author: moduleData.author || session.user.name || 'Unknown',
      updatedAt: new Date().toISOString()
    };

    // Create preview session
    const previewResult = await editorIntegration.createPreviewSession(
      moduleForPreview,
      session.user.id,
      previewOptions
    );

    if (!previewResult.success) {
      return NextResponse.json(
        { 
          error: 'Failed to create preview session',
          message: previewResult.error
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        sessionId: previewResult.sessionId,
        moduleId: previewResult.moduleId,
        originalModuleId: previewResult.originalModuleId,
        expiresAt: previewResult.expiresAt,
        previewTime: previewResult.previewTime,
        duration: previewOptions.duration || 1800000
      }
    });

  } catch (error) {
    console.error('Preview session creation error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to create preview session',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/asset-modules/runtime/preview
 * Get active preview sessions for the user
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get active preview sessions
    const activeSessions = editorIntegration.getActivePreviewSessions(session.user.id);

    return NextResponse.json({
      success: true,
      data: {
        sessions: activeSessions.map(session => ({
          sessionId: session.sessionId,
          moduleId: session.moduleId,
          originalModuleId: session.originalModuleId,
          createdAt: session.createdAt,
          expiresAt: session.expiresAt,
          executionCount: session.executionCount,
          lastUsed: session.lastUsed,
          isActive: session.isActive
        })),
        totalSessions: activeSessions.length
      }
    });

  } catch (error) {
    console.error('Error fetching preview sessions:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch preview sessions',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/asset-modules/runtime/preview
 * End a preview session
 */
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Verify session belongs to user
    const activeSessions = editorIntegration.getActivePreviewSessions(session.user.id);
    const targetSession = activeSessions.find(s => s.sessionId === sessionId);

    if (!targetSession) {
      return NextResponse.json(
        { error: 'Preview session not found or access denied' },
        { status: 404 }
      );
    }

    // End preview session
    const success = await editorIntegration.endPreviewSession(sessionId);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to end preview session' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        sessionId,
        message: 'Preview session ended successfully'
      }
    });

  } catch (error) {
    console.error('Error ending preview session:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to end preview session',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
