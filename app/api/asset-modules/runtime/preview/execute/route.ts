import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { editorIntegration } from '@/lib/asset-modules/runtime/editor-integration';
import { auth } from '@/lib/auth';

// Request validation schema
const executePreviewSchema = z.object({
  sessionId: z.string().min(1, 'Session ID is required'),
  inputData: z.record(z.any()).default({}),
  assetTypeId: z.string().min(1, 'Asset type ID is required')
});

/**
 * POST /api/asset-modules/runtime/preview/execute
 * Execute a module in preview mode
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = executePreviewSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { sessionId, inputData, assetTypeId } = validationResult.data;

    // Check if user has permission to preview modules
    if (!session.user.permissions?.includes('module.preview')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to preview modules' },
        { status: 403 }
      );
    }

    // Verify session belongs to user
    const activeSessions = editorIntegration.getActivePreviewSessions(session.user.id);
    const targetSession = activeSessions.find(s => s.sessionId === sessionId);

    if (!targetSession) {
      return NextResponse.json(
        { error: 'Preview session not found or access denied' },
        { status: 404 }
      );
    }

    // Execute preview
    const result = await editorIntegration.executePreview(
      sessionId,
      inputData,
      assetTypeId
    );

    return NextResponse.json({
      success: result.success,
      data: {
        sessionId: result.sessionId,
        executionId: result.executionId,
        executionTime: result.executionTime,
        outputs: result.outputs,
        renderResults: Object.keys(result.renderResults).length > 0 ? result.renderResults : undefined,
        validationResult: result.validationResult ? {
          valid: result.validationResult.valid,
          fieldsValidated: result.validationResult.metadata.fieldsValidated,
          rulesExecuted: result.validationResult.metadata.rulesExecuted,
          validationTime: result.validationResult.metadata.totalValidationTime,
          errors: result.validationResult.errors.length > 0 ? result.validationResult.errors : undefined,
          warnings: result.validationResult.warnings.length > 0 ? result.validationResult.warnings : undefined
        } : undefined,
        logicResult: result.logicResult ? {
          success: result.logicResult.success,
          executionTime: result.logicResult.executionTime,
          nodesExecuted: result.logicResult.nodesExecuted,
          outputs: result.logicResult.outputs,
          errors: result.logicResult.errors.length > 0 ? result.logicResult.errors : undefined,
          warnings: result.logicResult.warnings.length > 0 ? result.logicResult.warnings : undefined
        } : undefined,
        errors: result.errors.length > 0 ? result.errors : undefined,
        warnings: result.warnings.length > 0 ? result.warnings : undefined,
        metadata: {
          previewSession: true,
          executionCount: result.metadata.executionCount,
          sessionId: result.sessionId
        }
      }
    });

  } catch (error) {
    console.error('Preview execution error:', error);
    
    return NextResponse.json(
      { 
        error: 'Preview execution failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
