import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";
import { maintenanceService } from "@/lib/services/maintenance-service";
import prisma from "@/lib/prisma";
import { z } from "zod";
import { 
  startOfDay, 
  endOfDay, 
  addDays, 
  addWeeks, 
  addMonths,
  format,
  parseISO,
  isWithinInterval,
} from "date-fns";

// Schema for scheduling requests
const ScheduleRequestSchema = z.object({
  taskData: z.object({
    title: z.string(),
    description: z.string().optional(),
    assetId: z.string().cuid(),
    type: z.string(),
    priority: z.string(),
    estimatedDuration: z.number().positive(),
    assignedTo: z.string().optional(),
    assignedTeam: z.string().optional(),
    preferredDate: z.string().datetime().optional(),
    constraints: z.object({
      workingHoursOnly: z.boolean().default(true),
      excludeWeekends: z.boolean().default(true),
      excludeHolidays: z.boolean().default(true),
      bufferMinutes: z.number().default(30),
      maxDaysAhead: z.number().default(30),
    }).optional(),
  }),
  options: z.object({
    autoSchedule: z.boolean().default(true),
    checkConflicts: z.boolean().default(true),
    optimizeResources: z.boolean().default(true),
  }).optional(),
});

const BulkScheduleSchema = z.object({
  tasks: z.array(ScheduleRequestSchema.shape.taskData),
  globalConstraints: ScheduleRequestSchema.shape.taskData.shape.constraints.optional(),
  optimizationGoals: z.array(z.enum(["minimize_conflicts", "balance_workload", "minimize_cost", "minimize_time"])).default(["minimize_conflicts"]),
});

// GET /api/maintenance/schedule - Get scheduling information and availability
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get("startDate") ? parseISO(searchParams.get("startDate")!) : new Date();
    const endDate = searchParams.get("endDate") ? parseISO(searchParams.get("endDate")!) : addDays(new Date(), 30);
    const resourceId = searchParams.get("resourceId");
    const includeAvailability = searchParams.get("includeAvailability") === "true";

    // Get scheduled tasks for the period
    const scheduledTasks = await prisma.maintenanceTask.findMany({
      where: {
        scheduledDate: {
          gte: startDate,
          lte: endDate,
        },
        status: {
          in: ["scheduled", "in_progress"],
        },
        ...(resourceId && { assignedTo: resourceId }),
      },
      include: {
        asset: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
      },
      orderBy: {
        scheduledDate: "asc",
      },
    });

    // Calculate availability slots if requested
    let availability = null;
    if (includeAvailability) {
      availability = await calculateAvailability(startDate, endDate, resourceId);
    }

    // Get resource utilization
    const utilization = await calculateResourceUtilization(startDate, endDate, resourceId);

    // Detect conflicts
    const conflicts = await detectSchedulingConflicts(scheduledTasks);

    return NextResponse.json({
      success: true,
      data: {
        scheduledTasks,
        availability,
        utilization,
        conflicts,
        period: {
          startDate,
          endDate,
        },
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error getting schedule information:", error);
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// POST /api/maintenance/schedule - Schedule a new maintenance task with optimization
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = ScheduleRequestSchema.parse(body);
    const { taskData, options = {} } = validatedData;

    // Use maintenance service for intelligent scheduling
    const schedulingResult = await maintenanceService.scheduleMaintenanceTask(
      taskData,
      {
        considerWorkingHours: taskData.constraints?.workingHoursOnly ?? true,
        excludeWeekends: taskData.constraints?.excludeWeekends ?? true,
        excludeHolidays: taskData.constraints?.excludeHolidays ?? true,
        bufferMinutes: taskData.constraints?.bufferMinutes ?? 30,
      }
    );

    // Create the maintenance task with optimized scheduling
    const newTask = await prisma.maintenanceTask.create({
      data: {
        ...taskData,
        scheduledDate: schedulingResult.scheduledDate,
        dueDate: schedulingResult.dueDate,
        status: "scheduled",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      include: {
        asset: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
      },
    });

    // Create calendar event
    await prisma.maintenanceCalendarEvent.create({
      data: {
        taskId: newTask.id,
        title: newTask.title,
        description: newTask.description,
        startDate: schedulingResult.scheduledDate,
        endDate: schedulingResult.dueDate,
        assignedTo: newTask.assignedTo,
        status: "scheduled",
        priority: newTask.priority,
        createdBy: session.user.id,
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        task: newTask,
        scheduling: {
          originalRequest: taskData.preferredDate ? parseISO(taskData.preferredDate) : null,
          scheduledDate: schedulingResult.scheduledDate,
          dueDate: schedulingResult.dueDate,
          conflicts: schedulingResult.conflicts,
          optimized: schedulingResult.scheduledDate !== (taskData.preferredDate ? parseISO(taskData.preferredDate) : null),
        },
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error scheduling maintenance task:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: "Invalid request data",
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// PUT /api/maintenance/schedule - Reschedule or optimize existing tasks
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { taskId, newSchedule, optimize = false } = body;

    if (!taskId) {
      return NextResponse.json(
        { success: false, error: "Task ID is required" },
        { status: 400 }
      );
    }

    // Get existing task
    const existingTask = await prisma.maintenanceTask.findUnique({
      where: { id: taskId },
    });

    if (!existingTask) {
      return NextResponse.json(
        { success: false, error: "Task not found" },
        { status: 404 }
      );
    }

    let scheduledDate = newSchedule.scheduledDate ? parseISO(newSchedule.scheduledDate) : existingTask.scheduledDate;
    let dueDate = newSchedule.dueDate ? parseISO(newSchedule.dueDate) : existingTask.dueDate;

    // Optimize scheduling if requested
    if (optimize) {
      const schedulingResult = await maintenanceService.scheduleMaintenanceTask({
        ...existingTask,
        scheduledDate,
      });
      
      scheduledDate = schedulingResult.scheduledDate;
      dueDate = schedulingResult.dueDate;
    }

    // Update the task
    const updatedTask = await prisma.maintenanceTask.update({
      where: { id: taskId },
      data: {
        scheduledDate,
        dueDate,
        updatedAt: new Date(),
      },
      include: {
        asset: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
      },
    });

    // Update calendar event
    await prisma.maintenanceCalendarEvent.updateMany({
      where: { taskId },
      data: {
        startDate: scheduledDate,
        endDate: dueDate,
        updatedAt: new Date(),
      },
    });

    // Log the reschedule action
    await prisma.maintenanceWorkLog.create({
      data: {
        taskId,
        userId: session.user.id,
        userName: session.user.name || session.user.email || "Unknown",
        action: "updated",
        description: `Task rescheduled from ${format(existingTask.scheduledDate, "MMM dd, yyyy HH:mm")} to ${format(scheduledDate, "MMM dd, yyyy HH:mm")}`,
        timestamp: new Date(),
        metadata: JSON.stringify({ 
          rescheduled: true, 
          optimized: optimize,
          originalDate: existingTask.scheduledDate,
          newDate: scheduledDate,
        }),
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        task: updatedTask,
        rescheduling: {
          originalDate: existingTask.scheduledDate,
          newDate: scheduledDate,
          optimized: optimize,
        },
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error rescheduling maintenance task:", error);
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Helper functions
async function calculateAvailability(startDate: Date, endDate: Date, resourceId?: string) {
  // This would calculate available time slots based on working hours,
  // existing bookings, and resource constraints
  const availability = [];
  let current = startOfDay(startDate);
  
  while (current <= endDate) {
    const dayStart = new Date(current);
    dayStart.setHours(9, 0, 0, 0); // 9 AM
    const dayEnd = new Date(current);
    dayEnd.setHours(17, 0, 0, 0); // 5 PM
    
    // Get existing bookings for this day
    const existingTasks = await prisma.maintenanceTask.findMany({
      where: {
        scheduledDate: {
          gte: dayStart,
          lte: dayEnd,
        },
        status: {
          in: ["scheduled", "in_progress"],
        },
        ...(resourceId && { assignedTo: resourceId }),
      },
    });
    
    // Calculate available slots (simplified)
    const totalMinutes = 8 * 60; // 8 hours
    const bookedMinutes = existingTasks.reduce((sum, task) => 
      sum + (task.estimatedDuration || 60), 0
    );
    
    availability.push({
      date: current,
      totalCapacity: totalMinutes,
      bookedCapacity: bookedMinutes,
      availableCapacity: totalMinutes - bookedMinutes,
      utilization: bookedMinutes / totalMinutes,
      available: bookedMinutes < totalMinutes,
    });
    
    current = addDays(current, 1);
  }
  
  return availability;
}

async function calculateResourceUtilization(startDate: Date, endDate: Date, resourceId?: string) {
  const tasks = await prisma.maintenanceTask.findMany({
    where: {
      scheduledDate: {
        gte: startDate,
        lte: endDate,
      },
      ...(resourceId && { assignedTo: resourceId }),
    },
  });
  
  const totalDuration = tasks.reduce((sum, task) => sum + (task.estimatedDuration || 60), 0);
  const workingDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const totalCapacity = workingDays * 8 * 60; // 8 hours per day in minutes
  
  return {
    totalTasks: tasks.length,
    totalDuration,
    totalCapacity,
    utilization: totalCapacity > 0 ? totalDuration / totalCapacity : 0,
    averageTaskDuration: tasks.length > 0 ? totalDuration / tasks.length : 0,
  };
}

async function detectSchedulingConflicts(tasks: any[]) {
  const conflicts = [];
  
  for (let i = 0; i < tasks.length; i++) {
    for (let j = i + 1; j < tasks.length; j++) {
      const task1 = tasks[i];
      const task2 = tasks[j];
      
      // Check for same assignee and overlapping time
      if (task1.assignedTo === task2.assignedTo && task1.assignedTo) {
        const overlap = isWithinInterval(task1.scheduledDate, {
          start: task2.scheduledDate,
          end: task2.dueDate,
        }) || isWithinInterval(task2.scheduledDate, {
          start: task1.scheduledDate,
          end: task1.dueDate,
        });
        
        if (overlap) {
          conflicts.push({
            type: "resource_conflict",
            tasks: [task1.id, task2.id],
            resource: task1.assignedTo,
            severity: "high",
          });
        }
      }
    }
  }
  
  return conflicts;
}
