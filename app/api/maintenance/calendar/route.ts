import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";
import { 
  MaintenanceCalendarEventCreateSchema,
  MaintenanceCalendarEventUpdateSchema 
} from "@/lib/schemas/maintenance";
import { z } from "zod";
import prisma from "@/lib/prisma";

// GET /api/maintenance/calendar - Get maintenance calendar events
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parse date range parameters
    const startDate = searchParams.get("startDate") ? new Date(searchParams.get("startDate")!) : new Date();
    const endDate = searchParams.get("endDate") ? new Date(searchParams.get("endDate")!) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
    
    // Parse filter parameters
    const assetId = searchParams.get("assetId");
    const assetTypeId = searchParams.get("assetTypeId");
    const assignedTo = searchParams.get("assignedTo");
    const status = searchParams.get("status")?.split(",");
    const category = searchParams.get("category");

    // Build where clause
    const where: any = {
      startDate: { gte: startDate },
      endDate: { lte: endDate },
    };

    if (assetId) {
      where.assetId = assetId;
    }

    if (assignedTo) {
      where.assignedTo = assignedTo;
    }

    if (status && status.length > 0) {
      where.status = { in: status };
    }

    if (category) {
      where.category = category;
    }

    // If filtering by asset type, need to join through asset
    if (assetTypeId) {
      where.asset = {
        assetTypeId,
      };
    }

    // Get calendar events
    const events = await prisma.maintenanceCalendarEvent.findMany({
      where,
      include: {
        task: {
          select: {
            id: true,
            title: true,
            type: true,
            priority: true,
            status: true,
            estimatedDuration: true,
            estimatedCost: true,
          },
        },
        asset: {
          select: {
            id: true,
            name: true,
            category: true,
            location: true,
            assetType: {
              select: {
                id: true,
                name: true,
                code: true,
                icon: true,
                color: true,
              },
            },
          },
        },
      },
      orderBy: {
        startDate: "asc",
      },
    });

    // Also get maintenance tasks that don't have calendar events
    const tasksWithoutEvents = await prisma.maintenanceTask.findMany({
      where: {
        scheduledDate: { gte: startDate, lte: endDate },
        calendarEvents: { none: {} },
        ...(assetId && { assetId }),
        ...(assignedTo && { assignedTo }),
        ...(status && status.length > 0 && { status: { in: status } }),
        ...(assetTypeId && { asset: { assetTypeId } }),
      },
      include: {
        asset: {
          select: {
            id: true,
            name: true,
            category: true,
            location: true,
            assetType: {
              select: {
                id: true,
                name: true,
                code: true,
                icon: true,
                color: true,
              },
            },
          },
        },
      },
    });

    // Convert tasks to calendar event format
    const taskEvents = tasksWithoutEvents.map(task => ({
      id: `task-${task.id}`,
      taskId: task.id,
      assetId: task.assetId,
      title: task.title,
      description: task.description,
      startDate: task.scheduledDate,
      endDate: task.dueDate,
      allDay: false,
      recurrenceRule: null,
      color: task.asset?.assetType?.color || "#3b82f6",
      category: "maintenance_task",
      location: task.asset?.location,
      assignedTo: task.assignedTo,
      status: task.status,
      isBlocked: false,
      priority: task.priority,
      tags: task.tags || [],
      createdBy: "system",
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
      task: {
        id: task.id,
        title: task.title,
        type: task.type,
        priority: task.priority,
        status: task.status,
        estimatedDuration: task.estimatedDuration,
        estimatedCost: task.estimatedCost,
      },
      asset: task.asset,
    }));

    // Combine events and task events
    const allEvents = [...events, ...taskEvents];

    return NextResponse.json({
      success: true,
      data: {
        events: allEvents,
        dateRange: {
          startDate,
          endDate,
        },
        summary: {
          totalEvents: allEvents.length,
          taskEvents: taskEvents.length,
          calendarEvents: events.length,
          statusBreakdown: allEvents.reduce((acc, event) => {
            acc[event.status] = (acc[event.status] || 0) + 1;
            return acc;
          }, {} as Record<string, number>),
        },
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error getting maintenance calendar:", error);
    
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// POST /api/maintenance/calendar - Create maintenance calendar event
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate request data
    const validatedData = MaintenanceCalendarEventCreateSchema.parse({
      ...body,
      createdBy: session.user.id,
    });

    // Check for scheduling conflicts if isBlocked is true
    if (validatedData.isBlocked) {
      const conflicts = await prisma.maintenanceCalendarEvent.findMany({
        where: {
          OR: [
            {
              startDate: { lte: validatedData.endDate },
              endDate: { gte: validatedData.startDate },
            },
          ],
          isBlocked: true,
          ...(validatedData.assignedTo && { assignedTo: validatedData.assignedTo }),
        },
      });

      if (conflicts.length > 0) {
        return NextResponse.json(
          { 
            success: false,
            error: "Scheduling conflict detected",
            details: { conflicts },
          },
          { status: 409 }
        );
      }
    }

    // Create the calendar event
    const event = await prisma.maintenanceCalendarEvent.create({
      data: validatedData,
      include: {
        task: {
          select: {
            id: true,
            title: true,
            type: true,
            priority: true,
            status: true,
          },
        },
        asset: {
          select: {
            id: true,
            name: true,
            category: true,
            location: true,
            assetType: {
              select: {
                id: true,
                name: true,
                code: true,
                icon: true,
                color: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: event,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error creating maintenance calendar event:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: "Invalid request data",
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// PUT /api/maintenance/calendar - Update maintenance calendar event
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: "Event ID is required" },
        { status: 400 }
      );
    }

    // Validate update data
    const validatedData = MaintenanceCalendarEventUpdateSchema.parse(updateData);

    // Check if event exists
    const existingEvent = await prisma.maintenanceCalendarEvent.findUnique({
      where: { id },
    });

    if (!existingEvent) {
      return NextResponse.json(
        { success: false, error: "Calendar event not found" },
        { status: 404 }
      );
    }

    // Update the event
    const updatedEvent = await prisma.maintenanceCalendarEvent.update({
      where: { id },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
      include: {
        task: {
          select: {
            id: true,
            title: true,
            type: true,
            priority: true,
            status: true,
          },
        },
        asset: {
          select: {
            id: true,
            name: true,
            category: true,
            location: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedEvent,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error updating maintenance calendar event:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: "Invalid request data",
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
