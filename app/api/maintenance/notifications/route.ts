import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";
import { 
  MaintenanceNotificationCreateSchema,
  MaintenanceNotificationUpdateSchema 
} from "@/lib/schemas/maintenance";
import { z } from "zod";
import prisma from "@/lib/prisma";

// GET /api/maintenance/notifications - Get maintenance notifications
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parse pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = (page - 1) * limit;

    // Parse filter parameters
    const recipientId = searchParams.get("recipientId") || session.user.id;
    const status = searchParams.get("status")?.split(",");
    const type = searchParams.get("type")?.split(",");
    const unreadOnly = searchParams.get("unreadOnly") === "true";

    // Build where clause
    const where: any = {
      recipientId,
    };

    if (status && status.length > 0) {
      where.status = { in: status };
    }

    if (type && type.length > 0) {
      where.type = { in: type };
    }

    if (unreadOnly) {
      where.readAt = null;
      where.status = { not: "dismissed" };
    }

    // Get notifications with pagination
    const [notifications, total] = await Promise.all([
      prisma.maintenanceNotification.findMany({
        where,
        include: {
          task: {
            select: {
              id: true,
              title: true,
              type: true,
              priority: true,
              status: true,
              scheduledDate: true,
              dueDate: true,
              asset: {
                select: {
                  id: true,
                  name: true,
                  location: true,
                },
              },
            },
          },
        },
        orderBy: {
          scheduledFor: "desc",
        },
        skip: offset,
        take: limit,
      }),
      prisma.maintenanceNotification.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    // Add temporal information
    const now = new Date();
    const enhancedNotifications = notifications.map(notification => ({
      ...notification,
      isOverdue: notification.scheduledFor < now && notification.status === "pending",
      timeUntilScheduled: notification.scheduledFor.getTime() - now.getTime(),
    }));

    return NextResponse.json({
      success: true,
      data: {
        notifications: enhancedNotifications,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
        summary: {
          unreadCount: notifications.filter(n => !n.readAt && n.status !== "dismissed").length,
          pendingCount: notifications.filter(n => n.status === "pending").length,
          typeBreakdown: notifications.reduce((acc, notification) => {
            acc[notification.type] = (acc[notification.type] || 0) + 1;
            return acc;
          }, {} as Record<string, number>),
        },
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error getting maintenance notifications:", error);
    
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// POST /api/maintenance/notifications - Create maintenance notification
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate request data
    const validatedData = MaintenanceNotificationCreateSchema.parse(body);

    // Check if task exists
    const task = await prisma.maintenanceTask.findUnique({
      where: { id: validatedData.taskId },
      select: {
        id: true,
        title: true,
        status: true,
        scheduledDate: true,
        dueDate: true,
      },
    });

    if (!task) {
      return NextResponse.json(
        { success: false, error: "Task not found" },
        { status: 404 }
      );
    }

    // Create the notification
    const notification = await prisma.maintenanceNotification.create({
      data: validatedData,
      include: {
        task: {
          select: {
            id: true,
            title: true,
            type: true,
            priority: true,
            status: true,
            scheduledDate: true,
            dueDate: true,
            asset: {
              select: {
                id: true,
                name: true,
                location: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: notification,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error creating maintenance notification:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: "Invalid request data",
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// PUT /api/maintenance/notifications - Update notification status (mark as read, dismiss, etc.)
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { id, ids, action, ...updateData } = body;

    // Handle bulk operations
    if (ids && Array.isArray(ids)) {
      const validatedIds = z.array(z.string().cuid()).parse(ids);
      
      let updateFields: any = { updatedAt: new Date() };
      
      switch (action) {
        case "mark_read":
          updateFields.readAt = new Date();
          updateFields.status = "read";
          break;
        case "dismiss":
          updateFields.dismissedAt = new Date();
          updateFields.status = "dismissed";
          break;
        case "mark_unread":
          updateFields.readAt = null;
          updateFields.status = "sent";
          break;
        default:
          return NextResponse.json(
            { success: false, error: "Invalid action" },
            { status: 400 }
          );
      }

      const result = await prisma.maintenanceNotification.updateMany({
        where: {
          id: { in: validatedIds },
          recipientId: session.user.id, // Ensure user can only update their own notifications
        },
        data: updateFields,
      });

      return NextResponse.json({
        success: true,
        data: {
          updatedCount: result.count,
          action,
        },
        timestamp: new Date().toISOString(),
      });
    }

    // Handle single notification update
    if (!id) {
      return NextResponse.json(
        { success: false, error: "Notification ID is required" },
        { status: 400 }
      );
    }

    // Validate update data
    const validatedData = MaintenanceNotificationUpdateSchema.parse(updateData);

    // Check if notification exists and belongs to user
    const existingNotification = await prisma.maintenanceNotification.findFirst({
      where: {
        id,
        recipientId: session.user.id,
      },
    });

    if (!existingNotification) {
      return NextResponse.json(
        { success: false, error: "Notification not found" },
        { status: 404 }
      );
    }

    // Update the notification
    const updatedNotification = await prisma.maintenanceNotification.update({
      where: { id },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
      include: {
        task: {
          select: {
            id: true,
            title: true,
            type: true,
            priority: true,
            status: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedNotification,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error updating maintenance notification:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: "Invalid request data",
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
