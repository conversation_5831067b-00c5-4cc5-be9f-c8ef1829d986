import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";
import { BulkMaintenanceTaskUpdateSchema } from "@/lib/schemas/maintenance";
import { z } from "zod";
import prisma from "@/lib/prisma";

// PUT /api/maintenance/tasks/bulk - Bulk update maintenance tasks
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate request body
    const validatedData = BulkMaintenanceTaskUpdateSchema.parse(body);
    const { taskIds, updates } = validatedData;

    // Check if all tasks exist and user has permission to update them
    const existingTasks = await prisma.maintenanceTask.findMany({
      where: {
        id: { in: taskIds },
      },
      select: {
        id: true,
        status: true,
        assignedTo: true,
      },
    });

    if (existingTasks.length !== taskIds.length) {
      const foundIds = existingTasks.map(task => task.id);
      const missingIds = taskIds.filter(id => !foundIds.includes(id));
      
      return NextResponse.json(
        { 
          success: false,
          error: "Some tasks not found",
          details: { missingIds },
        },
        { status: 404 }
      );
    }

    // Perform bulk update
    const updateData: any = {
      ...updates,
      updatedAt: new Date(),
    };

    // Convert date strings to Date objects if present
    if (updates.scheduledDate) {
      updateData.scheduledDate = new Date(updates.scheduledDate);
    }
    if (updates.dueDate) {
      updateData.dueDate = new Date(updates.dueDate);
    }

    const result = await prisma.maintenanceTask.updateMany({
      where: {
        id: { in: taskIds },
      },
      data: updateData,
    });

    // Log the bulk update action
    await prisma.maintenanceWorkLog.createMany({
      data: taskIds.map(taskId => ({
        taskId,
        userId: session.user.id,
        userName: session.user.name || session.user.email || "Unknown",
        action: "updated",
        description: `Bulk update: ${Object.keys(updates).join(", ")}`,
        timestamp: new Date(),
        metadata: JSON.stringify({ bulkUpdate: true, updates }),
      })),
    });

    return NextResponse.json({
      success: true,
      data: {
        updatedCount: result.count,
        taskIds,
        updates,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error in bulk update maintenance tasks:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: "Invalid request data",
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// DELETE /api/maintenance/tasks/bulk - Bulk delete maintenance tasks
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const taskIdsParam = searchParams.get("taskIds");
    
    if (!taskIdsParam) {
      return NextResponse.json(
        { success: false, error: "Task IDs are required" },
        { status: 400 }
      );
    }

    const taskIds = taskIdsParam.split(",");

    // Validate task IDs
    const taskIdsSchema = z.array(z.string().cuid()).min(1);
    const validatedTaskIds = taskIdsSchema.parse(taskIds);

    // Check if tasks exist and can be deleted
    const existingTasks = await prisma.maintenanceTask.findMany({
      where: {
        id: { in: validatedTaskIds },
      },
      select: {
        id: true,
        status: true,
        title: true,
      },
    });

    if (existingTasks.length !== validatedTaskIds.length) {
      const foundIds = existingTasks.map(task => task.id);
      const missingIds = validatedTaskIds.filter(id => !foundIds.includes(id));
      
      return NextResponse.json(
        { 
          success: false,
          error: "Some tasks not found",
          details: { missingIds },
        },
        { status: 404 }
      );
    }

    // Check if any tasks are in progress (shouldn't be deleted)
    const inProgressTasks = existingTasks.filter(task => task.status === "in_progress");
    if (inProgressTasks.length > 0) {
      return NextResponse.json(
        { 
          success: false,
          error: "Cannot delete tasks that are in progress",
          details: { inProgressTasks: inProgressTasks.map(t => ({ id: t.id, title: t.title })) },
        },
        { status: 400 }
      );
    }

    // Delete related records first (work logs, notifications)
    await prisma.maintenanceWorkLog.deleteMany({
      where: { taskId: { in: validatedTaskIds } },
    });

    await prisma.maintenanceNotification.deleteMany({
      where: { taskId: { in: validatedTaskIds } },
    });

    await prisma.maintenanceCalendarEvent.deleteMany({
      where: { taskId: { in: validatedTaskIds } },
    });

    // Delete the tasks
    const result = await prisma.maintenanceTask.deleteMany({
      where: {
        id: { in: validatedTaskIds },
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        deletedCount: result.count,
        taskIds: validatedTaskIds,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error in bulk delete maintenance tasks:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: "Invalid task IDs",
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
