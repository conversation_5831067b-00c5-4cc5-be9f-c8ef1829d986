import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";
import { 
  MaintenanceTaskCreateSchema,
  MaintenanceTaskUpdateSchema 
} from "@/lib/schemas/maintenance";
import { z } from "zod";
import prisma from "@/lib/prisma";

// GET /api/maintenance/tasks/[id] - Get specific maintenance task
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    // Validate task ID
    const taskIdSchema = z.string().cuid();
    const validatedId = taskIdSchema.parse(id);

    const task = await prisma.maintenanceTask.findUnique({
      where: { id: validatedId },
      include: {
        asset: {
          include: {
            assetType: {
              select: {
                id: true,
                name: true,
                code: true,
                icon: true,
                color: true,
              },
            },
          },
        },
        schedule: {
          select: {
            id: true,
            name: true,
            type: true,
            frequency: true,
            priority: true,
          },
        },
        parentTask: {
          select: {
            id: true,
            title: true,
            status: true,
          },
        },
        subTasks: {
          select: {
            id: true,
            title: true,
            status: true,
            priority: true,
            scheduledDate: true,
            dueDate: true,
          },
        },
        workLogs: {
          orderBy: { timestamp: "desc" },
          take: 10,
        },
        notifications: {
          where: { status: { not: "dismissed" } },
          orderBy: { scheduledFor: "asc" },
        },
        calendarEvents: {
          select: {
            id: true,
            title: true,
            startDate: true,
            endDate: true,
            status: true,
          },
        },
      },
    });

    if (!task) {
      return NextResponse.json(
        { success: false, error: "Task not found" },
        { status: 404 }
      );
    }

    // Add temporal calculations
    const now = new Date();
    const isOverdue = task.status !== "completed" && task.dueDate < now;
    const isDueSoon = task.status === "scheduled" && 
      task.scheduledDate > now && 
      task.scheduledDate <= new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    const daysUntilDue = Math.ceil((task.dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    const daysUntilScheduled = Math.ceil((task.scheduledDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    const enhancedTask = {
      ...task,
      isOverdue,
      isDueSoon,
      daysUntilDue,
      daysUntilScheduled,
      completionPercentage: task.status === "in_progress" && task.estimatedDuration && task.actualDuration
        ? Math.min(100, (task.actualDuration / task.estimatedDuration) * 100)
        : task.status === "completed" ? 100 : 0,
    };

    return NextResponse.json({
      success: true,
      data: enhancedTask,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error getting maintenance task:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: "Invalid task ID",
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// PUT /api/maintenance/tasks/[id] - Update specific maintenance task
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;
    const body = await request.json();

    // Validate task ID
    const taskIdSchema = z.string().cuid();
    const validatedId = taskIdSchema.parse(id);

    // Validate update data
    const validatedData = MaintenanceTaskUpdateSchema.parse(body);

    // Check if task exists
    const existingTask = await prisma.maintenanceTask.findUnique({
      where: { id: validatedId },
      select: {
        id: true,
        status: true,
        assignedTo: true,
        title: true,
      },
    });

    if (!existingTask) {
      return NextResponse.json(
        { success: false, error: "Task not found" },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      ...validatedData,
      updatedAt: new Date(),
    };

    // Handle status transitions
    if (validatedData.status && validatedData.status !== existingTask.status) {
      if (validatedData.status === "completed" && !validatedData.completedDate) {
        updateData.completedDate = new Date();
        updateData.completedBy = session.user.id;
      }
      
      if (validatedData.status === "in_progress" && existingTask.status === "scheduled") {
        // Log task start
        await prisma.maintenanceWorkLog.create({
          data: {
            taskId: validatedId,
            userId: session.user.id,
            userName: session.user.name || session.user.email || "Unknown",
            action: "started",
            description: "Task started",
            timestamp: new Date(),
          },
        });
      }
    }

    // Update the task
    const updatedTask = await prisma.maintenanceTask.update({
      where: { id: validatedId },
      data: updateData,
      include: {
        asset: {
          select: {
            id: true,
            name: true,
            category: true,
            location: true,
          },
        },
      },
    });

    // Log the update
    await prisma.maintenanceWorkLog.create({
      data: {
        taskId: validatedId,
        userId: session.user.id,
        userName: session.user.name || session.user.email || "Unknown",
        action: "updated",
        description: `Task updated: ${Object.keys(validatedData).join(", ")}`,
        timestamp: new Date(),
        metadata: JSON.stringify({ updates: validatedData }),
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedTask,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error updating maintenance task:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: "Invalid request data",
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// DELETE /api/maintenance/tasks/[id] - Delete specific maintenance task
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    // Validate task ID
    const taskIdSchema = z.string().cuid();
    const validatedId = taskIdSchema.parse(id);

    // Check if task exists and can be deleted
    const existingTask = await prisma.maintenanceTask.findUnique({
      where: { id: validatedId },
      select: {
        id: true,
        status: true,
        title: true,
      },
    });

    if (!existingTask) {
      return NextResponse.json(
        { success: false, error: "Task not found" },
        { status: 404 }
      );
    }

    // Don't allow deletion of in-progress tasks
    if (existingTask.status === "in_progress") {
      return NextResponse.json(
        { 
          success: false,
          error: "Cannot delete task that is in progress",
        },
        { status: 400 }
      );
    }

    // Delete related records first
    await prisma.maintenanceWorkLog.deleteMany({
      where: { taskId: validatedId },
    });

    await prisma.maintenanceNotification.deleteMany({
      where: { taskId: validatedId },
    });

    await prisma.maintenanceCalendarEvent.deleteMany({
      where: { taskId: validatedId },
    });

    // Delete the task
    await prisma.maintenanceTask.delete({
      where: { id: validatedId },
    });

    return NextResponse.json({
      success: true,
      data: {
        deletedTaskId: validatedId,
        title: existingTask.title,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error deleting maintenance task:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: "Invalid task ID",
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
