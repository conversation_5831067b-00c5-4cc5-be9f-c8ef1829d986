import { NextRequest, NextResponse } from "next/server";
import { MaintenanceEngine } from "@/lib/engines/maintenance-engine";
import { maintenanceService } from "@/lib/services/maintenance-service";
import prisma from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-options";
import {
  MaintenanceTaskCreateSchema,
  MaintenanceTaskUpdateSchema,
  MaintenanceTaskFilterSchema,
  BulkMaintenanceTaskUpdateSchema
} from "@/lib/schemas/maintenance";
import { z } from "zod";

// GET /api/maintenance/tasks - Get maintenance tasks with advanced filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);

    // Parse pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = (page - 1) * limit;

    // Parse filter parameters
    const filterParams = {
      status: searchParams.get("status")?.split(",") || undefined,
      priority: searchParams.get("priority")?.split(",") || undefined,
      type: searchParams.get("type")?.split(",") || undefined,
      assetId: searchParams.get("assetId") || undefined,
      assetTypeId: searchParams.get("assetTypeId") || undefined,
      assignedTo: searchParams.get("assignedTo") || undefined,
      assignedTeam: searchParams.get("assignedTeam") || undefined,
      scheduledDateFrom: searchParams.get("scheduledDateFrom") ? new Date(searchParams.get("scheduledDateFrom")!) : undefined,
      scheduledDateTo: searchParams.get("scheduledDateTo") ? new Date(searchParams.get("scheduledDateTo")!) : undefined,
      dueDateFrom: searchParams.get("dueDateFrom") ? new Date(searchParams.get("dueDateFrom")!) : undefined,
      dueDateTo: searchParams.get("dueDateTo") ? new Date(searchParams.get("dueDateTo")!) : undefined,
      tags: searchParams.get("tags")?.split(",") || undefined,
      search: searchParams.get("search") || undefined,
    };

    // Validate filter parameters
    const validatedFilter = MaintenanceTaskFilterSchema.parse(filterParams);

    // Use maintenance service for advanced filtering
    const result = await maintenanceService.getMaintenanceTasks(
      validatedFilter,
      { page, limit, offset }
    );

    // Add temporal calculations
    const now = new Date();
    const enhancedTasks = result.tasks.map(task => {
      const isOverdue = task.status !== "completed" && task.dueDate < now;
      const isDueSoon = task.status === "scheduled" &&
        task.scheduledDate > now &&
        task.scheduledDate <= new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // Next 7 days

      const daysUntilDue = Math.ceil((task.dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      const daysUntilScheduled = Math.ceil((task.scheduledDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      return {
        ...task,
        isOverdue,
        isDueSoon,
        daysUntilDue,
        daysUntilScheduled,
        // Calculate completion percentage for in-progress tasks
        completionPercentage: task.status === "in_progress" && task.estimatedDuration && task.actualDuration
          ? Math.min(100, (task.actualDuration / task.estimatedDuration) * 100)
          : task.status === "completed" ? 100 : 0,
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        tasks: enhancedTasks,
        pagination: {
          page,
          limit,
          total: result.total,
          totalPages: result.totalPages,
          hasNext: page < result.totalPages,
          hasPrev: page > 1,
        },
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error getting maintenance tasks:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid filter parameters",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// POST /api/maintenance/tasks - Generate maintenance tasks
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { assetId, assetTypeId, scheduleId } = body;
    
    if (!assetId && !assetTypeId) {
      return NextResponse.json(
        { error: "Either assetId or assetTypeId is required" },
        { status: 400 }
      );
    }
    
    let result;
    
    if (assetTypeId) {
      // Generate tasks for all assets of this type
      result = await MaintenanceEngine.generateTasksForAssetType(assetTypeId);
    } else if (assetId && scheduleId) {
      // Generate tasks for specific asset and schedule
      const asset = await prisma.asset.findUnique({
        where: { id: assetId },
        include: {
          assetType: {
            include: {
              maintenanceSchedules: true,
            },
          },
        },
      });
      
      if (!asset) {
        return NextResponse.json(
          { error: "Asset not found" },
          { status: 404 }
        );
      }
      
      const schedule = asset.assetType?.maintenanceSchedules.find(s => s.id === scheduleId);
      
      if (!schedule) {
        return NextResponse.json(
          { error: "Maintenance schedule not found" },
          { status: 404 }
        );
      }
      
      result = await MaintenanceEngine.generateMaintenanceTasks({
        assetId,
        scheduleId,
        schedule,
      });
    } else {
      return NextResponse.json(
        { error: "Invalid parameters" },
        { status: 400 }
      );
    }
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error("Error generating maintenance tasks:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/maintenance/tasks - Complete maintenance task
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { taskId, completedBy, completionNotes, actualDuration, actualCost, checklistResults } = body;
    
    if (!taskId || !completedBy) {
      return NextResponse.json(
        { error: "Task ID and completed by are required" },
        { status: 400 }
      );
    }
    
    await MaintenanceEngine.completeMaintenanceTask(taskId, {
      completedBy,
      completionNotes,
      actualDuration,
      actualCost,
      checklistResults,
    });
    
    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error("Error completing maintenance task:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}